<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // تسجيل middleware aliases
        $middleware->alias([
            'security.headers' => \App\Http\Middleware\SecurityHeaders::class,
            'rate.limit' => \App\Http\Middleware\RateLimiting::class,
            'account.security' => \App\Http\Middleware\CheckAccountSecurity::class,
            'force.https' => \App\Http\Middleware\ForceHttps::class,
            'ip.blocking' => \App\Http\Middleware\IpBlocking::class,
            'sql.protection' => \App\Http\Middleware\SqlInjectionProtection::class,
            'protect.files' => \App\Http\Middleware\ProtectSensitiveFiles::class,
            'csrf.enhanced' => \App\Http\Middleware\EnhancedCsrfProtection::class,
        ]);

        $middleware->web(append: [
            // 1. الأمان الأساسي أولاً
            \App\Http\Middleware\ProtectSensitiveFiles::class,
            \App\Http\Middleware\ForceHttps::class,
            \App\Http\Middleware\IpBlocking::class,
            \App\Http\Middleware\SqlInjectionProtection::class,

            // 2. اللغة والتوطين
            \App\Http\Middleware\SetLocale::class,

            // 3. Headers الأمان والكاش
            \App\Http\Middleware\SecurityHeaders::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
