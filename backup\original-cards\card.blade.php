{{-- بطاقة الإعلان المحسنة --}}
@if(!isset($noWrapper) || !$noWrapper)
<div class="col-xl-4 col-lg-6 col-md-6 mb-5">
@endif
<div class="card ad-card h-100 shadow-sm border-0">
        <div class="card-header-custom">
            <!-- صورة الإعلان -->
            <div class="ad-image">
                @if($ad->image_url)
                    <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}" class="card-img-top">
                @else
                    <div class="placeholder-image">
                        <i class="{{ $ad->category->icon }} fa-4x text-muted"></i>
                    </div>
                @endif

                <!-- أيقونة المفضلة -->
                @auth
                    <div class="favorite-icon" data-ad-id="{{ $ad->id }}">
                        <button class="btn-favorite {{ $ad->isFavoritedBy(auth()->id()) ? 'favorited' : '' }}"
                                title="{{ $ad->isFavoritedBy(auth()->id()) ? __('Remove from Favorites') : __('Add to Favorites') }}">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                @endauth

                <!-- شارة الحالة في أعلى يمين الصورة -->
                @if($ad->created_at->diffInDays(now()) <= 3)
                    <div class="category-badge new-badge">
                        <i class="fas fa-star me-1"></i>
                        <span class="arabic-text">{{ __('New') }}</span>
                    </div>
                @endif
            </div>
        </div>

        <div class="card-body p-4" style="padding: 1.5rem !important; overflow: visible; position: relative;">
            <!-- شارة التصنيف -->
            <div class="category-badge-body mb-3">
                <i class="{{ $ad->category->icon }} me-2"></i>
                <span class="arabic-text">{{ $ad->category->name }}</span>
            </div>

            <!-- عنوان الإعلان -->
            <h5 class="ad-title arabic-text fw-bold mb-3">
                <a href="{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}" class="text-decoration-none text-dark">
                    {{ $ad->title }}
                </a>
            </h5>

            <!-- وصف مختصر -->
            <p class="ad-description text-muted arabic-text mb-3">
                {{ Str::limit($ad->description, 80) }}
            </p>

            <!-- معلومات إضافية -->
            <div class="ad-meta mb-3">
                <div class="row g-2">
                    <!-- الموقع -->
                    @if($ad->location)
                        <div class="col-6">
                            <div class="meta-item">
                                <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                <small class="arabic-text">{{ $ad->location }}</small>
                            </div>
                        </div>
                    @endif

                    <!-- عدد المشاهدات -->
                    <div class="col-6">
                        <div class="meta-item">
                            <i class="fas fa-eye text-info me-1"></i>
                            <small class="arabic-text">{{ $ad->views_count }} مشاهدة</small>
                        </div>
                    </div>

                    <!-- تاريخ النشر -->
                    <div class="col-12">
                        <div class="meta-item">
                            <i class="fas fa-calendar text-success me-1"></i>
                            <small class="arabic-text">{{ $ad->created_at->diffForHumans() }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات التواصل -->
            @if($ad->contact_info)
                <div class="contact-info mb-2">
                    <div class="contact-preview">
                        <i class="fas fa-phone text-primary me-2"></i>
                        <small class="text-muted arabic-text">{{ __('Contact available') }}</small>
                    </div>
                </div>
            @endif

            <!-- قسم الأسعار المحسن باستخدام المكون -->
            <x-price-display :ad="$ad" />
        </div>

        <div class="card-footer bg-transparent border-0 p-4 pt-0">
            <!-- أزرار الإجراءات -->
            <div class="ad-actions mb-3">
                <div class="d-flex gap-2">
                    <a href="{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}"
                       class="btn btn-primary flex-fill arabic-text">
                        <i class="fas fa-eye me-2"></i>
                        عرض التفاصيل
                    </a>
                    <button class="btn btn-outline-primary"
                            onclick="shareAd('{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}', '{{ $ad->title }}')"
                            title="مشاركة">
                        <i class="fas fa-share-alt"></i>
                    </button>
                </div>
            </div>

            <!-- تاريخ انتهاء الصلاحية مع تحذير القرب -->
            @if($ad->expires_at)
                <div class="expiry-info mt-2 text-center d-flex align-items-center justify-content-center gap-2">
                    <small class="text-muted arabic-text">
                        <i class="fas fa-hourglass-half me-1"></i>
                        ينتهي في: {{ $ad->expires_at->format('Y/m/d') }}
                    </small>
                    @if($ad->expires_at->diffInDays(now()) <= 7)
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-clock me-1"></i>
                            ينتهي قريباً
                        </span>
                    @endif
                </div>
            @endif
        </div>

        <!-- تأثير hover -->
        <div class="card-overlay"></div>
    </div>
@if(!isset($noWrapper) || !$noWrapper)
</div>
@endif

@push('styles')
<style>
/* بطاقة الإعلان المحسنة والمضغوطة */
.ad-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: visible; /* السماح بظهور العناصر المطلقة الموضع */
    border: 1px solid rgba(0, 0, 0, 0.05);
    min-height: 480px; /* تقليل الارتفاع */
    display: flex;
    flex-direction: column;
}

.ad-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 123, 255, 0.3);
}

/* صورة الإعلان المحسنة والمضغوطة */
.ad-image {
    position: relative;
    height: 220px; /* تقليل ارتفاع الصورة */
    overflow: hidden;
    border-radius: 14px 14px 0 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.ad-card:hover .ad-image img {
    transform: scale(1.08);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0.8;
}

/* أيقونة المفضلة */
.favorite-icon {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
}

.btn-favorite {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-favorite:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-favorite i {
    font-size: 18px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.btn-favorite.favorited i {
    color: #dc3545;
    animation: heartBeat 0.6s ease-in-out;
}

.btn-favorite:hover i {
    color: #dc3545;
}

/* تأثير نبضة القلب */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* شارة التصنيف المحسنة */
.category-badge-body {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 3px 12px rgba(0, 123, 255, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    width: fit-content;
    max-width: 100%;
}

.category-badge-body:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.35);
}

.category-badge-body i {
    font-size: 1rem;
    margin-left: 0.5rem;
}

/* معلومات الإعلان المحسنة */
.ad-meta {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* تم حذف meta-grid واستبدالها بـ Bootstrap Grid */

.meta-item {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
    color: #6c757d;
    transition: color 0.3s ease;
    margin-bottom: 0.3rem;
}

.meta-item:hover {
    color: #495057;
}

.meta-item i {
    width: 16px;
    text-align: center;
    flex-shrink: 0;
    margin-left: 0.3rem;
}

.meta-item small {
    font-weight: 500;
}

/* عنوان الإعلان */
.ad-title {
    font-size: 1.3rem;
    line-height: 1.4;
    color: #2c3e50;
    transition: color 0.3s ease;
    margin-bottom: 1rem;
}

.ad-title:hover {
    color: #007bff;
}

.ad-title a {
    color: inherit;
    text-decoration: none;
}

/* وصف الإعلان */
.ad-description {
    font-size: 1rem;
    line-height: 1.6;
    color: #6c757d;
    margin-bottom: 1.25rem;
}

/* قسم الأسعار المحسن والمضغوط */
.price-section-modern {
    margin: 0.75rem 0;
    position: relative;
    overflow: visible !important;
    z-index: 1;
    padding: 10px; /* مساحة إضافية للعناصر المطلقة الموضع */
}

/* شارات الأسعار الأساسية */
.price-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    width: 100%;
    justify-content: center;
}

.price-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* السعر المجاني المحسن */
.price-free-modern {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.price-free-modern i {
    font-size: 1rem;
    animation: bounce 2s infinite;
}

/* السعر عند الطلب المحسن */
.price-on-request-modern {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
}

/* حاوي عرض الأسعار المحسن */
.price-display-container {
    position: relative;
    background: transparent;
    border-radius: 8px;
    padding: 10px; /* مساحة إضافية للعناصر المطلقة الموضع */
    margin: 0;
    overflow: visible !important;
    z-index: 1;
    /* خلفية شفافة لتجنب التداخل مع شارات الأسعار */
}

/* السعر مع خصم - تصميم مضغوط */
.price-with-discount-modern {
    position: relative;
}

/* شارة الخصم المضغوطة */
.discount-tag {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    z-index: 2;
    animation: pulse 2s infinite;
}

/* صف الأسعار المضغوط */
.price-row {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

/* السعر الحالي المضغوط */
.price-current {
    font-size: 1.4rem;
    font-weight: 700;
    color: #059669;
    text-shadow: 0 1px 2px rgba(5, 150, 105, 0.2);
}

/* السعر الأصلي المضغوط */
.price-original {
    font-size: 0.9rem;
    color: #6b7280;
    text-decoration: line-through;
    font-weight: 500;
    opacity: 0.8;
}

/* مبلغ التوفير المضغوط */
.savings-compact {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.25rem;
}

.savings-compact i {
    font-size: 0.7rem;
}

/* السعر العادي المضغوط */
.price-regular-modern {
    text-align: center;
}

.price-main {
    font-size: 1.3rem;
    font-weight: 700;
    color: #10b981;
    text-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
}

/* صف الشارات المضغوطة */
.price-badges-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
    justify-content: center;
}

/* الشارات المصغرة */
.mini-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.2rem 0.4rem;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.mini-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* شارة قابل للتفاوض المصغرة */
.negotiable-mini {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    border: 1px solid rgba(6, 182, 212, 0.3);
}

/* شارة عرض محدود المصغرة */
.limited-offer-mini {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: 1px solid rgba(245, 158, 11, 0.3);
    animation: blink 1.5s infinite;
}

.mini-badge i {
    font-size: 0.65rem;
}

/* الأنيميشن والتأثيرات */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0.7;
    }
}

/* تأثيرات hover للأسعار المحسنة */
.price-section-modern:hover {
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.price-display-container:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* ضمان ظهور العناصر المطلقة الموضع في بطاقة الإعلانات */
.ad-card .discount-tag-overlay-modern {
    z-index: 10 !important;
    position: absolute !important;
    top: -8px !important;
    left: -8px !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.ad-card .original-price-overlay-modern {
    z-index: 10 !important;
    position: absolute !important;
    top: -8px !important;
    right: -8px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.ad-card .current-price-with-overlay {
    overflow: visible !important;
    position: relative !important;
    min-height: 60px !important; /* مساحة كافية للعناصر المطلقة */
}

.ad-card .price-with-discount-overlay {
    overflow: visible !important;
    position: relative !important;
    padding: 15px !important; /* مساحة إضافية للعناصر المطلقة */
}

.price-with-discount-modern:hover .discount-tag {
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.price-current:hover {
    color: #047857;
    transform: scale(1.02);
    transition: all 0.3s ease;
}

/* أزرار الإجراءات المحسنة */
.ad-actions .btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.ad-actions .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    box-shadow: 0 3px 12px rgba(0, 123, 255, 0.25);
}

.ad-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.35);
}

.ad-actions .btn-outline-primary {
    color: #007bff;
    border-color: #007bff;
    background: transparent;
}

.ad-actions .btn-outline-primary:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25);
}

/* معلومات انتهاء الصلاحية */
.expiry-info {
    background: rgba(255, 193, 7, 0.1);
    border-radius: 8px;
    padding: 0.5rem;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

/* تحسين card-body */
.ad-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: visible !important;
    position: relative;
}

.ad-card .card-footer {
    margin-top: auto;
}

/* شارات الحالة (تبقى في مكانها) */
.category-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    backdrop-filter: blur(10px);
}

/* شارات الحالة المخصصة */
.category-badge.urgent-badge {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.category-badge.new-badge {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.status-badge {
    position: absolute;
    top: 60px;
    left: 10px;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: bold;
}

.status-badge.new {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.status-badge.urgent {
    background: rgba(220, 53, 69, 0.9);
    color: white;
}

/* عنوان الإعلان */
.ad-title {
    font-size: 1rem;
    line-height: 1.4;
    min-height: 2.5rem;
    margin-bottom: 0.75rem;
}

.ad-title a:hover {
    color: #007bff !important;
}

/* وصف الإعلان */
.ad-description {
    font-size: 0.85rem;
    line-height: 1.4;
    min-height: 2.5rem;
    margin-bottom: 0.75rem;
}

/* معلومات إضافية */
.meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.meta-item i {
    width: 16px;
    text-align: center;
}

/* معلومات التواصل */
.contact-preview {
    background: rgba(0, 123, 255, 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
}

/* أزرار الإجراءات */
.ad-actions .btn {
    border-radius: 8px;
    font-size: 0.85rem;
    padding: 0.5rem;
}

/* معلومات انتهاء الصلاحية */
.expiry-info {
    background: rgba(255, 193, 7, 0.1);
    padding: 0.25rem;
    border-radius: 5px;
}

/* تأثير overlay */
.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(0, 123, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.ad-card:hover .card-overlay {
    opacity: 1;
}

/* العرض القائمة المحسن */
.list-view .ad-card {
    flex-direction: row !important;
    align-items: stretch !important;
    min-height: 220px;
    border-radius: 18px;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.list-view .ad-image {
    width: 280px;
    height: 200px;
    flex-shrink: 0;
    border-radius: 18px 0 0 18px;
}

.list-view .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 2rem !important;
}

.list-view .ad-title {
    min-height: auto;
    font-size: 1.4rem;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.list-view .ad-description {
    min-height: auto;
    flex: 1;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* تأثيرات hover لعرض القائمة */
.list-view .ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* تحسين معلومات الإعلان في عرض القائمة */
.list-view .ad-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.list-view .meta-item {
    font-size: 0.95rem;
    color: #6c757d;
}

/* تحسين الأزرار في عرض القائمة */
.list-view .ad-actions {
    margin-top: auto;
    padding-top: 1rem;
}

.list-view .ad-actions .btn {
    font-size: 0.95rem;
    padding: 0.6rem 1.2rem;
    margin-right: 0.5rem;
}

/* تحسينات الأسعار في عرض القائمة */
.list-view .price-section {
    padding: 1rem;
    margin: 1rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 1px solid rgba(0, 123, 255, 0.1);
}

.list-view .current-price .price-value {
    font-size: 1.5rem;
    font-weight: 700;
}

.list-view .discount-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.list-view .price-free,
.list-view .price-on-request {
    padding: 0.8rem;
    font-size: 1rem;
}

.list-view .negotiable-badge,
.list-view .limited-offer-badge {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .ad-image {
        height: 320px;
    }

    .ad-card {
        min-height: 600px;
    }

    /* تحسينات عرض القائمة للشاشات الكبيرة جداً */
    .list-view .ad-image {
        width: 320px;
        height: 240px;
    }

    .list-view .ad-card {
        min-height: 260px;
    }

    .list-view .ad-title {
        font-size: 1.5rem;
    }

    .list-view .ad-description {
        font-size: 1.15rem;
    }
}

/* تحسينات للشاشات المتوسطة */
@media (min-width: 992px) and (max-width: 1399px) {
    .ad-image {
        height: 300px;
    }

    .ad-card {
        min-height: 580px;
    }

    /* تحسينات عرض القائمة للشاشات المتوسطة */
    .list-view .ad-image {
        width: 300px;
        height: 220px;
    }

    .list-view .ad-card {
        min-height: 240px;
    }

    .list-view .ad-title {
        font-size: 1.35rem;
    }

    .list-view .ad-description {
        font-size: 1.05rem;
    }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .ad-image {
        height: 250px;
    }

    .ad-card {
        min-height: 480px;
    }

    .ad-title {
        font-size: 1.1rem;
        min-height: 2.5rem;
    }

    .ad-description {
        font-size: 0.9rem;
        min-height: 2.5rem;
    }

    .ad-actions .btn {
        font-size: 0.85rem;
        padding: 0.5rem;
    }

    .list-view .ad-card {
        flex-direction: column !important;
        min-height: 420px;
        margin-bottom: 1.5rem;
    }

    .list-view .ad-image {
        width: 100%;
        height: 250px;
        border-radius: 18px 18px 0 0;
    }

    .list-view .card-body {
        padding: 1.5rem !important;
    }

    .list-view .ad-title {
        font-size: 1.2rem;
    }

    .list-view .ad-description {
        font-size: 1rem;
    }

    .btn-favorite {
        width: 35px;
        height: 35px;
    }

    .btn-favorite i {
        font-size: 16px;
    }

    .status-badge {
        top: 55px;
    }
}

/* تحسينات للتابلت في عرض القائمة */
@media (min-width: 769px) and (max-width: 991px) {
    .list-view .ad-image {
        width: 250px;
        height: 180px;
    }

    .list-view .ad-card {
        min-height: 200px;
    }

    .list-view .ad-title {
        font-size: 1.25rem;
    }

    .list-view .ad-description {
        font-size: 1rem;
    }

    .list-view .card-body {
        padding: 1.5rem !important;
    }
}

@media (max-width: 576px) {
    .ad-image {
        height: 220px;
    }

    .btn-favorite {
        width: 32px;
        height: 32px;
    }

    .btn-favorite i {
        font-size: 14px;
    }

    .status-badge {
        top: 50px;
    }

    .category-badge,
    .status-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    /* تحسينات عرض القائمة للشاشات الصغيرة جداً */
    .list-view .ad-card {
        min-height: 380px;
        margin-bottom: 1rem;
    }

    .list-view .ad-image {
        height: 200px;
    }

    .list-view .card-body {
        padding: 1rem !important;
    }

    .list-view .ad-title {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
    }

    .list-view .ad-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .list-view .ad-actions .btn {
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
    }

    /* تحسينات الأسعار للشاشات الصغيرة */
    .price-section {
        padding: 1rem;
        margin: 0.5rem 0;
    }

    .current-price .price-value {
        font-size: 1.4rem !important;
    }

    .discount-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .savings-amount {
        font-size: 0.8rem;
    }

    /* تحسينات الأسعار في عرض القائمة للشاشات الصغيرة */
    .list-view .price-section {
        padding: 0.8rem;
        margin: 0.5rem 0;
    }

    .list-view .current-price .price-value {
        font-size: 1.2rem !important;
    }

    .list-view .discount-badge {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }

    /* تحسينات الأسعار الجديدة للموبايل */
    .price-current {
        font-size: 1.2rem !important;
    }

    .price-main {
        font-size: 1.1rem !important;
    }

    .price-badges-row {
        gap: 0.2rem;
    }

    .mini-badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
    }

    .savings-compact {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .discount-tag {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* تحسينات إضافية للشاشات الصغيرة جداً */
@media (max-width: 576px) {
    .price-row {
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .price-current {
        font-size: 1.1rem !important;
    }

    .price-original {
        font-size: 0.8rem;
    }

    .ad-card {
        min-height: 380px;
    }

    .ad-image {
        height: 160px;
    }
}
</style>
@endpush

@push('scripts')
<script>
// دالة مشاركة الإعلان
function shareAd(url, title) {
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        // نسخ الرابط للحافظة
        navigator.clipboard.writeText(url).then(function() {
            // إظهار رسالة نجاح
            showToast('{{ __("Link copied to clipboard") }}', 'success');
        }).catch(function() {
            // فتح نافذة مشاركة تقليدية
            window.open('https://wa.me/?text=' + encodeURIComponent(title + ' - ' + url), '_blank');
        });
    }
}

// تم حذف الكود المكرر لنظام المفضلة
// الكود الأساسي موجود في public/js/favorites.js
// لتجنب تكرار event listeners والرسائل المكررة
</script>
@endpush

