/**
 * نظام تشخيص وتصحيح أخطاء المفضلة
 * يساعد في مراقبة وحل مشاكل نظام المفضلة
 */

// تفعيل وضع التشخيص (يمكن تعطيله في الإنتاج)
const DEBUG_MODE = false; // تغيير إلى true للتشخيص

/**
 * تسجيل أحداث المفضلة للتشخيص
 */
function logFavoriteEvent(event, data) {
    if (!DEBUG_MODE) return;
    
    console.group(`🔍 Favorites Debug: ${event}`);
    console.log('Timestamp:', new Date().toISOString());
    console.log('Data:', data);
    console.groupEnd();
}

/**
 * فحص تكرار event listeners
 */
function checkDuplicateListeners() {
    if (!DEBUG_MODE) return;
    
    const buttons = document.querySelectorAll('.btn-favorite');
    let duplicateCount = 0;
    
    buttons.forEach((button, index) => {
        const listeners = button.getAttribute('data-favorites-initialized');
        if (!listeners) {
            console.warn(`Button ${index} has no initialization marker`);
        }
    });
    
    logFavoriteEvent('Duplicate Check', {
        totalButtons: buttons.length,
        duplicateCount: duplicateCount
    });
}

/**
 * مراقبة طلبات AJAX
 */
function monitorAjaxRequests() {
    if (!DEBUG_MODE) return;
    
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        
        if (url.includes('favorites')) {
            logFavoriteEvent('AJAX Request', {
                url: url,
                method: args[1]?.method || 'GET',
                timestamp: Date.now()
            });
        }
        
        return originalFetch.apply(this, args);
    };
}

/**
 * فحص حالة نظام المفضلة
 */
function checkFavoritesSystemHealth() {
    const health = {
        favoritesJsLoaded: typeof window.FavoritesSystem !== 'undefined',
        csrfToken: !!document.querySelector('meta[name="csrf-token"]'),
        userAuthenticated: !!document.querySelector('meta[name="user-authenticated"]'),
        favoriteButtons: document.querySelectorAll('.btn-favorite').length,
        toastSystem: typeof showToast === 'function'
    };
    
    logFavoriteEvent('System Health Check', health);
    return health;
}

/**
 * إصلاح المشاكل الشائعة
 */
function fixCommonIssues() {
    // إزالة event listeners المكررة
    document.querySelectorAll('.btn-favorite').forEach(button => {
        if (button.hasAttribute('data-favorites-initialized')) {
            // الزر مهيأ بالفعل، لا نحتاج لفعل شيء
            return;
        }
        
        // إضافة event listener إذا لم يكن موجوداً
        if (window.FavoritesSystem) {
            window.FavoritesSystem.reinitialize();
        }
    });
    
    logFavoriteEvent('Fixed Common Issues', {
        timestamp: Date.now()
    });
}

// تهيئة نظام التشخيص
if (DEBUG_MODE) {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 Favorites Debug System Loaded');
        
        // فحص صحة النظام
        setTimeout(() => {
            checkFavoritesSystemHealth();
            checkDuplicateListeners();
        }, 1000);
        
        // مراقبة طلبات AJAX
        monitorAjaxRequests();
        
        // إضافة أدوات التشخيص للوحة التحكم
        window.FavoritesDebug = {
            checkHealth: checkFavoritesSystemHealth,
            checkDuplicates: checkDuplicateListeners,
            fixIssues: fixCommonIssues,
            enableDebug: () => { DEBUG_MODE = true; },
            disableDebug: () => { DEBUG_MODE = false; }
        };
        
        console.log('🎯 Use FavoritesDebug.checkHealth() to diagnose issues');
    });
}
