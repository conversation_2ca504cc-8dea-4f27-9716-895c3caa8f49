{{-- تذييل البطاقة --}}
<div class="card-footer bg-transparent border-0 pt-0">
    @if($config['showActions'])
        <!-- أزرار الإجراءات -->
        <div class="action-buttons d-flex justify-content-between align-items-center">
            <div class="left-actions">
                <a href="{{ route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye me-1"></i>
                    {{ __('View Details') }}
                </a>
            </div>

            <div class="right-actions">
                <!-- زر المفضلة -->
                @if($config['showFavorites'] ?? true)
                    @auth
                        <div class="favorite-icon" data-ad-id="{{ $ad->id }}"
                             @if(isset($favorite)) data-favorite-id="{{ $favorite->id }}" @endif>
                            <button class="btn btn-outline-danger btn-sm btn-favorite me-1"
                                    title="{{ __('Add to Favorites') }}">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    @endauth
                @endif

                <!-- زر المقارنة -->
                <button class="btn btn-outline-info btn-sm compare-btn"
                        data-ad-id="{{ $ad->id }}"
                        onclick="toggleCompare({{ $ad->id }})"
                        title="{{ __('Add to Compare') }}">
                    <i class="fas fa-balance-scale"></i>
                </button>

                <!-- زر المشاركة -->
                <button class="btn btn-outline-secondary btn-sm ms-1"
                        onclick="shareAd('{{ route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]) }}', '{{ $ad->title }}')"
                        title="{{ __('Share') }}">
                    <i class="fas fa-share-alt"></i>
                </button>

                {{-- تم نقل أيقونات التواصل إلى قسم منفصل في أعلى البطاقة --}}
            </div>
        </div>
    @endif

    @if($config['showExpiry'] || ($config['variant'] === 'favorites'))
        <!-- معلومات انتهاء الصلاحية ومعلومات المفضلة -->
        <div class="footer-info mt-2">
            @if($config['showExpiry'] && $ad->expires_at)
                @php
                    $daysLeft = $ad->expires_at->diffInDays(now());
                    $isExpiringSoon = $daysLeft <= 7;
                @endphp

                <small class="text-{{ $isExpiringSoon ? 'danger' : 'muted' }} d-block">
                    <i class="fas fa-calendar-times me-1"></i>
                    @if($daysLeft > 0)
                        {{ __('Expires in :days days', ['days' => $daysLeft]) }}
                    @else
                        {{ __('Expired') }}
                    @endif
                </small>
            @endif

            {{-- تاريخ إضافة المفضلة --}}
            @if($config['variant'] === 'favorites' && isset($config['favoriteId']))
                @php
                    // الحصول على تاريخ إضافة المفضلة الحقيقي من جدول interactions
                    $favorite = \App\Models\Interaction::find($config['favoriteId']);
                    $addedDate = $favorite ? $favorite->created_at : now();
                @endphp
                <small class="text-success d-block mt-1">
                    <i class="fas fa-heart text-danger me-1"></i>
                    {{ __('Added to favorites') }} {{ $addedDate->diffForHumans() }}
                </small>
            @endif
        </div>
    @endif
</div>