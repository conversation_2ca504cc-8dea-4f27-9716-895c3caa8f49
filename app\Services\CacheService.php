<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * خدمة إدارة التخزين المؤقت المتقدمة
 * تدير جميع عمليات التخزين المؤقت في التطبيق لتحسين الأداء
 */
class CacheService
{
    // مدد انتهاء الصلاحية بالدقائق
    const CACHE_DURATIONS = [
        'categories' => 60,        // ساعة واحدة
        'featured_ads' => 30,      // 30 دقيقة
        'popular_ads' => 15,       // 15 دقيقة
        'user_stats' => 10,        // 10 دقائق
        'site_stats' => 5,         // 5 دقائق
        'announcements' => 120,    // ساعتان
        'settings' => 1440,        // 24 ساعة
        'search_results' => 15,    // 15 دقيقة
        'related_ads' => 30,       // 30 دقيقة
        'navigation' => 120,       // ساعتان
    ];

    /**
     * الحصول على التصنيفات النشطة من الكاش
     */
    public static function getActiveCategories()
    {
        return Cache::remember('active_categories', self::CACHE_DURATIONS['categories'], function () {
            return \App\Models\Category::active()
                ->withCount(['ads' => function ($query) {
                    $query->active()->notExpired();
                }])
                ->orderBy('name_ar')
                ->get();
        });
    }

    /**
     * الحصول على الإعلانات المميزة من الكاش
     */
    public static function getFeaturedAds($limit = 6)
    {
        $cacheKey = "featured_ads_{$limit}";

        return Cache::remember($cacheKey, self::CACHE_DURATIONS['featured_ads'], function () use ($limit) {
            return \App\Models\Ad::active()
                ->notExpired()
                ->featured()
                ->with(['category', 'user'])
                ->latest()
                ->limit($limit)
                ->get();
        });
    }

    /**
     * الحصول على الإعلانات الأكثر مشاهدة من الكاش
     */
    public static function getPopularAds($limit = 6)
    {
        $cacheKey = "popular_ads_{$limit}";

        return Cache::remember($cacheKey, self::CACHE_DURATIONS['popular_ads'], function () use ($limit) {
            return \App\Models\Ad::active()
                ->notExpired()
                ->orderBy('views_count', 'desc')
                ->with(['category', 'user'])
                ->limit($limit)
                ->get();
        });
    }

    /**
     * الحصول على إحصائيات المستخدم من الكاش
     */
    public static function getUserStats($userId)
    {
        $cacheKey = "user_stats_{$userId}";

        return Cache::remember($cacheKey, self::CACHE_DURATIONS['user_stats'], function () use ($userId) {
            $user = \App\Models\User::find($userId);
            if (!$user) return null;

            return [
                'total_ads' => $user->ads()->count(),
                'active_ads' => $user->activeAds()->count(),
                'pending_ads' => $user->pendingAds()->count(),
                'total_views' => $user->ads()->sum('views_count'),
            ];
        });
    }

    /**
     * الحصول على إحصائيات الموقع العامة من الكاش
     */
    public static function getSiteStats()
    {
        return Cache::remember('site_stats', self::CACHE_DURATIONS['site_stats'], function () {
            try {
                return [
                    'total_categories' => \App\Models\Category::active()->count(),
                    'total_ads' => \App\Models\Ad::active()->notExpired()->count(),
                    'total_users' => \App\Models\User::count(),
                    // استخدام التفاعلات بدلاً من feedbacks للهيكل الجديد
                    'total_interactions' => \App\Models\Interaction::count(),
                    'total_ratings' => \App\Models\Interaction::where('type', 'rating')->count(),
                ];
            } catch (\Exception $e) {
                // في حالة عدم وجود البيانات، إرجاع قيم افتراضية
                return [
                    'total_categories' => 0,
                    'total_ads' => 0,
                    'total_users' => 0,
                    'total_interactions' => 0,
                    'total_ratings' => 0,
                ];
            }
        });
    }

    /**
     * الحصول على الإعلانات النشطة من الكاش
     */
    public static function getActiveAnnouncements()
    {
        return Cache::remember('active_announcements', self::CACHE_DURATIONS['announcements'], function () {
            return \App\Models\Announcement::active()->ordered()->get();
        });
    }

    /**
     * الحصول على إعدادات الإعلانات من الكاش
     */
    public static function getAnnouncementSettings()
    {
        return Cache::remember('announcement_settings', self::CACHE_DURATIONS['settings'], function () {
            return (object) [
                'is_enabled' => \App\Models\Setting::get('announcements_enabled', true),
                'animation_type' => \App\Models\Setting::get('announcements_animation_type', 'rotation'),
                'transition_duration' => \App\Models\Setting::get('announcements_transition_duration', 5),
                'scroll_speed' => \App\Models\Setting::get('announcements_scroll_speed', 30)
            ];
        });
    }

    /**
     * مسح كاش محدد
     */
    public static function clearCache($key)
    {
        try {
            Cache::forget($key);
            Log::info("Cache cleared: {$key}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to clear cache: {$key}", ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * مسح جميع الكاش المتعلق بالتصنيفات
     */
    public static function clearCategoriesCache()
    {
        self::clearCache('active_categories');
        Log::info('Categories cache cleared');
    }

    /**
     * مسح جميع الكاش المتعلق بالإعلانات
     */
    public static function clearAdsCache()
    {
        // مسح الكاش للإعلانات المميزة بأحجام مختلفة
        for ($i = 1; $i <= 20; $i++) {
            self::clearCache("featured_ads_{$i}");
            self::clearCache("popular_ads_{$i}");
        }

        self::clearCache('site_stats');
        Log::info('Ads cache cleared');
    }

    /**
     * مسح كاش المستخدم
     */
    public static function clearUserCache($userId)
    {
        self::clearCache("user_stats_{$userId}");
        Log::info("User cache cleared for user: {$userId}");
    }

    /**
     * مسح جميع الكاش المتعلق بالإعلانات
     */
    public static function clearAnnouncementsCache()
    {
        self::clearCache('active_announcements');
        self::clearCache('announcement_settings');
        Log::info('Announcements cache cleared');
    }

    /**
     * مسح كاش البحث
     */
    public static function clearSearchCache($searchTerm = null)
    {
        if ($searchTerm) {
            $cacheKey = "search_" . md5($searchTerm);
            self::clearCache($cacheKey);
        } else {
            // مسح جميع كاش البحث (يتطلب تحسين لاحقاً)
            Cache::flush();
        }
        Log::info('Search cache cleared', ['term' => $searchTerm]);
    }

    /**
     * مسح كاش الإعلانات ذات الصلة
     */
    public static function clearRelatedAdsCache($adId)
    {
        self::clearCache("related_ads_{$adId}_4");
        self::clearCache("related_ads_{$adId}_6");
        self::clearCache("related_ads_{$adId}_8");
        Log::info("Related ads cache cleared for ad: {$adId}");
    }

    /**
     * الحصول على إحصائيات الكاش
     */
    public static function getCacheStats()
    {
        try {
            // هذا يعتمد على نوع الكاش المستخدم
            return [
                'status' => 'active',
                'driver' => config('cache.default'),
                'keys_count' => 'غير متاح', // يحتاج تحسين حسب نوع الكاش
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * تسخين الكاش (تحميل البيانات المهمة مسبقاً)
     */
    public static function warmupCache()
    {
        try {
            // تحميل التصنيفات
            self::getActiveCategories();

            // تحميل الإعلانات المميزة
            self::getFeaturedAds(6);

            // تحميل الإعلانات الشائعة
            self::getPopularAds(6);

            Log::info('Cache warmed up successfully');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to warm up cache', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * مسح جميع الكاش
     */
    public static function clearAllCache()
    {
        try {
            Cache::flush();
            Log::info('All cache cleared');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clear all cache', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * الحصول على معلومات الكاش
     */
    public static function getCacheInfo()
    {
        $cacheKeys = [
            'active_categories',
            'featured_ads_6',
            'popular_ads_6',
            'site_stats',
            'active_announcements',
            'announcement_settings'
        ];

        $info = [];
        foreach ($cacheKeys as $key) {
            $info[$key] = [
                'exists' => Cache::has($key),
                'size' => Cache::has($key) ? strlen(serialize(Cache::get($key))) : 0
            ];
        }

        return $info;
    }


}
