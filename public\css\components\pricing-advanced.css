/*
 * ملف CSS متقدم لمكونات الأسعار
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على أنماط متقدمة لعرض الأسعار بشكل جذاب ومنظم
 */

/* ===== متغيرات الألوان للأسعار ===== */
:root {
    --price-primary: #10b981;
    --price-secondary: #059669;
    --price-accent: #34d399;
    --price-warning: #f59e0b;
    --price-danger: #ef4444;
    --price-info: #3b82f6;
    --price-success: #10b981;
    --price-muted: #6b7280;
    --price-border: rgba(16, 185, 129, 0.2);
    --price-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --price-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== بطاقة الأسعار الرئيسية ===== */
.pricing-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid var(--price-border);
    border-radius: 16px;
    box-shadow: var(--price-shadow);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--price-primary) 0%, var(--price-accent) 100%);
}

.pricing-card:hover {
    box-shadow: var(--price-shadow-hover);
    transform: translateY(-2px);
}

/* ===== محتوى الأسعار ===== */
.pricing-content {
    background: rgba(16, 185, 129, 0.02);
    border: 1px solid rgba(16, 185, 129, 0.1);
    border-radius: 12px;
    padding: 1.25rem;
    position: relative;
}

.pricing-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, var(--price-primary) 50%, transparent 100%);
}

/* ===== تحسينات للعرض المفصل ===== */
.price-detailed .price-display-container {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(16, 185, 129, 0.15);
    border-radius: 12px;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
    backdrop-filter: blur(10px);
}

.price-detailed .current-price-content {
    text-align: center;
    padding: 0.75rem;
}

.price-detailed .price-value-overlay {
    font-size: 2rem;
    font-weight: 800;
    color: var(--price-primary);
    text-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

/* ===== ملاحظات السعر المتقدمة ===== */
.price-notes-detailed {
    margin-top: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
    border: 1px solid rgba(59, 130, 246, 0.15);
    border-radius: 10px;
    border-right: 4px solid var(--price-info);
    position: relative;
}

.price-notes-detailed::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--price-info) 50%, transparent 100%);
}

.price-notes-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 0.9rem;
    color: var(--price-info);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-notes-content {
    font-size: 0.9rem;
    color: #374151;
    line-height: 1.6;
    text-align: right;
    font-weight: 500;
}

/* ===== معلومات العملة المتقدمة ===== */
.currency-info-detailed {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
    border: 1px solid rgba(245, 158, 11, 0.15);
    border-radius: 8px;
    border-left: 3px solid var(--price-warning);
}

.currency-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #92400e;
    font-weight: 600;
}

.currency-info-item i {
    font-size: 1rem;
    color: var(--price-warning);
}

/* ===== شارات الميزات المتقدمة ===== */
.expires-badge {
    background: linear-gradient(135deg, var(--price-danger) 0%, #dc2626 100%);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: white;
    font-weight: 600;
    animation: pulse-glow 2s infinite;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 4px 8px rgba(239, 68, 68, 0.5);
        transform: scale(1.02);
    }
}

/* ===== تحسينات الشارات الإضافية ===== */
.price-detailed .additional-badges-row {
    margin-top: 1rem;
    justify-content: flex-start;
    gap: 0.5rem;
}

.price-detailed .feature-badge {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    font-weight: 700;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.price-detailed .feature-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* ===== تأثيرات التفاعل ===== */
.pricing-card .price-value-overlay {
    cursor: pointer;
    transition: all 0.3s ease;
}

.pricing-card .price-value-overlay:hover {
    transform: scale(1.05);
    text-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

/* ===== الاستجابة للأجهزة المحمولة ===== */
@media (max-width: 768px) {
    .pricing-card {
        margin-bottom: 1rem;
        border-radius: 12px;
    }
    
    .pricing-content {
        padding: 1rem;
    }
    
    .price-detailed .price-display-container {
        padding: 1rem;
    }
    
    .price-detailed .price-value-overlay {
        font-size: 1.75rem;
    }
    
    .price-notes-detailed,
    .currency-info-detailed {
        padding: 0.75rem;
        margin-top: 0.75rem;
    }
    
    .price-detailed .additional-badges-row {
        justify-content: center;
        margin-top: 0.75rem;
    }
    
    .price-detailed .feature-badge {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .pricing-content {
        padding: 0.75rem;
    }
    
    .price-detailed .price-value-overlay {
        font-size: 1.5rem;
    }
    
    .price-notes-header {
        font-size: 0.8rem;
    }
    
    .price-notes-content {
        font-size: 0.85rem;
    }
    
    .currency-info-item {
        font-size: 0.8rem;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .pricing-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .pricing-content {
        background: white;
        border: 1px solid #ddd;
    }
    
    .price-detailed .feature-badge {
        background: #f3f4f6 !important;
        color: #374151 !important;
        border: 1px solid #d1d5db;
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.pricing-card:focus-within {
    outline: 2px solid var(--price-primary);
    outline-offset: 2px;
}

.price-detailed .feature-badge:focus {
    outline: 2px solid currentColor;
    outline-offset: 2px;
}

/* ===== تأثيرات الحركة المتقدمة ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
    20% { opacity: 1; transform: translateX(-50%) translateY(0); }
    80% { opacity: 1; transform: translateX(-50%) translateY(0); }
    100% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
}

.pricing-card {
    animation: slideInUp 0.6s ease-out;
}

.pricing-card.animate-in {
    animation: slideInUp 0.6s ease-out;
}

.price-notes-detailed,
.currency-info-detailed {
    animation: slideInUp 0.8s ease-out;
}
