@extends('layouts.app')

@section('title', 'إعلانات ' . $user->name)

@section('content')
<div class="container-fluid py-4">
    <!-- رأس الصفحة مع معلومات المستخدم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="user-header-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <!-- صورة المستخدم -->
                            <div class="user-avatar-large me-4">
                                @if($user->avatar)
                                    <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" class="avatar-image-large">
                                @else
                                    <div class="avatar-placeholder-large">
                                        <i class="fas fa-user"></i>
                                    </div>
                                @endif
                                
                                <!-- شارات المستخدم -->
                                <div class="user-badges-large">
                                    @if($user->is_verified)
                                        <span class="badge-verified-large" title="مستخدم موثق">
                                            <i class="fas fa-check-circle"></i>
                                        </span>
                                    @endif
                                    
                                    @if($user->is_admin)
                                        <span class="badge-admin-large" title="مدير">
                                            <i class="fas fa-crown"></i>
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <!-- معلومات المستخدم -->
                            <div class="user-info">
                                <h1 class="user-name">{{ $user->name }}</h1>
                                @if($user->company_name)
                                    <p class="company-name">{{ $user->company_name }}</p>
                                @endif
                                <div class="user-meta">
                                    <span class="join-date">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        عضو منذ {{ $user->created_at->format('Y') }}
                                    </span>
                                    @if($user->last_seen_at)
                                        <span class="last-seen ms-3">
                                            <i class="fas fa-clock me-1"></i>
                                            آخر نشاط {{ $user->last_seen_at->diffForHumans() }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 text-end">
                        <!-- إحصائيات سريعة -->
                        <div class="quick-stats">
                            <div class="stat-item">
                                <span class="stat-number">{{ $stats['active_ads'] }}</span>
                                <span class="stat-label">إعلان نشط</span>
                            </div>
                            @if($stats['average_rating'] > 0)
                                <div class="stat-item">
                                    <span class="stat-number">{{ number_format($stats['average_rating'], 1) }}</span>
                                    <span class="stat-label">متوسط التقييم</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات مفصلة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="stats-detailed-card">
                <h4 class="mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات المستخدم
                </h4>
                <div class="row">
                    <div class="col-md-3 col-6">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-bullhorn text-primary"></i>
                            </div>
                            <div class="stat-content">
                                <h5>{{ $stats['total_ads'] }}</h5>
                                <p>إجمالي الإعلانات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle text-success"></i>
                            </div>
                            <div class="stat-content">
                                <h5>{{ $stats['active_ads'] }}</h5>
                                <p>إعلانات نشطة</p>
                            </div>
                        </div>
                    </div>
                    @if($stats['average_rating'] > 0)
                        <div class="col-md-3 col-6">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-star text-warning"></i>
                                </div>
                                <div class="stat-content">
                                    <h5>{{ number_format($stats['average_rating'], 1) }}</h5>
                                    <p>متوسط التقييم</p>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if($stats['reviews_count'] > 0)
                        <div class="col-md-3 col-6">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-comments text-info"></i>
                                </div>
                                <div class="stat-content">
                                    <h5>{{ $stats['reviews_count'] }}</h5>
                                    <p>عدد التقييمات</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- الإعلانات -->
    <div class="row">
        <div class="col-12">
            <div class="ads-section">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>
                        <i class="fas fa-list me-2"></i>
                        إعلانات {{ $user->name }} ({{ $ads->total() }})
                    </h4>
                    
                    <!-- أزرار التصفية -->
                    <div class="filter-buttons">
                        <button class="btn btn-outline-primary btn-sm active" data-filter="all">
                            جميع الإعلانات
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" data-filter="recent">
                            الأحدث
                        </button>
                        <button class="btn btn-outline-info btn-sm" data-filter="popular">
                            الأكثر مشاهدة
                        </button>
                    </div>
                </div>

                @if($ads->count() > 0)
                    <div class="row" id="ads-container">
                        @foreach($ads as $ad)
                            <div class="col-lg-4 col-md-6 mb-4">
                                @include('components.ad-card.index', [
                                    'ad' => $ad,
                                    'variant' => 'default',
                                    'showFavorites' => true,
                                    'showPricing' => true,
                                    'showMeta' => true,
                                    'showActions' => true
                                ])
                            </div>
                        @endforeach
                    </div>

                    <!-- التصفح -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $ads->links() }}
                    </div>
                @else
                    <div class="empty-state">
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إعلانات نشطة</h5>
                            <p class="text-muted">لم ينشر {{ $user->name }} أي إعلانات نشطة حتى الآن.</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
/* أنماط صفحة إعلانات المستخدم */
.user-header-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.user-avatar-large {
    position: relative;
}

.avatar-image-large {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.avatar-placeholder-large {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.user-badges-large {
    position: absolute;
    bottom: -5px;
    right: -5px;
    display: flex;
    gap: 3px;
}

.badge-verified-large,
.badge-admin-large {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    border: 3px solid white;
}

.badge-verified-large {
    background: #10b981;
}

.badge-admin-large {
    background: #f59e0b;
}

.user-name {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.company-name {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0.25rem 0;
}

.user-meta {
    margin-top: 0.5rem;
    opacity: 0.8;
}

.quick-stats {
    display: flex;
    gap: 2rem;
    justify-content: flex-end;
}

.quick-stats .stat-item {
    text-align: center;
}

.quick-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
}

.quick-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.stats-detailed-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.stat-card {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.stat-card h5 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: #1a202c;
}

.stat-card p {
    margin: 0;
    color: #718096;
    font-size: 0.9rem;
}

.ads-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.filter-buttons .btn {
    margin-left: 0.5rem;
}

.empty-state {
    background: #f8fafc;
    border-radius: 10px;
    border: 2px dashed #e2e8f0;
}

@media (max-width: 768px) {
    .user-header-card {
        padding: 1.5rem;
    }
    
    .user-header-card .row {
        text-align: center;
    }
    
    .quick-stats {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .avatar-image-large,
    .avatar-placeholder-large {
        width: 80px;
        height: 80px;
    }
    
    .user-name {
        font-size: 1.5rem;
    }
}
</style>
@endsection
