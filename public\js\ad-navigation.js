/**
 * نظام التنقل بين الإعلانات - AJAX
 * يدير التنقل السلس بين الإعلانات بدون تحديث الصفحة
 */

class AdNavigation {
    constructor() {
        this.isLoading = false;
        this.currentAdId = null;
        this.categorySlug = null;
        this.init();
    }

    /**
     * تهيئة نظام التنقل
     */
    init() {
        this.extractCurrentAdInfo();
        this.bindNavigationEvents();
        this.preloadAdjacentAds();
    }

    /**
     * استخراج معلومات الإعلان الحالي
     */
    extractCurrentAdInfo() {
        const currentUrl = window.location.pathname;
        const urlParts = currentUrl.split('/');
        
        if (urlParts.length >= 4 && urlParts[1] === 'ads') {
            this.categorySlug = urlParts[2];
            this.currentAdSlug = urlParts[3];
        }
    }

    /**
     * ربط أحداث التنقل - مع الحفاظ على التوافق
     */
    bindNavigationEvents() {
        // التنقل بالأزرار - اختياري (يمكن تفعيله/إلغاؤه)
        document.addEventListener('click', (e) => {
            const navButton = e.target.closest('.ad-navigation a');
            if (navButton && this.isAjaxNavigationEnabled()) {
                e.preventDefault();
                this.navigateToAd(navButton.href);
            }
            // إذا لم يكن AJAX مفعلاً، سيعمل الرابط العادي
        });

        // التنقل بلوحة المفاتيح - اختياري
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey || !this.isAjaxNavigationEnabled()) return;

            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.navigateToNext();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.navigateToPrevious();
                    break;
            }
        });
    }

    /**
     * فحص ما إذا كان التنقل AJAX مفعلاً
     */
    isAjaxNavigationEnabled() {
        // يمكن التحكم في هذا من الإعدادات أو localStorage
        return localStorage.getItem('ajax_navigation') !== 'disabled';
    }

    /**
     * التنقل إلى إعلان محدد
     */
    async navigateToAd(url) {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoadingState();

        try {
            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.success) {
                this.updateAdContent(data.ad, data.relatedAds, data.previousAd, data.nextAd);
                this.updateUrl(url);
                this.updatePageTitle(data.ad.title);
            } else {
                throw new Error(data.message || 'فشل في تحميل الإعلان');
            }

        } catch (error) {
            console.error('خطأ في التنقل:', error);

            // إظهار رسالة خطأ للمستخدم
            if (window.showToast) {
                window.showToast('حدث خطأ في التنقل، سيتم تحميل الصفحة...', 'warning');
            }

            // في حالة الخطأ، انتقل بالطريقة التقليدية بعد تأخير قصير
            setTimeout(() => {
                window.location.href = url;
            }, 1000);
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    /**
     * التنقل للإعلان التالي
     */
    navigateToNext() {
        const nextButton = document.querySelector('.ad-navigation a[href*="next"]');
        if (nextButton) {
            this.navigateToAd(nextButton.href);
        }
    }

    /**
     * التنقل للإعلان السابق
     */
    navigateToPrevious() {
        const prevButton = document.querySelector('.ad-navigation a[href*="previous"]');
        if (prevButton) {
            this.navigateToAd(prevButton.href);
        }
    }

    /**
     * تحديث محتوى الإعلان
     */
    updateAdContent(ad, relatedAds, previousAd, nextAd) {
        // تحديث تفاصيل الإعلان
        this.updateAdDetails(ad);
        
        // تحديث الإعلانات المشابهة
        this.updateRelatedAds(relatedAds);
        
        // تحديث أزرار التنقل
        this.updateNavigationButtons(previousAd, nextAd);
        
        // تحديث معلومات الإعلان الحالي
        this.currentAdSlug = ad.slug;
        
        // إعادة تهيئة المكونات التفاعلية
        this.reinitializeComponents();
    }

    /**
     * تحديث تفاصيل الإعلان
     */
    updateAdDetails(ad) {
        // تحديث العنوان
        const titleElement = document.querySelector('.ad-title, h1');
        if (titleElement) {
            titleElement.textContent = ad.title;
        }

        // تحديث الوصف
        const descriptionElement = document.querySelector('.ad-description');
        if (descriptionElement) {
            descriptionElement.innerHTML = ad.description;
        }

        // تحديث السعر
        const priceElement = document.querySelector('.ad-price');
        if (priceElement && ad.price) {
            priceElement.textContent = ad.price + ' ريال';
        }

        // تحديث الصور
        this.updateAdImages(ad.images);
        
        // تحديث معلومات الاتصال
        this.updateContactInfo(ad);
    }

    /**
     * تحديث صور الإعلان
     */
    updateAdImages(images) {
        const imageContainer = document.querySelector('.ad-images');
        if (imageContainer && images && images.length > 0) {
            // تحديث الصور هنا
            // يمكن تطوير هذا لاحقاً حسب بنية HTML الخاصة بالصور
        }
    }

    /**
     * تحديث معلومات الاتصال
     */
    updateContactInfo(ad) {
        const phoneElement = document.querySelector('.ad-phone');
        if (phoneElement && ad.phone) {
            phoneElement.textContent = ad.phone;
        }

        const locationElement = document.querySelector('.ad-location');
        if (locationElement && ad.location) {
            locationElement.textContent = ad.location;
        }
    }

    /**
     * تحديث أزرار التنقل
     */
    updateNavigationButtons(previousAd, nextAd) {
        const navigationContainer = document.querySelector('.ad-navigation');
        if (!navigationContainer) return;

        let navigationHTML = '<div class="row g-3">';

        if (previousAd) {
            navigationHTML += `
                <div class="col-6">
                    <a href="/ads/${this.categorySlug}/${previousAd.slug}" 
                       class="btn btn-outline-primary w-100 text-start">
                        <i class="fas fa-arrow-right me-2"></i>
                        <div class="nav-content">
                            <small class="text-muted arabic-text">الإعلان السابق</small>
                            <div class="nav-title arabic-text">${previousAd.title.substring(0, 30)}...</div>
                        </div>
                    </a>
                </div>
            `;
        }

        if (nextAd) {
            navigationHTML += `
                <div class="col-6">
                    <a href="/ads/${this.categorySlug}/${nextAd.slug}" 
                       class="btn btn-outline-primary w-100 text-end">
                        <div class="nav-content">
                            <small class="text-muted arabic-text">الإعلان التالي</small>
                            <div class="nav-title arabic-text">${nextAd.title.substring(0, 30)}...</div>
                        </div>
                        <i class="fas fa-arrow-left ms-2"></i>
                    </a>
                </div>
            `;
        }

        navigationHTML += '</div>';
        navigationContainer.innerHTML = navigationHTML;
    }

    /**
     * تحديث URL بدون تحديث الصفحة
     */
    updateUrl(url) {
        window.history.pushState({}, '', url);
    }

    /**
     * تحديث عنوان الصفحة
     */
    updatePageTitle(title) {
        document.title = `${title} - موقع إعلاني`;
    }

    /**
     * إظهار حالة التحميل
     */
    showLoadingState() {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'ad-loading-overlay';
        loadingOverlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
        loadingOverlay.style.cssText = 'background: rgba(0,0,0,0.5); z-index: 9999;';
        loadingOverlay.innerHTML = `
            <div class="text-center text-white">
                <div class="spinner-border mb-3" role="status"></div>
                <div>جاري تحميل الإعلان...</div>
            </div>
        `;
        document.body.appendChild(loadingOverlay);
    }

    /**
     * إخفاء حالة التحميل
     */
    hideLoadingState() {
        const loadingOverlay = document.getElementById('ad-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }

    /**
     * تحميل مسبق للإعلانات المجاورة
     */
    preloadAdjacentAds() {
        // يمكن تطوير هذا لاحقاً لتحسين الأداء
    }

    /**
     * إعادة تهيئة المكونات التفاعلية
     */
    reinitializeComponents() {
        // لا نحتاج لإعادة ربط أحداث المفضلة
        // لأن favorites.js يدير ذلك بالفعل
        // تجنب التكرار في event listeners

        // إعادة تهيئة المكونات الأخرى حسب الحاجة
        // (يمكن إضافة مكونات أخرى هنا لاحقاً)
    }
}

// تهيئة نظام التنقل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // التحقق من أننا في صفحة عرض إعلان
    if (window.location.pathname.includes('/ads/') && window.location.pathname.split('/').length >= 4) {
        window.adNavigation = new AdNavigation();
    }
});

// تصدير للاستخدام العام
window.AdNavigation = AdNavigation;
