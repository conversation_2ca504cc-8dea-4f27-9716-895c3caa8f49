<?php

namespace App\Http\Controllers;

use App\Services\SearchService;
use App\Services\CacheService;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * كونترولر البحث المتقدم
 * يدير جميع عمليات البحث والفلترة والاقتراحات
 */
class SearchController extends Controller
{
    /**
     * صفحة البحث المتقدم
     */
    public function index(Request $request)
    {
        // التحقق من صحة البيانات
        $request->validate([
            'q' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:100',
        ]);

        $query = $request->get('q', '');

        // تنظيف مدخلات البحث لحماية من XSS وSQL Injection
        if (!empty($query)) {
            $query = InputSanitizationService::sanitizeText($query);
        }

        // إنشاء paginator فارغ كافتراضي
        $results = new \Illuminate\Pagination\LengthAwarePaginator(
            collect(), 0, 12, 1, ['path' => request()->url()]
        );
        $totalResults = 0;

        if (!empty($query)) {
            // تصحيح الأخطاء الإملائية
            $correctedQuery = SearchService::correctSpelling($query);

            // البحث في الإعلانات
            $results = SearchService::searchAds($request->all());
            $totalResults = $results->total();

            // إذا لم توجد نتائج مع الكلمة الأصلية، جرب الكلمة المصححة
            if ($totalResults === 0 && $correctedQuery !== $query) {
                $request->merge(['q' => $correctedQuery]);
                $results = SearchService::searchAds($request->all());
                $totalResults = $results->total();

                // إشعار المستخدم بالتصحيح
                if ($totalResults > 0) {
                    session()->flash('search_correction', [
                        'original' => $query,
                        'corrected' => $correctedQuery
                    ]);
                }
            }
        }

        // الحصول على البيانات المساعدة
        $categories = CacheService::getActiveCategories();
        $popularSearches = SearchService::getPopularSearches();
        $recentSearches = SearchService::getRecentSearches();

        return view('search.index', compact(
            'query',
            'results',
            'totalResults',
            'categories',
            'popularSearches',
            'recentSearches'
        ));
    }

    /**
     * البحث السريع (AJAX)
     */
    public function quickSearch(Request $request): JsonResponse
    {
        // التحقق من صحة البيانات
        $request->validate([
            'q' => 'required|string|max:255',
        ]);

        $query = $request->get('q', '');

        // تنظيف مدخلات البحث لحماية من XSS
        $query = InputSanitizationService::sanitizeText($query);

        if (strlen($query) < 2) {
            return response()->json([
                'success' => false,
                'message' => 'يجب أن يكون البحث أكثر من حرفين'
            ]);
        }

        try {
            $results = SearchService::smartSearch($query, 8);

            return response()->json([
                'success' => true,
                'results' => $results,
                'query' => $query
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في البحث'
            ], 500);
        }
    }

    /**
     * اقتراحات البحث (AJAX)
     */
    public function suggestions(Request $request): JsonResponse
    {
        // التحقق من صحة البيانات
        $request->validate([
            'q' => 'required|string|max:255',
        ]);

        $query = $request->get('q', '');

        // تنظيف مدخلات البحث لحماية من XSS
        $query = InputSanitizationService::sanitizeText($query);

        if (strlen($query) < 2) {
            return response()->json([
                'suggestions' => []
            ]);
        }

        try {
            $suggestions = SearchService::getSearchSuggestions($query);

            return response()->json([
                'suggestions' => $suggestions,
                'query' => $query
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'suggestions' => []
            ]);
        }
    }

    /**
     * البحث المتقدم بالفلاتر
     */
    public function advanced(Request $request)
    {
        $filters = $request->only([
            'categories',
            'locations',
            'date_range',
            'views_range',
            'featured_only'
        ]);

        // إزالة الفلاتر الفارغة
        $filters = array_filter($filters, function($value) {
            return !empty($value);
        });

        // إنشاء paginator فارغ كافتراضي
        $results = new \Illuminate\Pagination\LengthAwarePaginator(
            collect(), 0, 12, 1, ['path' => request()->url()]
        );
        $totalResults = 0;

        if (!empty($filters)) {
            $results = SearchService::advancedFilter($filters);
            $totalResults = $results->total();
        }

        $categories = CacheService::getActiveCategories();
        $locations = $this->getPopularLocations();

        return view('search.advanced', compact(
            'results',
            'totalResults',
            'filters',
            'categories',
            'locations'
        ));
    }

    /**
     * البحث بالموقع الجغرافي
     */
    public function byLocation(Request $request)
    {
        $location = $request->get('location', '');
        $radius = $request->get('radius', 50);

        if (empty($location)) {
            return redirect()->route('search.index')
                ->with('error', 'يرجى تحديد الموقع للبحث');
        }

        $results = SearchService::searchByLocation($location, $radius);

        return view('search.location', compact(
            'results',
            'location',
            'radius'
        ));
    }

    /**
     * حفظ البحث المفضل
     */
    public function saveFavorite(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|max:255',
            'filters' => 'nullable|array'
        ]);

        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول لحفظ البحثات المفضلة'
            ], 401);
        }

        try {
            // حفظ البحث المفضل (يمكن إضافة جدول منفصل لاحقاً)
            $favorites = auth()->user()->favorites ?? [];
            $favorites[] = [
                'query' => $request->query,
                'filters' => $request->filters ?? [],
                'created_at' => now()->toISOString()
            ];

            auth()->user()->update(['favorites' => $favorites]);

            return response()->json([
                'success' => true,
                'message' => 'تم حفظ البحث في المفضلة'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في حفظ البحث'
            ], 500);
        }
    }

    /**
     * إحصائيات البحث (للإدارة)
     */
    public function stats()
    {
        $this->authorize('admin'); // التأكد من صلاحيات الإدارة

        $stats = SearchService::getSearchStats();

        return view('admin.search.stats', compact('stats'));
    }

    /**
     * تصدير نتائج البحث
     */
    public function export(Request $request)
    {
        $query = $request->get('q', '');
        $format = $request->get('format', 'csv');

        if (empty($query)) {
            return redirect()->back()
                ->with('error', 'يرجى إدخال مصطلح البحث');
        }

        $results = SearchService::searchAds($request->all());

        // تصدير النتائج (يمكن تطوير هذا لاحقاً)
        return response()->json([
            'message' => 'ميزة التصدير قيد التطوير',
            'results_count' => $results->total()
        ]);
    }

    /**
     * الحصول على المواقع الشائعة
     */
    private function getPopularLocations(): array
    {
        return cache()->remember('popular_locations', 3600, function () {
            return \DB::table('ads')
                ->select('location', \DB::raw('COUNT(*) as count'))
                ->whereNotNull('location')
                ->where('location', '!=', '')
                ->where('status', 'active')
                ->groupBy('location')
                ->orderBy('count', 'desc')
                ->limit(20)
                ->pluck('location')
                ->toArray();
        });
    }

    /**
     * مسح سجل البحث للمستخدم
     */
    public function clearHistory(): JsonResponse
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول'
            ], 401);
        }

        try {
            \DB::table('search_logs')
                ->where('user_id', auth()->id())
                ->delete();

            // مسح الكاش
            cache()->forget("recent_searches_" . auth()->id());

            return response()->json([
                'success' => true,
                'message' => 'تم مسح سجل البحث'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في مسح سجل البحث'
            ], 500);
        }
    }
}
