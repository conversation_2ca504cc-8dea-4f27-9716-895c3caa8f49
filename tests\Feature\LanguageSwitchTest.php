<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;

/**
 * اختبارات تبديل اللغة
 */
class LanguageSwitchTest extends TestCase
{
    use RefreshDatabase;

    /**
     * اختبار تبديل اللغة من العربية إلى الإنجليزية
     */
    public function test_can_switch_from_arabic_to_english()
    {
        // تعيين اللغة الحالية للعربية
        Session::put('locale', 'ar');
        App::setLocale('ar');

        // إرسال طلب تبديل للإنجليزية
        $response = $this->get(route('language.switch', 'en'));

        // التحقق من إعادة التوجيه
        $response->assertRedirect();

        // التحقق من رسالة النجاح
        $response->assertSessionHas('success', 'Language changed to English successfully');

        // التحقق من حفظ اللغة في الجلسة
        $this->assertEquals('en', Session::get('locale'));

        // التحقق من وجود Cookie
        $response->assertCookie('locale', 'en');
    }

    /**
     * اختبار تبديل اللغة من الإنجليزية إلى العربية
     */
    public function test_can_switch_from_english_to_arabic()
    {
        // تعيين اللغة الحالية للإنجليزية
        Session::put('locale', 'en');
        App::setLocale('en');

        // إرسال طلب تبديل للعربية
        $response = $this->get(route('language.switch', 'ar'));

        // التحقق من إعادة التوجيه
        $response->assertRedirect();

        // التحقق من رسالة النجاح
        $response->assertSessionHas('success', 'تم تغيير اللغة إلى العربية بنجاح');

        // التحقق من حفظ اللغة في الجلسة
        $this->assertEquals('ar', Session::get('locale'));

        // التحقق من وجود Cookie
        $response->assertCookie('locale', 'ar');
    }

    /**
     * اختبار رفض لغة غير مدعومة
     */
    public function test_rejects_unsupported_locale()
    {
        // إرسال طلب بلغة غير مدعومة
        $response = $this->get(route('language.switch', 'fr'));

        // التحقق من إعادة التوجيه
        $response->assertRedirect();

        // التحقق من تعيين اللغة الافتراضية (العربية)
        $this->assertEquals('ar', Session::get('locale'));

        // التحقق من وجود Cookie بالعربية
        $response->assertCookie('locale', 'ar');
    }

    /**
     * اختبار رفض مدخل ضار
     */
    public function test_rejects_malicious_input()
    {
        // إرسال طلب بمدخل ضار
        $response = $this->get(route('language.switch', '<script>alert("xss")</script>'));

        // التحقق من إعادة التوجيه
        $response->assertRedirect();

        // التحقق من تعيين اللغة الافتراضية (العربية)
        $this->assertEquals('ar', Session::get('locale'));

        // التحقق من وجود Cookie بالعربية
        $response->assertCookie('locale', 'ar');
    }



    /**
     * اختبار middleware SetLocale
     */
    public function test_set_locale_middleware_works()
    {
        // تعيين لغة في Cookie
        $response = $this->withCookie('locale', 'en')
                         ->get('/');

        // التحقق من تطبيق اللغة
        $this->assertEquals('en', App::getLocale());
    }

    /**
     * اختبار أولوية الجلسة على Cookie
     */
    public function test_session_takes_priority_over_cookie()
    {
        // تعيين لغة مختلفة في الجلسة والكوكي
        Session::put('locale', 'ar');

        $response = $this->withCookie('locale', 'en')
                         ->get('/');

        // التحقق من أن الجلسة لها الأولوية
        $this->assertEquals('ar', App::getLocale());
    }

    /**
     * اختبار Cookie الآمن
     */
    public function test_cookie_is_secure()
    {
        $response = $this->get(route('language.switch', 'en'));

        // التحقق من وجود Cookie مع الإعدادات الآمنة
        $response->assertCookie('locale', 'en');

        // يمكن إضافة المزيد من الاختبارات للتحقق من إعدادات الأمان
    }
}
