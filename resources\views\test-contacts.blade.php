@extends('layouts.app')

@section('title', 'اختبار معلومات التواصل')

@section('content')
<div class="container mt-5">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-vial me-2"></i>اختبار API معلومات التواصل</h3>
                    <p class="mb-0">المستخدم الحالي: {{ Auth::user()->name }} ({{ Auth::user()->email }})</p>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <h5>اختبار العمليات الأساسية</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="testGetContacts()">
                                    <i class="fas fa-download me-2"></i>جلب معلومات التواصل
                                </button>
                                <button class="btn btn-success" onclick="testCreateContact()">
                                    <i class="fas fa-plus me-2"></i>إنشاء معلومة تواصل
                                </button>
                                <button class="btn btn-warning" onclick="testUpdateContact()">
                                    <i class="fas fa-edit me-2"></i>تحديث معلومة تواصل
                                </button>
                                <button class="btn btn-danger" onclick="testDeleteContact()">
                                    <i class="fas fa-trash me-2"></i>حذف معلومة تواصل
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h5>اختبار العمليات المتقدمة</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-info" onclick="testTogglePublic()">
                                    <i class="fas fa-eye me-2"></i>تبديل حالة العرض
                                </button>
                                <button class="btn btn-secondary" onclick="testTogglePrimary()">
                                    <i class="fas fa-star me-2"></i>تبديل الأساسية
                                </button>
                                <button class="btn btn-outline-primary" onclick="testGetTypes()">
                                    <i class="fas fa-list me-2"></i>جلب أنواع التواصل
                                </button>
                                <button class="btn btn-outline-secondary" onclick="testGetPrivacyLevels()">
                                    <i class="fas fa-shield-alt me-2"></i>جلب مستويات الخصوصية
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h5>أدوات الاختبار</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-success" onclick="clearResults()">
                                    <i class="fas fa-broom me-2"></i>مسح النتائج
                                </button>
                                <button class="btn btn-outline-info" onclick="showCurrentUser()">
                                    <i class="fas fa-user me-2"></i>معلومات المستخدم
                                </button>
                                <button class="btn btn-outline-warning" onclick="testAllOperations()">
                                    <i class="fas fa-play me-2"></i>اختبار شامل
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>نتائج الاختبار:</h5>
                            <div>
                                <span id="test-status" class="badge bg-secondary">جاهز</span>
                                <span id="test-count" class="badge bg-info">0 اختبار</span>
                            </div>
                        </div>
                        <div id="results" class="border p-3 bg-light" style="height: 500px; overflow-y: auto; font-family: 'Courier New', monospace;">
                            <p class="text-muted">انقر على أي زر لبدء الاختبار...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const resultsDiv = document.getElementById('results');
    const statusSpan = document.getElementById('test-status');
    const countSpan = document.getElementById('test-count');
    let testCount = 0;
    let lastContactId = null;
    
    function updateStatus(status, type = 'secondary') {
        statusSpan.className = `badge bg-${type}`;
        statusSpan.textContent = status;
    }
    
    function updateCount() {
        testCount++;
        countSpan.textContent = `${testCount} اختبار`;
    }
    
    function log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            'error': 'text-danger',
            'success': 'text-success',
            'warning': 'text-warning',
            'info': 'text-info'
        };
        const color = colors[type] || 'text-dark';
        resultsDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
        resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }
    
    function clearResults() {
        resultsDiv.innerHTML = '<p class="text-muted">تم مسح النتائج...</p>';
        testCount = 0;
        updateCount();
        updateStatus('جاهز');
    }
    
    async function makeRequest(url, options = {}) {
        updateStatus('جاري التنفيذ...', 'warning');
        
        const defaultOptions = {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        if (finalOptions.headers && options.headers) {
            finalOptions.headers = { ...defaultOptions.headers, ...options.headers };
        }
        
        try {
            const response = await fetch(url, finalOptions);
            log(`📡 ${finalOptions.method || 'GET'} ${url} - Status: ${response.status}`);
            
            const data = await response.json();
            
            if (response.ok) {
                updateStatus('نجح', 'success');
                log('✅ الطلب نجح', 'success');
                log(JSON.stringify(data, null, 2));
                return data;
            } else {
                updateStatus('فشل', 'danger');
                log(`❌ فشل الطلب: ${data.message || 'خطأ غير معروف'}`, 'error');
                if (data.errors) {
                    log('🔍 أخطاء التحقق:', 'error');
                    log(JSON.stringify(data.errors, null, 2), 'error');
                }
                return data;
            }
        } catch (error) {
            updateStatus('خطأ', 'danger');
            log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            throw error;
        }
    }
    
    // تعريف الوظائف في النطاق العام
    window.testGetContacts = async function() {
        updateCount();
        log('🔄 بدء اختبار جلب معلومات التواصل...');
        
        const data = await makeRequest('/dashboard/contacts');
        if (data.success && data.data.length > 0) {
            lastContactId = data.data[0].id;
            log(`💾 تم حفظ ID أول معلومة تواصل: ${lastContactId}`, 'info');
        }
    };
    
    window.testCreateContact = async function() {
        updateCount();
        log('🔄 بدء اختبار إنشاء معلومة تواصل...');
        
        const testData = {
            contact_type: 'phone',
            contact_value: `+96777${Math.floor(Math.random() * 1000000).toString().padStart(7, '0')}`,
            display_label: 'هاتف اختبار',
            privacy_level: 'public',
            is_primary: false,
            is_public: true,
            display_order: 0
        };
        
        log('📤 البيانات المرسلة:');
        log(JSON.stringify(testData, null, 2));
        
        const data = await makeRequest('/dashboard/contacts', {
            method: 'POST',
            body: JSON.stringify(testData)
        });
        
        if (data.success && data.data) {
            lastContactId = data.data.id;
            log(`💾 تم حفظ ID معلومة التواصل الجديدة: ${lastContactId}`, 'info');
        }
    };
    
    window.testUpdateContact = async function() {
        if (!lastContactId) {
            log('❌ لا يوجد ID معلومة تواصل للتحديث. قم بجلب أو إنشاء معلومة أولاً.', 'error');
            return;
        }
        
        updateCount();
        log(`🔄 بدء اختبار تحديث معلومة التواصل ID: ${lastContactId}...`);
        
        const updateData = {
            contact_type: 'email',
            contact_value: `test${Math.floor(Math.random() * 1000)}@example.com`,
            display_label: 'إيميل محدث',
            privacy_level: 'registered_users',
            is_primary: true,
            is_public: true,
            display_order: 1
        };
        
        log('📤 البيانات المحدثة:');
        log(JSON.stringify(updateData, null, 2));
        
        await makeRequest(`/dashboard/contacts/${lastContactId}`, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
    };
    
    window.testDeleteContact = async function() {
        if (!lastContactId) {
            log('❌ لا يوجد ID معلومة تواصل للحذف. قم بجلب أو إنشاء معلومة أولاً.', 'error');
            return;
        }
        
        updateCount();
        log(`🔄 بدء اختبار حذف معلومة التواصل ID: ${lastContactId}...`);
        
        await makeRequest(`/dashboard/contacts/${lastContactId}`, {
            method: 'DELETE'
        });
        
        lastContactId = null;
        log('💾 تم مسح ID معلومة التواصل المحفوظ', 'info');
    };
    
    window.testTogglePublic = async function() {
        if (!lastContactId) {
            log('❌ لا يوجد ID معلومة تواصل. قم بجلب أو إنشاء معلومة أولاً.', 'error');
            return;
        }
        
        updateCount();
        log(`🔄 بدء اختبار تبديل حالة العرض لمعلومة التواصل ID: ${lastContactId}...`);
        
        await makeRequest(`/dashboard/contacts/${lastContactId}/toggle-public`, {
            method: 'POST'
        });
    };
    
    window.testTogglePrimary = async function() {
        if (!lastContactId) {
            log('❌ لا يوجد ID معلومة تواصل. قم بجلب أو إنشاء معلومة أولاً.', 'error');
            return;
        }
        
        updateCount();
        log(`🔄 بدء اختبار تبديل الأساسية لمعلومة التواصل ID: ${lastContactId}...`);
        
        await makeRequest(`/dashboard/contacts/${lastContactId}/toggle-primary`, {
            method: 'POST'
        });
    };
    
    window.testGetTypes = async function() {
        updateCount();
        log('🔄 بدء اختبار جلب أنواع التواصل...');
        
        await makeRequest('/dashboard/contacts/types/available');
    };
    
    window.testGetPrivacyLevels = async function() {
        updateCount();
        log('🔄 بدء اختبار جلب مستويات الخصوصية...');
        
        await makeRequest('/dashboard/contacts/privacy/levels');
    };
    
    window.showCurrentUser = function() {
        updateCount();
        log('👤 معلومات المستخدم الحالي:', 'info');
        log('الاسم: {{ Auth::user()->name }}', 'info');
        log('الإيميل: {{ Auth::user()->email }}', 'info');
        log('ID: {{ Auth::user()->id }}', 'info');
    };
    
    window.testAllOperations = async function() {
        log('🚀 بدء الاختبار الشامل لجميع العمليات...', 'warning');
        
        try {
            await testGetContacts();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCreateContact();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testUpdateContact();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testTogglePublic();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testTogglePrimary();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testGetTypes();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testGetPrivacyLevels();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDeleteContact();
            
            log('🎉 تم إكمال الاختبار الشامل بنجاح!', 'success');
            updateStatus('مكتمل', 'success');
            
        } catch (error) {
            log('❌ فشل الاختبار الشامل', 'error');
            updateStatus('فشل', 'danger');
        }
    };
    
    window.clearResults = clearResults;
    
    // تسجيل معلومات أولية
    log('🌐 صفحة اختبار معلومات التواصل جاهزة', 'success');
    log(`👤 المستخدم: {{ Auth::user()->name }} (ID: {{ Auth::user()->id }})`, 'info');
    log('📋 جاهز للاختبار!', 'success');
});
</script>
@endsection
