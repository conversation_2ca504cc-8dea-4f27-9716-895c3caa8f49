<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use App\Services\EncryptionService;
use App\Models\Interaction;
use App\Models\SystemLog;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * الحقول القابلة للتعبئة الجماعية - محدثة للهيكل الجديد
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'email_verified_at',

        // معلومات الأمان
        'password_changed_at',
        'last_login_at',
        'last_login_ip',
        'registration_ip',
        'failed_login_attempts',
        'locked_until',
        'force_password_change',
        'password_history',

        // معلومات الدفع
        'card_number',
        'card_type',
        'preferred_currency',

        // الصلاحيات
        'is_admin',
        'is_active',

        // معلومات التواصل
        'phone',
        'whatsapp',

        // إعدادات الخصوصية
        'privacy_settings',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'password_history',
        'last_login_ip',
        'registration_ip',
        'card_number',
        'registration_ip',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'password_changed_at' => 'datetime',
            'last_login_at' => 'datetime',
            'locked_until' => 'datetime',
            'password_history' => 'array',
            'privacy_settings' => 'array',
            'is_admin' => 'boolean',
            'is_active' => 'boolean',
            'force_password_change' => 'boolean',
        ];
    }

    /**
     * علاقة واحد إلى متعدد مع الإعلانات
     * مستخدم واحد يمكن أن يملك عدة إعلانات
     */
    public function ads(): HasMany
    {
        return $this->hasMany(Ad::class);
    }

    /**
     * الحصول على الإعلانات النشطة للمستخدم
     */
    public function activeAds(): HasMany
    {
        return $this->hasMany(Ad::class)->where('status', 'active')->notExpired();
    }

    /**
     * الحصول على الإعلانات المعلقة للمستخدم
     */
    public function pendingAds(): HasMany
    {
        return $this->hasMany(Ad::class)->where('status', 'pending');
    }

    /**
     * تشفير رقم البطاقة عند الحفظ
     */
    public function setCardNumberAttribute($value)
    {
        $this->attributes['card_number'] = EncryptionService::encryptText($value);
    }

    /**
     * فك تشفير رقم البطاقة عند الاستدعاء
     */
    public function getCardNumberAttribute($value)
    {
        return EncryptionService::decryptText($value);
    }

    /**
     * الحصول على رقم البطاقة مخفي للعرض
     */
    public function getMaskedCardNumberAttribute()
    {
        $cardNumber = $this->card_number;
        if (empty($cardNumber)) {
            return null;
        }

        if (strlen($cardNumber) <= 4) {
            return str_repeat('*', strlen($cardNumber));
        }

        return str_repeat('*', strlen($cardNumber) - 4) . substr($cardNumber, -4);
    }

    /**
     * الحصول على البريد الإلكتروني مخفي للعرض
     */
    public function getMaskedEmailAttribute()
    {
        return EncryptionService::maskEmail($this->email);
    }

    /**
     * التقييمات التي كتبها المستخدم (من النظام الجديد)
     */
    public function reviews()
    {
        return $this->hasMany(Interaction::class)->where('type', 'rating');
    }

    /**
     * التقييمات المكتوبة للمستخدم (من النظام الجديد)
     */
    public function receivedReviews()
    {
        return $this->hasMany(Interaction::class, 'target_user_id')->where('type', 'rating');
    }

    /**
     * التقييمات المعتمدة المكتوبة للمستخدم (من النظام الجديد)
     */
    public function approvedReceivedReviews()
    {
        return $this->receivedReviews(); // جميع التقييمات معتمدة في النظام الجديد
    }

    // تم تحديث علاقة الإشعارات في الأسفل

    /**
     * الحصول على متوسط تقييم المستخدم (من النظام الجديد)
     */
    public function getAverageRatingAttribute(): float
    {
        $ratings = $this->receivedReviews()
            ->get()
            ->pluck('data.rating')
            ->filter();

        return $ratings->count() > 0 ? round($ratings->average(), 2) : 0;
    }

    /**
     * الحصول على عدد التقييمات للمستخدم (من النظام الجديد)
     */
    public function getReceivedReviewsCountAttribute(): int
    {
        return $this->receivedReviews()->count();
    }

    /**
     * التحقق من انتهاء صلاحية كلمة المرور
     */
    public function isPasswordExpired(): bool
    {
        if (!$this->password_changed_at) {
            return true; // إذا لم يتم تسجيل تاريخ تغيير كلمة المرور
        }

        $maxAge = $this->is_admin ? 90 : 180; // 90 يوم للمديرين، 180 للمستخدمين العاديين

        return $this->password_changed_at->addDays($maxAge)->isPast();
    }

    /**
     * التحقق من قفل الحساب
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * قفل الحساب لفترة محددة
     */
    public function lockAccount(int $minutes = 15): void
    {
        $this->update([
            'locked_until' => now()->addMinutes($minutes),
            'failed_login_attempts' => $this->failed_login_attempts + 1,
        ]);
    }

    /**
     * إلغاء قفل الحساب
     */
    public function unlockAccount(): void
    {
        $this->update([
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);
    }

    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public function recordFailedLogin(): void
    {
        $attempts = $this->failed_login_attempts + 1;

        $this->update([
            'failed_login_attempts' => $attempts,
        ]);

        // قفل الحساب بعد 5 محاولات فاشلة
        if ($attempts >= 5) {
            $this->lockAccount(15); // قفل لمدة 15 دقيقة
        }
    }

    /**
     * تسجيل تسجيل دخول ناجح
     */
    public function recordSuccessfulLogin(string $ip): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip,
            'failed_login_attempts' => 0,
            'locked_until' => null,
        ]);
    }

    /**
     * إجبار تغيير كلمة المرور
     */
    public function forcePasswordChange(): void
    {
        $this->update([
            'force_password_change' => true,
        ]);
    }

    /**
     * التحقق من ضرورة تغيير كلمة المرور
     */
    public function mustChangePassword(): bool
    {
        return $this->force_password_change || $this->isPasswordExpired();
    }

    /**
     * تحديث كلمة المرور مع حفظ التاريخ
     */
    public function updatePassword(string $newPassword): void
    {
        // حفظ كلمة المرور القديمة في التاريخ (اختياري)
        $history = $this->password_history ?? [];
        if (count($history) >= 5) {
            array_shift($history); // إزالة أقدم كلمة مرور
        }
        $history[] = [
            'hash' => $this->password,
            'changed_at' => now()->toISOString(),
        ];

        $this->update([
            'password' => $newPassword,
            'password_changed_at' => now(),
            'force_password_change' => false,
            'password_history' => $history,
        ]);
    }

    /**
     * التحقق من إعادة استخدام كلمة المرور
     */
    public function isPasswordReused(string $newPassword): bool
    {
        $history = $this->password_history ?? [];

        foreach ($history as $entry) {
            if (password_verify($newPassword, $entry['hash'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * التحقق من دور المستخدم
     *
     * @param string $role
     * @return bool
     */
    public function hasRole(string $role): bool
    {
        // في هذا النظام، لدينا فقط دور admin
        if ($role === 'admin') {
            return $this->is_admin;
        }

        // يمكن إضافة أدوار أخرى في المستقبل
        return false;
    }

    /**
     * التحقق من كون المستخدم مدير
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->is_admin;
    }

    // تم نقل علاقة المفضلة إلى جدول interactions الموحد

    /**
     * الحصول على الإعلانات المفضلة للمستخدم من جدول interactions
     */
    public function favoriteAds()
    {
        return $this->hasManyThrough(
            Ad::class,
            Interaction::class,
            'user_id',
            'id',
            'id',
            'ad_id'
        )->where('interactions.type', 'favorite')
         ->orderBy('interactions.created_at', 'desc');
    }

    /**
     * الحصول على الإعلانات المفضلة النشطة فقط
     */
    public function activeFavoriteAds()
    {
        return $this->favoriteAds()
                    ->where('ads.status', 'active')
                    ->where(function ($query) {
                        $query->whereNull('ads.expires_at')
                              ->orWhere('ads.expires_at', '>', now());
                    });
    }

    // تم تحديث method hasFavorited في الأسفل

    // تم تحديث methods المفضلة في الأسفل للهيكل الجديد

    /**
     * تبديل حالة الإعلان في المفضلة
     */
    public function toggleFavorite(int $adId): array
    {
        $exists = $this->hasFavorited($adId);

        if ($exists) {
            $this->removeFromFavorites($adId);
            return [
                'success' => true,
                'action' => 'removed',
                'is_favorited' => false,
                'message' => 'تم إزالة الإعلان من المفضلة'
            ];
        } else {
            $this->addToFavorites($adId);
            return [
                'success' => true,
                'action' => 'added',
                'is_favorited' => true,
                'message' => 'تم إضافة الإعلان للمفضلة'
            ];
        }
    }

    /**
     * الحصول على عدد الإعلانات المفضلة
     */
    public function getFavoritesCountAttribute(): int
    {
        return $this->interactions()
                    ->where('type', 'favorite')
                    ->count();
    }

    /**
     * تسجيل عرض معلومات المستخدم المتقدمة
     */
    public function logAdvancedProfileView(): void
    {
        Log::info('عرض معلومات المستخدم المتقدمة', [
            'user_id' => $this->id,
            'user_name' => $this->name,
            'active_ads_count' => $this->activeAds()->count(),
            'average_rating' => $this->average_rating,
            'reviews_count' => $this->received_reviews_count,
            'favorites_count' => $this->favorites_count,
            'is_verified' => $this->is_verified,
            'viewer_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);
    }

    /**
     * الحصول على رابط الصورة الشخصية
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        // استخدام Gravatar كبديل
        $hash = md5(strtolower(trim($this->email)));
        return "https://www.gravatar.com/avatar/{$hash}?d=mp&s=200";
    }

    /**
     * الحصول على إحصائيات شاملة للمستخدم
     */
    public function getComprehensiveStats(): array
    {
        return [
            'total_ads' => $this->ads()->count(),
            'active_ads' => $this->activeAds()->count(),
            'pending_ads' => $this->pendingAds()->count(),
            'average_rating' => $this->average_rating,
            'reviews_count' => $this->received_reviews_count,
            'favorites_count' => $this->favorites_count,
            'join_year' => $this->created_at->format('Y'),
            'last_activity' => $this->last_seen_at ? $this->last_seen_at->diffForHumans() : null,
            'trust_indicators' => [
                'is_verified' => $this->is_verified,
                'phone_verified' => !is_null($this->phone_verified_at),
                'email_verified' => !is_null($this->email_verified_at),
                'is_admin' => $this->is_admin,
                'is_premium' => $this->is_premium ?? false,
            ]
        ];
    }

    /**
     * العلاقة مع معلومات التواصل
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(UserContact::class);
    }

    /**
     * الحصول على معلومات التواصل العامة مرتبة
     */
    public function publicContacts(): HasMany
    {
        return $this->hasMany(UserContact::class)
                    ->public()
                    ->ordered();
    }

    /**
     * الحصول على معلومات التواصل للمستخدمين المسجلين
     */
    public function contactsForRegisteredUsers(): HasMany
    {
        return $this->hasMany(UserContact::class)
                    ->forRegisteredUsers()
                    ->ordered();
    }

    /**
     * الحصول على معلومات التواصل الأساسية
     */
    public function primaryContacts(): HasMany
    {
        return $this->hasMany(UserContact::class)
                    ->where('is_primary', true)
                    ->ordered();
    }

    /**
     * الحصول على رقم الهاتف الأساسي من معلومات التواصل
     */
    public function getPrimaryPhoneAttribute(): ?string
    {
        $phoneContact = $this->contacts()
                           ->where('contact_type', 'phone')
                           ->where('is_primary', true)
                           ->first();

        return $phoneContact ? $phoneContact->contact_value : null;
    }

    /**
     * الحصول على الإيميل الأساسي من معلومات التواصل
     */
    public function getPrimaryEmailContactAttribute(): ?string
    {
        $emailContact = $this->contacts()
                           ->where('contact_type', 'email')
                           ->where('is_primary', true)
                           ->first();

        return $emailContact ? $emailContact->contact_value : null;
    }

    /**
     * الحصول على رقم الواتساب الأساسي
     */
    public function getPrimaryWhatsappAttribute(): ?string
    {
        $whatsappContact = $this->contacts()
                              ->where('contact_type', 'whatsapp')
                              ->where('is_primary', true)
                              ->first();

        return $whatsappContact ? $whatsappContact->contact_value : null;
    }

    /**
     * التحقق من وجود معلومات تواصل عامة
     */
    public function hasPublicContacts(): bool
    {
        return $this->contacts()->public()->exists();
    }

    /**
     * الحصول على عدد معلومات التواصل المتحقق منها
     */
    public function getVerifiedContactsCountAttribute(): int
    {
        return $this->contacts()->verified()->count();
    }

    /**
     * الحصول على إعدادات الخصوصية من الحقل المحسن الجديد
     * يستخدم privacy_settings JSON field بدلاً من جدول منفصل
     */
    public function getPrivacySettingsAttribute(): array
    {
        // الإعدادات الافتراضية
        $defaultSettings = [
            'show_phone' => true,
            'show_email' => true,
            'show_whatsapp' => true,
            'allow_contact_reveal' => true,
            'show_contacts_to_guests' => true,
            'show_contacts_to_registered' => true,
            'require_verification_to_view' => false,
            'auto_hide_unverified_contacts' => false,
            'contact_visibility_level' => 'public'
        ];

        // فك تشفير إعدادات الخصوصية من JSON field
        $settings = $this->attributes['privacy_settings'] ?? null;

        if (!$settings || empty($settings)) {
            return $defaultSettings;
        }

        if (is_string($settings)) {
            $decoded = json_decode($settings, true);
            if (json_last_error() !== JSON_ERROR_NONE || !is_array($decoded)) {
                // في حالة فشل فك التشفير، إرجاع الإعدادات الافتراضية
                return $defaultSettings;
            }
            // دمج الإعدادات المفكوكة مع الافتراضية
            return array_merge($defaultSettings, $decoded);
        }

        if (is_array($settings)) {
            // دمج الإعدادات الموجودة مع الافتراضية
            return array_merge($defaultSettings, $settings);
        }

        // في أي حالة أخرى، إرجاع الإعدادات الافتراضية
        return $defaultSettings;
    }

    /**
     * تحديث إعدادات الخصوصية
     */
    public function updatePrivacySettings(array $settings): bool
    {
        $currentSettings = $this->getPrivacySettingsAttribute();
        $newSettings = array_merge($currentSettings, $settings);

        $this->privacy_settings = json_encode($newSettings);
        return $this->save();
    }

    /**
     * التحقق من إمكانية عرض معلومات التواصل للمستخدم المحدد
     */
    public function canViewContactsBy(?User $viewer = null): bool
    {
        return $this->privacy_settings->canViewContacts($viewer);
    }

    /**
     * الحصول على معلومات التواصل المرئية للمستخدم المحدد
     */
    public function getVisibleContactsFor(?User $viewer = null): HasMany
    {
        $query = $this->contacts()->ordered();

        // إذا كان المشاهد هو صاحب المعلومات أو مدير، عرض كل شيء
        if ($viewer && ($viewer->id === $this->id || $viewer->is_admin)) {
            return $query;
        }

        // تطبيق إعدادات الخصوصية من النظام الجديد
        $privacySettings = $this->getPrivacySettingsAttribute();

        // التحقق من إمكانية عرض معلومات التواصل
        $canViewContacts = true;
        if (!$viewer) {
            // للضيوف
            $canViewContacts = $privacySettings['show_contacts_to_guests'] ?? true;
        } else {
            // للمستخدمين المسجلين
            $canViewContacts = $privacySettings['show_contacts_to_registered'] ?? true;
        }

        if (!$canViewContacts) {
            return $this->contacts()->where('id', 0); // إرجاع استعلام فارغ
        }

        // إخفاء المعلومات غير المتحقق منها إذا كان مفعلاً
        if ($privacySettings['auto_hide_unverified_contacts'] ?? false) {
            $query->where('is_verified', true);
        }

        // تطبيق مستويات الخصوصية
        $visibilityLevel = $privacySettings['contact_visibility_level'] ?? 'public';

        if (!$viewer) {
            // للضيوف: إظهار المعلومات العامة فقط
            $query->where('privacy_level', 'public');
        } elseif ($visibilityLevel === 'private') {
            return $this->contacts()->where('id', 0); // إرجاع استعلام فارغ
        } else {
            // للمستخدمين المسجلين: حسب مستوى الخصوصية
            $query->whereIn('privacy_level', ['public', 'registered_users']);
        }

        return $query;
    }

    /**
     * العلاقات الجديدة للهيكل المحسن
     */

    /**
     * علاقة مع التفاعلات (مفضلة، تقييمات، تعليقات)
     */
    public function interactions(): HasMany
    {
        return $this->hasMany(Interaction::class);
    }

    /**
     * الحصول على المفضلة
     */
    public function favorites(): HasMany
    {
        return $this->hasMany(Interaction::class)->where('type', 'favorite');
    }

    /**
     * الحصول على التقييمات المعطاة
     */
    public function givenRatings(): HasMany
    {
        return $this->hasMany(Interaction::class)->where('type', 'rating');
    }

    /**
     * الحصول على التعليقات
     */
    public function comments(): HasMany
    {
        return $this->hasMany(Interaction::class)->where('type', 'comment');
    }

    /**
     * علاقة مع الإشعارات
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * علاقة مع سجلات النظام
     */
    public function systemLogs(): HasMany
    {
        return $this->hasMany(SystemLog::class);
    }

    /**
     * التحقق من وجود إعلان في المفضلة
     */
    public function hasFavorited($adId): bool
    {
        return $this->interactions()
                    ->where('ad_id', $adId)
                    ->where('type', 'favorite')
                    ->exists();
    }

    /**
     * إضافة إعلان للمفضلة
     */
    public function addToFavorites($adId): void
    {
        $this->interactions()->updateOrCreate([
            'ad_id' => $adId,
            'type' => 'favorite'
        ]);
    }

    /**
     * إزالة إعلان من المفضلة
     */
    public function removeFromFavorites($adId): void
    {
        $this->interactions()
            ->where('ad_id', $adId)
            ->where('type', 'favorite')
            ->delete();
    }
}
