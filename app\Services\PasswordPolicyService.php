<?php

namespace App\Services;

/**
 * خدمة سياسات كلمات المرور
 * تدير التحقق من قوة كلمات المرور وتطبيق السياسات الأمنية
 */
class PasswordPolicyService
{
    /**
     * الحد الأدنى لطول كلمة المرور
     */
    const MIN_LENGTH = 12;

    /**
     * الحد الأدنى لطول كلمة المرور للمستخدمين العاديين
     */
    const MIN_LENGTH_REGULAR = 8;

    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePassword(string $password, bool $isAdmin = false): array
    {
        $errors = [];
        $minLength = $isAdmin ? self::MIN_LENGTH : self::MIN_LENGTH_REGULAR;

        // التحقق من الطول
        if (strlen($password) < $minLength) {
            $errors[] = "كلمة المرور يجب أن تكون {$minLength} أحرف على الأقل";
        }

        // التحقق من وجود حرف كبير
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }

        // التحقق من وجود حرف صغير
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }

        // التحقق من وجود رقم
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }

        // التحقق من وجود رمز خاص (للمديرين فقط)
        if ($isAdmin && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل (!@#$%^&*...)';
        }

        // التحقق من عدم وجود أنماط شائعة
        $commonPatterns = [
            '/123456/',
            '/password/i',
            '/admin/i',
            '/qwerty/i',
            '/abc/i',
        ];

        foreach ($commonPatterns as $pattern) {
            if (preg_match($pattern, $password)) {
                $errors[] = 'كلمة المرور تحتوي على نمط شائع وغير آمن';
                break;
            }
        }

        // التحقق من عدم تكرار الأحرف
        if (preg_match('/(.)\1{2,}/', $password)) {
            $errors[] = 'كلمة المرور لا يجب أن تحتوي على أحرف متكررة أكثر من مرتين';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => self::calculatePasswordStrength($password)
        ];
    }

    /**
     * حساب قوة كلمة المرور (من 0 إلى 100)
     */
    public static function calculatePasswordStrength(string $password): int
    {
        $score = 0;

        // نقاط للطول
        $length = strlen($password);
        if ($length >= 8) $score += 20;
        if ($length >= 12) $score += 10;
        if ($length >= 16) $score += 10;

        // نقاط للتنوع
        if (preg_match('/[a-z]/', $password)) $score += 10;
        if (preg_match('/[A-Z]/', $password)) $score += 10;
        if (preg_match('/[0-9]/', $password)) $score += 10;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 15;

        // نقاط للتعقيد
        $uniqueChars = count(array_unique(str_split($password)));
        if ($uniqueChars >= 8) $score += 10;
        if ($uniqueChars >= 12) $score += 5;

        // خصم نقاط للأنماط الضعيفة
        if (preg_match('/(.)\1{2,}/', $password)) $score -= 10;
        if (preg_match('/123|abc|qwe/i', $password)) $score -= 15;

        return max(0, min(100, $score));
    }

    /**
     * إنشاء كلمة مرور قوية
     */
    public static function generateSecurePassword(int $length = 16): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        
        $password = '';
        
        // ضمان وجود حرف من كل نوع
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];
        
        // إضافة باقي الأحرف عشوائياً
        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }
        
        // خلط الأحرف
        return str_shuffle($password);
    }

    /**
     * التحقق من انتهاء صلاحية كلمة المرور
     */
    public static function isPasswordExpired($user): bool
    {
        if (!$user->password_changed_at) {
            return true; // إذا لم يتم تسجيل تاريخ تغيير كلمة المرور
        }

        $maxAge = $user->is_admin ? 90 : 180; // 90 يوم للمديرين، 180 للمستخدمين العاديين
        
        return $user->password_changed_at->addDays($maxAge)->isPast();
    }

    /**
     * التحقق من إعادة استخدام كلمة المرور
     */
    public static function isPasswordReused(string $newPassword, $user): bool
    {
        // يمكن تطبيق هذا لاحقاً بحفظ hash لآخر 5 كلمات مرور
        // حالياً نرجع false
        return false;
    }

    /**
     * الحصول على رسائل المساعدة لكلمة المرور
     */
    public static function getPasswordHints(bool $isAdmin = false): array
    {
        $minLength = $isAdmin ? self::MIN_LENGTH : self::MIN_LENGTH_REGULAR;
        
        $hints = [
            "استخدم {$minLength} أحرف على الأقل",
            'اجمع بين الأحرف الكبيرة والصغيرة',
            'أضف أرقام',
            'تجنب المعلومات الشخصية',
            'تجنب الكلمات الشائعة',
        ];

        if ($isAdmin) {
            $hints[] = 'أضف رموز خاصة (!@#$%^&*...)';
            $hints[] = 'استخدم كلمة مرور فريدة للحساب الإداري';
        }

        return $hints;
    }

    /**
     * تقييم مستوى الأمان
     */
    public static function getSecurityLevel(int $strength): string
    {
        if ($strength < 30) return 'ضعيف جداً';
        if ($strength < 50) return 'ضعيف';
        if ($strength < 70) return 'متوسط';
        if ($strength < 85) return 'قوي';
        return 'قوي جداً';
    }

    /**
     * الحصول على لون مؤشر القوة
     */
    public static function getStrengthColor(int $strength): string
    {
        if ($strength < 30) return 'danger';
        if ($strength < 50) return 'warning';
        if ($strength < 70) return 'info';
        if ($strength < 85) return 'success';
        return 'primary';
    }
}
