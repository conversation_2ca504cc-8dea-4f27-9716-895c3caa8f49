@extends('layouts.app')

@section('title', __('Register') . ' - ' . __('site_name'))
@section('description', __('Create a new account to access all features'))

@section('content')
<!-- القسم الرئيسي للتسجيل -->
<section class="auth-section py-5" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); min-height: 100vh;">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-70">
            <div class="col-lg-6 col-md-8">
                <!-- بطاقة التسجيل -->
                <div class="auth-card">
                    <div class="auth-header text-center mb-4">
                        <div class="auth-icon mb-3">
                            <i class="fas fa-user-plus fa-3x text-success"></i>
                        </div>
                        <h2 class="arabic-text fw-bold text-dark mb-2">{{ __('Create New Account') }}</h2>
                        <p class="text-muted arabic-text">{{ __('Join us and enjoy all features') }}</p>
                    </div>

                    <!-- رسائل الأخطاء -->
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li class="arabic-text">{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- نموذج التسجيل -->
                    <form method="POST" action="{{ route('auth.register.submit') }}" class="auth-form">
                        @csrf
                        
                        <div class="row">
                            <!-- الاسم الكامل -->
                            <div class="col-12 mb-3">
                                <label for="name" class="form-label arabic-text fw-bold">
                                    <i class="fas fa-user me-2 text-success"></i>
                                    {{ __('Full Name') }}
                                </label>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name') }}" 
                                       placeholder="{{ __('Enter your full name') }}"
                                       required 
                                       autofocus>
                                @error('name')
                                    <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-12 mb-3">
                                <label for="email" class="form-label arabic-text fw-bold">
                                    <i class="fas fa-envelope me-2 text-success"></i>
                                    {{ __('Email') }}
                                </label>
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email') }}" 
                                       placeholder="{{ __('Enter your email') }}"
                                       required>
                                @error('email')
                                    <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- كلمة المرور -->
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label arabic-text fw-bold">
                                    <i class="fas fa-lock me-2 text-success"></i>
                                    {{ __('Password') }}
                                </label>
                                <div class="password-input-group">
                                    <input type="password" 
                                           class="form-control @error('password') is-invalid @enderror" 
                                           id="password" 
                                           name="password" 
                                           placeholder="{{ __('Enter password') }}"
                                           required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="password-icon"></i>
                                    </button>
                                </div>
                                @error('password')
                                    <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- تأكيد كلمة المرور -->
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label arabic-text fw-bold">
                                    <i class="fas fa-lock me-2 text-success"></i>
                                    {{ __('Confirm Password') }}
                                </label>
                                <div class="password-input-group">
                                    <input type="password" 
                                           class="form-control" 
                                           id="password_confirmation" 
                                           name="password_confirmation" 
                                           placeholder="{{ __('Confirm password') }}"
                                           required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                                        <i class="fas fa-eye" id="password_confirmation-icon"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- رقم البطاقة -->
                            <div class="col-md-6 mb-3">
                                <label for="card_number" class="form-label arabic-text fw-bold">
                                    <i class="fas fa-credit-card me-2 text-success"></i>
                                    {{ __('Card Number') }}
                                    <small class="text-muted">({{ __('Optional') }})</small>
                                </label>
                                <input type="text" 
                                       class="form-control @error('card_number') is-invalid @enderror" 
                                       id="card_number" 
                                       name="card_number" 
                                       value="{{ old('card_number') }}" 
                                       placeholder="{{ __('Enter card number') }}">
                                @error('card_number')
                                    <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- نوع البطاقة -->
                            <div class="col-md-6 mb-3">
                                <label for="card_type" class="form-label arabic-text fw-bold">
                                    <i class="fas fa-university me-2 text-success"></i>
                                    {{ __('Card Type') }}
                                    <small class="text-muted">({{ __('Optional') }})</small>
                                </label>
                                <select class="form-select @error('card_type') is-invalid @enderror" 
                                        id="card_type" 
                                        name="card_type">
                                    <option value="">{{ __('Select card type') }}</option>
                                    <option value="كاك بنك" {{ old('card_type') == 'كاك بنك' ? 'selected' : '' }}>{{ __('CAC Bank') }}</option>
                                    <option value="الكريمي" {{ old('card_type') == 'الكريمي' ? 'selected' : '' }}>{{ __('Al-Kuraimi Bank') }}</option>
                                    <option value="بنك اليمن الدولي" {{ old('card_type') == 'بنك اليمن الدولي' ? 'selected' : '' }}>{{ __('Yemen International Bank') }}</option>
                                    <option value="البنك الأهلي" {{ old('card_type') == 'البنك الأهلي' ? 'selected' : '' }}>{{ __('Al-Ahli Bank') }}</option>
                                    <option value="بنك التضامن" {{ old('card_type') == 'بنك التضامن' ? 'selected' : '' }}>{{ __('Tadhamon Bank') }}</option>
                                    <option value="أخرى" {{ old('card_type') == 'أخرى' ? 'selected' : '' }}>{{ __('Other') }}</option>
                                </select>
                                @error('card_type')
                                    <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- نوع العملة -->
                            <div class="col-12 mb-4">
                                <label for="currency" class="form-label arabic-text fw-bold">
                                    <i class="fas fa-coins me-2 text-success"></i>
                                    {{ __('Preferred Currency') }}
                                </label>
                                <div class="currency-options">
                                    <div class="row g-2">
                                        <div class="col-md-4">
                                            <div class="form-check currency-check">
                                                <input class="form-check-input" type="radio" name="currency" id="currency_yer" value="YER" {{ old('currency', 'YER') == 'YER' ? 'checked' : '' }}>
                                                <label class="form-check-label arabic-text" for="currency_yer">
                                                    <i class="fas fa-coins text-warning me-2"></i>
                                                    {{ __('Yemeni Rial') }}
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check currency-check">
                                                <input class="form-check-input" type="radio" name="currency" id="currency_usd" value="USD" {{ old('currency') == 'USD' ? 'checked' : '' }}>
                                                <label class="form-check-label arabic-text" for="currency_usd">
                                                    <i class="fas fa-dollar-sign text-success me-2"></i>
                                                    {{ __('US Dollar') }}
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check currency-check">
                                                <input class="form-check-input" type="radio" name="currency" id="currency_sar" value="SAR" {{ old('currency') == 'SAR' ? 'checked' : '' }}>
                                                <label class="form-check-label arabic-text" for="currency_sar">
                                                    <i class="fas fa-coins text-info me-2"></i>
                                                    {{ __('Saudi Riyal') }}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @error('currency')
                                    <div class="invalid-feedback arabic-text d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- زر التسجيل -->
                        <div class="form-group mb-4">
                            <button type="submit" class="btn btn-success btn-lg w-100 arabic-text fw-bold">
                                <i class="fas fa-user-plus me-2"></i>
                                {{ __('Create Account') }}
                            </button>
                        </div>

                        <!-- روابط إضافية -->
                        <div class="auth-links text-center">
                            <p class="text-muted arabic-text mb-3">
                                {{ __('Already have an account?') }}
                                <a href="{{ route('login') }}" class="text-success fw-bold text-decoration-none">
                                    {{ __('Login here') }}
                                </a>
                            </p>
                            
                            <!-- روابط التنقل -->
                            <div class="navigation-links">
                                <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm me-2">
                                    <i class="fas fa-home me-1"></i>
                                    {{ __('Home') }}
                                </a>
                                <a href="{{ route('action.choice') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    {{ __('Back') }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- معلومات إضافية -->
                <div class="auth-info mt-4 text-center">
                    <div class="info-card">
                        <h6 class="text-white arabic-text fw-bold mb-2">
                            <i class="fas fa-shield-alt me-2"></i>
                            {{ __('Secure Registration') }}
                        </h6>
                        <p class="text-white-50 arabic-text small mb-0">
                            {{ __('Your personal information is protected and secure') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق خاص بصفحة التسجيل */
.auth-section {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.currency-check {
    background: rgba(40, 167, 69, 0.1);
    border-radius: 10px;
    padding: 0.75rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.currency-check:hover {
    background: rgba(40, 167, 69, 0.2);
    transform: translateY(-2px);
}

.currency-check input:checked + label {
    color: #28a745;
    font-weight: bold;
}

.currency-check input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.currency-check input:checked ~ .currency-check {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.2);
}

.form-select {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    border-radius: 15px;
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
    background: linear-gradient(45deg, #218838, #1ea085);
}

/* تحسينات إضافية للاستجابة */
@media (max-width: 768px) {
    .currency-options .col-md-4 {
        margin-bottom: 0.5rem;
    }
    
    .currency-check {
        padding: 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// دالة إظهار/إخفاء كلمة المرور (نفس الدالة من صفحة تسجيل الدخول)
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تركيز تلقائي على حقل الاسم
    const nameInput = document.getElementById('name');
    if (nameInput && !nameInput.value) {
        nameInput.focus();
    }
    
    // إضافة تأثيرات للحقول
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // تحسين تجربة اختيار العملة
    const currencyInputs = document.querySelectorAll('input[name="currency"]');
    currencyInputs.forEach(input => {
        input.addEventListener('change', function() {
            // إزالة التأثير من جميع الخيارات
            document.querySelectorAll('.currency-check').forEach(check => {
                check.style.borderColor = 'transparent';
                check.style.background = 'rgba(40, 167, 69, 0.1)';
            });
            
            // إضافة التأثير للخيار المحدد
            if (this.checked) {
                const parentCheck = this.closest('.currency-check');
                parentCheck.style.borderColor = '#28a745';
                parentCheck.style.background = 'rgba(40, 167, 69, 0.2)';
            }
        });
    });
    
    // تطبيق التأثير على العملة المحددة مسبقاً
    const checkedCurrency = document.querySelector('input[name="currency"]:checked');
    if (checkedCurrency) {
        const parentCheck = checkedCurrency.closest('.currency-check');
        parentCheck.style.borderColor = '#28a745';
        parentCheck.style.background = 'rgba(40, 167, 69, 0.2)';
    }
});
</script>
@endpush
