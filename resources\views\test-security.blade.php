@extends('layouts.app')

@section('title', 'اختبار نظام الأمان والخصوصية')

@section('content')
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-shield-alt me-2"></i>اختبار نظام الأمان والخصوصية</h3>
                    <p class="mb-0">المستخدم: {{ Auth::user()->name }} ({{ Auth::user()->email }})</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- إعدادات الخصوصية -->
                        <div class="col-md-4">
                            <h5><i class="fas fa-cog me-2"></i>إعدادات الخصوصية</h5>
                            <div class="d-grid gap-2 mb-4">
                                <button class="btn btn-primary" onclick="testGetPrivacySettings()">
                                    <i class="fas fa-download me-2"></i>جلب إعدادات الخصوصية
                                </button>
                                <button class="btn btn-warning" onclick="testUpdatePrivacySettings()">
                                    <i class="fas fa-edit me-2"></i>تحديث إعدادات الخصوصية
                                </button>
                                <button class="btn btn-secondary" onclick="testResetPrivacySettings()">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين للافتراضية
                                </button>
                            </div>
                        </div>

                        <!-- إحصائيات الأمان -->
                        <div class="col-md-4">
                            <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات الأمان</h5>
                            <div class="d-grid gap-2 mb-4">
                                <button class="btn btn-info" onclick="testGetSecurityStats()">
                                    <i class="fas fa-chart-line me-2"></i>إحصائيات الأمان
                                </button>
                                <button class="btn btn-danger" onclick="testGetSuspiciousActivities()">
                                    <i class="fas fa-exclamation-triangle me-2"></i>النشاطات المشبوهة
                                </button>
                                <button class="btn btn-dark" onclick="testContactAccess()">
                                    <i class="fas fa-eye me-2"></i>محاكاة وصول لمعلومات التواصل
                                </button>
                            </div>
                        </div>

                        <!-- إدارة الحظر (للمدير فقط) -->
                        <div class="col-md-4">
                            <h5><i class="fas fa-ban me-2"></i>إدارة الحظر</h5>
                            @if(Auth::user()->is_admin)
                                <div class="d-grid gap-2 mb-4">
                                    <button class="btn btn-danger" onclick="testBlockIp()">
                                        <i class="fas fa-ban me-2"></i>حظر IP
                                    </button>
                                    <button class="btn btn-success" onclick="testUnblockIp()">
                                        <i class="fas fa-check me-2"></i>إلغاء حظر IP
                                    </button>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-info-circle me-2"></i>
                                    هذه الوظائف متاحة للمدير فقط
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- عرض إعدادات الخصوصية الحالية -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>إعدادات الخصوصية الحالية:</h5>
                            <div id="privacy-settings-display" class="border p-3 bg-light">
                                <p class="text-muted">جاري التحميل...</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>إحصائيات الأمان:</h5>
                            <div id="security-stats-display" class="border p-3 bg-light">
                                <p class="text-muted">انقر على "إحصائيات الأمان" لعرض البيانات</p>
                            </div>
                        </div>
                    </div>

                    <!-- سجل الأحداث -->
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>سجل الأحداث:</h5>
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearLog()">
                                <i class="fas fa-broom me-1"></i>مسح السجل
                            </button>
                        </div>
                        <div id="log-output" class="border p-3 bg-dark text-light" style="height: 400px; overflow-y: auto; font-family: monospace;">
                            <div class="text-success">[جاهز] صفحة اختبار الأمان والخصوصية محملة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const logOutput = document.getElementById('log-output');
    const privacyDisplay = document.getElementById('privacy-settings-display');
    const securityDisplay = document.getElementById('security-stats-display');
    
    function log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            'error': 'text-danger',
            'success': 'text-success',
            'warning': 'text-warning',
            'info': 'text-info'
        };
        const color = colors[type] || 'text-light';
        logOutput.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
        logOutput.scrollTop = logOutput.scrollHeight;
    }
    
    function clearLog() {
        logOutput.innerHTML = '<div class="text-success">[مسح] تم مسح السجل</div>';
    }
    
    async function makeRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        if (finalOptions.headers && options.headers) {
            finalOptions.headers = { ...defaultOptions.headers, ...options.headers };
        }
        
        try {
            log(`📡 ${finalOptions.method || 'GET'} ${url}`, 'info');
            const response = await fetch(url, finalOptions);
            
            const data = await response.json();
            
            if (response.ok) {
                log(`✅ نجح الطلب: ${data.message}`, 'success');
                return data;
            } else {
                log(`❌ فشل الطلب: ${data.message}`, 'error');
                if (data.errors) {
                    log(`🔍 أخطاء: ${JSON.stringify(data.errors)}`, 'error');
                }
                return data;
            }
        } catch (error) {
            log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            throw error;
        }
    }
    
    // تحميل إعدادات الخصوصية عند تحميل الصفحة
    testGetPrivacySettings();
    
    // تعريف الوظائف في النطاق العام
    window.testGetPrivacySettings = async function() {
        log('🔄 جلب إعدادات الخصوصية...', 'info');
        
        try {
            const data = await makeRequest('/dashboard/privacy');
            if (data.success) {
                const settings = data.data;
                let html = '<div class="row">';
                
                // عرض الإعدادات الأساسية
                html += `
                    <div class="col-12 mb-2">
                        <strong>إعدادات العرض:</strong><br>
                        <small>عرض للزوار: ${settings.show_contacts_to_guests ? '✅' : '❌'}</small><br>
                        <small>عرض للمسجلين: ${settings.show_contacts_to_registered ? '✅' : '❌'}</small><br>
                        <small>يتطلب تحقق: ${settings.require_verification_to_view ? '✅' : '❌'}</small>
                    </div>
                    <div class="col-12 mb-2">
                        <strong>حدود الوصول:</strong><br>
                        <small>تحديد المعدل: ${settings.enable_contact_rate_limiting ? '✅' : '❌'}</small><br>
                        <small>حد الساعة: ${settings.max_contact_views_per_hour}</small><br>
                        <small>حد اليوم: ${settings.max_contact_views_per_day}</small>
                    </div>
                    <div class="col-12">
                        <strong>الأمان:</strong><br>
                        <small>تشفير: ${settings.enable_contact_encryption ? '✅' : '❌'}</small><br>
                        <small>علامة مائية: ${settings.enable_watermark ? '✅' : '❌'}</small><br>
                        <small>تتبع التفاعلات: ${settings.track_contact_interactions ? '✅' : '❌'}</small>
                    </div>
                `;
                html += '</div>';
                
                privacyDisplay.innerHTML = html;
            }
        } catch (error) {
            privacyDisplay.innerHTML = '<p class="text-danger">فشل في تحميل إعدادات الخصوصية</p>';
        }
    };
    
    window.testUpdatePrivacySettings = async function() {
        log('🔄 تحديث إعدادات الخصوصية...', 'info');
        
        const updateData = {
            show_contacts_to_guests: true,
            show_contacts_to_registered: true,
            require_verification_to_view: false,
            enable_contact_rate_limiting: true,
            max_contact_views_per_hour: 15,
            max_contact_views_per_day: 75,
            notify_on_suspicious_activity: true,
            track_contact_interactions: true,
            log_contact_access: true
        };
        
        log(`📤 البيانات المرسلة: ${JSON.stringify(updateData)}`, 'info');
        
        try {
            const data = await makeRequest('/dashboard/privacy', {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
            
            if (data.success) {
                testGetPrivacySettings(); // إعادة تحميل الإعدادات
            }
        } catch (error) {
            log(`❌ فشل التحديث: ${error.message}`, 'error');
        }
    };
    
    window.testResetPrivacySettings = async function() {
        log('🔄 إعادة تعيين إعدادات الخصوصية...', 'info');
        
        try {
            const data = await makeRequest('/dashboard/privacy/reset', {
                method: 'POST'
            });
            
            if (data.success) {
                testGetPrivacySettings(); // إعادة تحميل الإعدادات
            }
        } catch (error) {
            log(`❌ فشل إعادة التعيين: ${error.message}`, 'error');
        }
    };
    
    window.testGetSecurityStats = async function() {
        log('🔄 جلب إحصائيات الأمان...', 'info');
        
        try {
            const data = await makeRequest('/dashboard/privacy/security-stats?days=30');
            if (data.success) {
                const stats = data.data;
                let html = `
                    <div class="row">
                        <div class="col-6">
                            <strong>إجمالي الوصولات:</strong><br>
                            <span class="badge bg-primary">${stats.total_accesses || 0}</span>
                        </div>
                        <div class="col-6">
                            <strong>زوار فريدون:</strong><br>
                            <span class="badge bg-info">${stats.unique_visitors || 0}</span>
                        </div>
                        <div class="col-6 mt-2">
                            <strong>مستخدمون مسجلون:</strong><br>
                            <span class="badge bg-success">${stats.registered_users || 0}</span>
                        </div>
                        <div class="col-6 mt-2">
                            <strong>نشاطات مشبوهة:</strong><br>
                            <span class="badge bg-danger">${stats.suspicious_activities || 0}</span>
                        </div>
                    </div>
                `;
                
                if (stats.access_types && Object.keys(stats.access_types).length > 0) {
                    html += '<hr><strong>أنواع الوصول:</strong><br>';
                    Object.entries(stats.access_types).forEach(([type, count]) => {
                        html += `<span class="badge bg-secondary me-1">${type}: ${count}</span>`;
                    });
                }
                
                securityDisplay.innerHTML = html;
            }
        } catch (error) {
            securityDisplay.innerHTML = '<p class="text-danger">فشل في تحميل إحصائيات الأمان</p>';
        }
    };
    
    window.testGetSuspiciousActivities = async function() {
        log('🔄 جلب النشاطات المشبوهة...', 'info');
        
        try {
            const data = await makeRequest('/dashboard/privacy/suspicious-activities?limit=5');
            if (data.success) {
                log(`📊 تم العثور على ${data.data.length} نشاط مشبوه`, 'warning');
                data.data.forEach((activity, index) => {
                    log(`🚨 نشاط ${index + 1}: ${activity.access_type} من ${activity.ip_address} - ${activity.suspicious_reason}`, 'warning');
                });
            }
        } catch (error) {
            log(`❌ فشل جلب النشاطات المشبوهة: ${error.message}`, 'error');
        }
    };
    
    window.testContactAccess = async function() {
        log('🔄 محاكاة وصول لمعلومات التواصل...', 'info');
        
        // محاكاة عدة طلبات للوصول
        for (let i = 1; i <= 3; i++) {
            setTimeout(async () => {
                try {
                    await makeRequest('/dashboard/contacts');
                    log(`📞 محاكاة وصول ${i} - تم بنجاح`, 'info');
                } catch (error) {
                    log(`❌ فشل محاكاة الوصول ${i}: ${error.message}`, 'error');
                }
            }, i * 1000);
        }
    };
    
    @if(Auth::user()->is_admin)
    window.testBlockIp = async function() {
        const testIp = '*************'; // IP تجريبي
        log(`🔄 حظر IP تجريبي: ${testIp}...`, 'info');
        
        try {
            const data = await makeRequest('/dashboard/privacy/block-ip', {
                method: 'POST',
                body: JSON.stringify({
                    ip_address: testIp,
                    minutes: 30,
                    reason: 'اختبار حظر من صفحة الاختبار'
                })
            });
        } catch (error) {
            log(`❌ فشل حظر IP: ${error.message}`, 'error');
        }
    };
    
    window.testUnblockIp = async function() {
        const testIp = '*************'; // نفس IP التجريبي
        log(`🔄 إلغاء حظر IP: ${testIp}...`, 'info');
        
        try {
            const data = await makeRequest('/dashboard/privacy/unblock-ip', {
                method: 'POST',
                body: JSON.stringify({
                    ip_address: testIp
                })
            });
        } catch (error) {
            log(`❌ فشل إلغاء حظر IP: ${error.message}`, 'error');
        }
    };
    @endif
    
    window.clearLog = clearLog;
    
    // معلومات أولية
    log('🛡️ نظام الأمان والخصوصية جاهز للاختبار', 'success');
    log(`👤 المستخدم: {{ Auth::user()->name }} ({{ Auth::user()->is_admin ? 'مدير' : 'مستخدم عادي' }})`, 'info');
});
</script>
@endsection
