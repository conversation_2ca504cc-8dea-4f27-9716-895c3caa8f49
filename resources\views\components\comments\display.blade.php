{{-- مكون عرض التعليقات والردود المتقدم --}}
@props(['ad', 'detailed' => true, 'showForm' => true, 'limit' => null])

{{-- إضافة Logging لعرض التعليقات --}}
@php
    if ($detailed) {
        Log::info('عرض التعليقات والردود المتقدمة', [
            'ad_id' => $ad->id,
            'ad_title' => $ad->title,
            'comments_count' => $ad->comments_count,
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'timestamp' => now()
        ]);
    }
    
    // الحصول على التعليقات مع الردود
    $comments = $ad->getCommentsWithReplies($limit ?? 10);
    $totalComments = $ad->comments_count;
@endphp

<div class="comments-section {{ $detailed ? 'comments-detailed' : 'comments-compact' }}">
    {{-- رأس قسم التعليقات --}}
    <div class="comments-header">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="comments-title">
                <i class="fas fa-comments text-primary me-2"></i>
                {{ __('Comments & Discussion') }}
                <span class="comments-count">({{ $totalComments }})</span>
            </h4>
            
            @if($showForm && auth()->check())
                <button class="btn btn-outline-primary btn-sm" onclick="CommentsManager.showAddCommentForm()">
                    <i class="fas fa-plus me-1"></i>
                    {{ __('Add Comment') }}
                </button>
            @endif
        </div>
    </div>

    {{-- نموذج إضافة تعليق --}}
    @if($showForm && auth()->check())
        <div class="add-comment-form" id="addCommentForm" style="display: none;">
            <div class="comment-form-card">
                <h5 class="mb-3">
                    <i class="fas fa-edit me-2"></i>
                    {{ __('Add Your Comment') }}
                </h5>
                
                <form id="commentForm" onsubmit="CommentsManager.submitComment(event)">
                    @csrf
                    <input type="hidden" name="ad_id" value="{{ $ad->id }}">
                    <input type="hidden" name="parent_id" id="parentCommentId" value="">
                    
                    {{-- نص التعليق --}}
                    <div class="mb-3">
                        <label for="commentContent" class="form-label">{{ __('Your Comment') }}</label>
                        <textarea class="form-control" id="commentContent" name="content" rows="4" 
                                  placeholder="{{ __('Share your thoughts about this ad') }}" 
                                  maxlength="1000" required></textarea>
                        <div class="form-text">{{ __('Maximum 1000 characters') }}</div>
                    </div>
                    
                    {{-- أزرار التحكم --}}
                    <div class="comment-form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {{ __('Submit Comment') }}
                        </button>
                        
                        <button type="button" class="btn btn-outline-secondary ms-2" 
                                onclick="CommentsManager.hideAddCommentForm()">
                            <i class="fas fa-times me-1"></i>
                            {{ __('Cancel') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    @endif

    {{-- قائمة التعليقات --}}
    <div class="comments-list">
        @if($comments->count() > 0)
            @foreach($comments as $comment)
                <div class="comment-item" data-comment-id="{{ $comment->id }}">
                    <div class="comment-header">
                        <div class="commenter-info">
                            <div class="commenter-avatar">
                                @if($comment->user->avatar)
                                    <img src="{{ $comment->user->avatar_url }}" alt="{{ $comment->user->name }}" class="avatar-sm">
                                @else
                                    <div class="avatar-placeholder-sm">
                                        <i class="fas fa-user"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="commenter-details">
                                <div class="commenter-name">
                                    {{ $comment->user->name }}
                                    @if($comment->user->is_verified)
                                        <i class="fas fa-check-circle text-success ms-1" title="{{ __('Verified User') }}"></i>
                                    @endif
                                    @if($comment->user->id === $ad->user_id)
                                        <span class="badge badge-owner ms-1">{{ __('Ad Owner') }}</span>
                                    @endif
                                </div>
                                
                                <div class="comment-meta">
                                    <span class="comment-date">{{ $comment->created_at->diffForHumans() }}</span>
                                    @if($comment->updated_at != $comment->created_at)
                                        <span class="comment-edited">({{ __('edited') }})</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div class="comment-actions">
                            @auth
                                <button class="btn btn-sm btn-outline-secondary" 
                                        onclick="CommentsManager.toggleLike({{ $comment->id }})">
                                    <i class="fas fa-thumbs-up me-1"></i>
                                    {{ __('Like') }} ({{ $comment->likes_count ?? 0 }})
                                </button>
                                
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="CommentsManager.showReplyForm({{ $comment->id }})">
                                    <i class="fas fa-reply me-1"></i>
                                    {{ __('Reply') }}
                                </button>
                                
                                @if(auth()->id() === $comment->user_id || auth()->user()->is_admin)
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            @if(auth()->id() === $comment->user_id)
                                                <li>
                                                    <a class="dropdown-item" href="#" 
                                                       onclick="CommentsManager.editComment({{ $comment->id }})">
                                                        <i class="fas fa-edit me-2"></i>{{ __('Edit') }}
                                                    </a>
                                                </li>
                                            @endif
                                            <li>
                                                <a class="dropdown-item text-danger" href="#" 
                                                   onclick="CommentsManager.deleteComment({{ $comment->id }})">
                                                    <i class="fas fa-trash me-2"></i>{{ __('Delete') }}
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item" href="#" 
                                                   onclick="CommentsManager.reportComment({{ $comment->id }})">
                                                    <i class="fas fa-flag me-2"></i>{{ __('Report') }}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                @else
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="#" 
                                                   onclick="CommentsManager.reportComment({{ $comment->id }})">
                                                    <i class="fas fa-flag me-2"></i>{{ __('Report') }}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                @endif
                            @endauth
                        </div>
                    </div>
                    
                    <div class="comment-content">
                        <p class="comment-text">{{ $comment->content }}</p>
                    </div>
                    
                    {{-- نموذج الرد --}}
                    <div class="reply-form" id="replyForm{{ $comment->id }}" style="display: none;">
                        <form onsubmit="CommentsManager.submitReply(event, {{ $comment->id }})">
                            @csrf
                            <input type="hidden" name="ad_id" value="{{ $ad->id }}">
                            <input type="hidden" name="parent_id" value="{{ $comment->id }}">
                            
                            <div class="mb-3">
                                <textarea class="form-control" name="content" rows="3" 
                                          placeholder="{{ __('Write your reply...') }}" 
                                          maxlength="500" required></textarea>
                            </div>
                            
                            <div class="reply-form-actions">
                                <button type="submit" class="btn btn-sm btn-primary">
                                    <i class="fas fa-reply me-1"></i>
                                    {{ __('Reply') }}
                                </button>
                                
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" 
                                        onclick="CommentsManager.hideReplyForm({{ $comment->id }})">
                                    {{ __('Cancel') }}
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    {{-- الردود --}}
                    @if($comment->replies && $comment->replies->count() > 0)
                        <div class="replies-section">
                            @foreach($comment->replies as $reply)
                                <div class="reply-item" data-comment-id="{{ $reply->id }}">
                                    <div class="reply-header">
                                        <div class="replier-info">
                                            <div class="replier-avatar">
                                                @if($reply->user->avatar)
                                                    <img src="{{ $reply->user->avatar_url }}" alt="{{ $reply->user->name }}" class="avatar-xs">
                                                @else
                                                    <div class="avatar-placeholder-xs">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                @endif
                                            </div>
                                            
                                            <div class="replier-details">
                                                <div class="replier-name">
                                                    {{ $reply->user->name }}
                                                    @if($reply->user->is_verified)
                                                        <i class="fas fa-check-circle text-success ms-1" title="{{ __('Verified User') }}"></i>
                                                    @endif
                                                    @if($reply->user->id === $ad->user_id)
                                                        <span class="badge badge-owner ms-1">{{ __('Ad Owner') }}</span>
                                                    @endif
                                                </div>
                                                
                                                <div class="reply-meta">
                                                    <span class="reply-date">{{ $reply->created_at->diffForHumans() }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="reply-actions">
                                            @auth
                                                @if(auth()->id() === $reply->user_id || auth()->user()->is_admin)
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="CommentsManager.deleteComment({{ $reply->id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            @endauth
                                        </div>
                                    </div>
                                    
                                    <div class="reply-content">
                                        <p class="reply-text">{{ $reply->content }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endforeach
            
            {{-- رابط عرض المزيد --}}
            @if($limit && $totalComments > $limit)
                <div class="text-center mt-4">
                    <a href="#" class="btn btn-outline-primary" onclick="CommentsManager.loadMoreComments()">
                        <i class="fas fa-chevron-down me-1"></i>
                        {{ __('Load More Comments') }} ({{ $totalComments - $limit }} {{ __('remaining') }})
                    </a>
                </div>
            @endif
        @else
            <div class="no-comments">
                <div class="text-center py-5">
                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{{ __('No Comments Yet') }}</h5>
                    <p class="text-muted">{{ __('Be the first to comment on this ad') }}</p>
                    
                    @if($showForm && auth()->check())
                        <button class="btn btn-primary mt-3" onclick="CommentsManager.showAddCommentForm()">
                            <i class="fas fa-comment me-1"></i>
                            {{ __('Write First Comment') }}
                        </button>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>
