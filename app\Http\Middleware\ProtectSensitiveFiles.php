<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware لحماية الملفات الحساسة
 * يمنع الوصول للملفات الحساسة عبر الويب
 */
class ProtectSensitiveFiles
{
    /**
     * الملفات المحظورة
     */
    private array $blockedFiles = [
        '.env',
        '.env.example',
        '.env.local',
        '.env.production',
        '.env.staging',
        'composer.json',
        'composer.lock',
        'package.json',
        'package-lock.json',
        'yarn.lock',
        'artisan',
        'server.php',
        'phpunit.xml',
        'webpack.mix.js',
        'vite.config.js',
        '.gitignore',
        '.gitattributes',
        'README.md',
        'CHANGELOG.md',
        'LICENSE',
    ];

    /**
     * المجلدات المحظورة
     */
    private array $blockedDirectories = [
        '.git',
        '.github',
        'vendor',
        'node_modules',
        'tests',
        'storage/logs',
        'storage/framework',
        'bootstrap/cache',
        'database/migrations',
        'database/seeders',
    ];

    /**
     * أنماط الملفات المحظورة
     */
    private array $blockedPatterns = [
        '/\.php$/i',
        '/\.sql$/i',
        '/\.log$/i',
        '/\.bak$/i',
        '/\.backup$/i',
        '/\.old$/i',
        '/\.tmp$/i',
        '/\.temp$/i',
        '/\.cache$/i',
        '/\.config$/i',
        '/\.ini$/i',
        '/\.conf$/i',
        '/\.htaccess$/i',
        '/\.htpasswd$/i',
        '/\.key$/i',
        '/\.pem$/i',
        '/\.crt$/i',
        '/\.p12$/i',
        '/\.pfx$/i',
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $path = $request->getPathInfo();
        $filename = basename($path);

        // فحص الملفات المحظورة مباشرة
        if (in_array($filename, $this->blockedFiles)) {
            $this->logBlockedAccess($request, 'blocked_file', $filename);
            return $this->denyAccess();
        }

        // فحص المجلدات المحظورة
        foreach ($this->blockedDirectories as $dir) {
            if (str_starts_with(ltrim($path, '/'), $dir)) {
                $this->logBlockedAccess($request, 'blocked_directory', $dir);
                return $this->denyAccess();
            }
        }

        // فحص أنماط الملفات المحظورة
        foreach ($this->blockedPatterns as $pattern) {
            if (preg_match($pattern, $filename)) {
                $this->logBlockedAccess($request, 'blocked_pattern', $pattern);
                return $this->denyAccess();
            }
        }

        // فحص محاولات الوصول للملفات الحساسة بطرق مختلفة
        if ($this->isSensitivePathAttempt($path)) {
            $this->logBlockedAccess($request, 'sensitive_path_attempt', $path);
            return $this->denyAccess();
        }

        // فحص محاولات Directory Traversal
        if ($this->isDirectoryTraversalAttempt($path)) {
            $this->logBlockedAccess($request, 'directory_traversal', $path);
            return $this->denyAccess();
        }

        return $next($request);
    }

    /**
     * فحص محاولات الوصول للمسارات الحساسة
     */
    private function isSensitivePathAttempt(string $path): bool
    {
        $sensitivePaths = [
            '/phpmyadmin',
            '/mysql',
            '/database',
            '/backup',
            '/config',
            '/wp-admin',
            '/wp-content',
            '/wp-includes',
            '/.well-known',
            '/api/config',
            '/api/admin',
        ];

        foreach ($sensitivePaths as $sensitivePath) {
            if (str_starts_with($path, $sensitivePath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * فحص محاولات Directory Traversal
     */
    private function isDirectoryTraversalAttempt(string $path): bool
    {
        // فحص أنماط Directory Traversal الشائعة
        $traversalPatterns = [
            '/\.\.\//',
            '/\.\.\\\\/',
            '/%2e%2e%2f/',
            '/%2e%2e%5c/',
            '/\.\.\%2f/',
            '/\.\.\%5c/',
            '/\.\.%252f/',
            '/\.\.%255c/',
        ];

        foreach ($traversalPatterns as $pattern) {
            if (preg_match($pattern, strtolower($path))) {
                return true;
            }
        }

        return false;
    }

    /**
     * تسجيل محاولة الوصول المحظورة
     */
    private function logBlockedAccess(Request $request, string $type, string $details): void
    {
        Log::warning('Blocked access to sensitive file/directory', [
            'type' => $type,
            'details' => $details,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'referer' => $request->header('referer'),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * رفض الوصول
     */
    private function denyAccess(): Response
    {
        return response()->view('errors.403', [
            'message' => 'Access to this resource is forbidden.'
        ], 403);
    }

    /**
     * إضافة ملف للقائمة المحظورة
     */
    public function addBlockedFile(string $filename): void
    {
        if (!in_array($filename, $this->blockedFiles)) {
            $this->blockedFiles[] = $filename;
        }
    }

    /**
     * إضافة مجلد للقائمة المحظورة
     */
    public function addBlockedDirectory(string $directory): void
    {
        if (!in_array($directory, $this->blockedDirectories)) {
            $this->blockedDirectories[] = $directory;
        }
    }

    /**
     * إضافة نمط للقائمة المحظورة
     */
    public function addBlockedPattern(string $pattern): void
    {
        if (!in_array($pattern, $this->blockedPatterns)) {
            $this->blockedPatterns[] = $pattern;
        }
    }

    /**
     * الحصول على قائمة الملفات المحظورة
     */
    public function getBlockedFiles(): array
    {
        return $this->blockedFiles;
    }

    /**
     * الحصول على قائمة المجلدات المحظورة
     */
    public function getBlockedDirectories(): array
    {
        return $this->blockedDirectories;
    }

    /**
     * الحصول على قائمة الأنماط المحظورة
     */
    public function getBlockedPatterns(): array
    {
        return $this->blockedPatterns;
    }
}
