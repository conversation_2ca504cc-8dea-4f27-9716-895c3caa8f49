@extends('layouts.app')

@section('title', __('Create New Ad'))

@push('styles')
<style>
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

.preview-container {
    position: relative;
    display: inline-block;
}

.remove-preview {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 12px;
    cursor: pointer;
}

@if(app()->getLocale() == 'ar')
.btn-group .btn {
    border-radius: 0;
}
.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}
@endif
</style>
@endpush

@section('content')
<div class="container py-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1 arabic-text text-primary" data-animation="fadeInLeft">
                        <i class="fas fa-plus-circle me-2"></i>
                        {{ __('Create New Ad') }}
                    </h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Add your ad now and reach thousands of users') }}</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.ads.index') }}" class="btn btn-outline-secondary arabic-text">
                        <i class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'right' : 'left' }} me-2"></i>
                        {{ __('Back to My Ads') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إنشاء الإعلان -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0 arabic-text">
                        <i class="fas fa-edit me-2"></i>
                        {{ __('Ad Details') }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('dashboard.ads.store') }}" enctype="multipart/form-data" id="createAdForm">
                        @csrf

                        <!-- العنوان بالعربية -->
                        <div class="mb-3">
                            <label for="title_ar" class="form-label required arabic-text">
                                <i class="fas fa-heading me-1"></i>
                                {{ __('Title (Arabic)') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('title_ar') is-invalid @enderror"
                                   id="title_ar"
                                   name="title_ar"
                                   value="{{ old('title_ar') }}"
                                   required
                                   maxlength="255"
                                   placeholder="{{ __('Enter ad title in Arabic') }}"
                                   dir="rtl">
                            @error('title_ar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- العنوان بالإنجليزية -->
                        <div class="mb-3">
                            <label for="title_en" class="form-label arabic-text">
                                <i class="fas fa-heading me-1"></i>
                                {{ __('Title (English)') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('title_en') is-invalid @enderror"
                                   id="title_en"
                                   name="title_en"
                                   value="{{ old('title_en') }}"
                                   maxlength="255"
                                   placeholder="{{ __('Enter ad title in English (optional)') }}"
                                   dir="ltr">
                            @error('title_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- التصنيف -->
                        <div class="mb-3">
                            <label for="category_id" class="form-label required arabic-text">
                                <i class="fas fa-tags me-1"></i>
                                {{ __('Category') }}
                            </label>
                            <select class="form-select @error('category_id') is-invalid @enderror"
                                    id="category_id"
                                    name="category_id"
                                    required>
                                <option value="">{{ __('Select Category') }}</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}"
                                            {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الوصف بالعربية -->
                        <div class="mb-3">
                            <label for="description_ar" class="form-label required arabic-text">
                                <i class="fas fa-align-left me-1"></i>
                                {{ __('Description (Arabic)') }}
                            </label>
                            <textarea class="form-control @error('description_ar') is-invalid @enderror"
                                      id="description_ar"
                                      name="description_ar"
                                      rows="4"
                                      required
                                      maxlength="2000"
                                      placeholder="{{ __('Describe your ad in detail in Arabic') }}"
                                      dir="rtl">{{ old('description_ar') }}</textarea>
                            @error('description_ar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الوصف بالإنجليزية -->
                        <div class="mb-3">
                            <label for="description_en" class="form-label arabic-text">
                                <i class="fas fa-align-left me-1"></i>
                                {{ __('Description (English)') }}
                            </label>
                            <textarea class="form-control @error('description_en') is-invalid @enderror"
                                      id="description_en"
                                      name="description_en"
                                      rows="4"
                                      maxlength="2000"
                                      placeholder="{{ __('Describe your ad in detail in English (optional)') }}"
                                      dir="ltr">{{ old('description_en') }}</textarea>
                            @error('description_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الصورة -->
                        <div class="mb-3">
                            <label for="image" class="form-label arabic-text">
                                <i class="fas fa-image me-1"></i>
                                {{ __('Ad Image') }}
                            </label>
                            <input type="file"
                                   class="form-control @error('image') is-invalid @enderror"
                                   id="image"
                                   name="image"
                                   accept="image/*"
                                   onchange="previewImage(this)">
                            <div class="form-text arabic-text">
                                {{ __('Maximum: 5 MB. Supported types: JPG, PNG, WEBP') }}
                            </div>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                            <!-- معاينة الصورة -->
                            <div id="imagePreview" style="display: none;" class="mt-2">
                                <div class="preview-container">
                                    <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                    <button type="button" class="remove-preview" onclick="removeImage()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label arabic-text">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ __('Phone Number') }}
                                </label>
                                <input type="tel"
                                       class="form-control @error('phone') is-invalid @enderror"
                                       id="phone"
                                       name="phone"
                                       value="{{ old('phone') }}"
                                       maxlength="20"
                                       placeholder="{{ __('Phone number') }}"
                                       dir="ltr">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label arabic-text">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ __('Email') }}
                                </label>
                                <input type="email"
                                       class="form-control @error('email') is-invalid @enderror"
                                       id="email"
                                       name="email"
                                       value="{{ old('email') }}"
                                       maxlength="255"
                                       placeholder="{{ __('Email address') }}"
                                       dir="ltr">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- الموقع -->
                        <div class="mb-3">
                            <label for="location" class="form-label arabic-text">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ __('Ad Location') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('location') is-invalid @enderror"
                                   id="location"
                                   name="location"
                                   value="{{ old('location') }}"
                                   maxlength="255"
                                   placeholder="{{ __('City or area') }}"
                                   dir="rtl">
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- قسم الأسعار المتقدم -->
                        <div class="card border-0 bg-light mb-4">
                            <div class="card-header bg-gradient-primary text-white">
                                <h6 class="mb-0 arabic-text">
                                    <i class="fas fa-tags me-2"></i>
                                    معلومات السعر والعروض
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- نوع السعر -->
                                <div class="mb-3">
                                    <label class="form-label arabic-text fw-bold">
                                        <i class="fas fa-dollar-sign me-1"></i>
                                        نوع السعر
                                    </label>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="price_type" id="price_fixed" value="fixed" checked>
                                                <label class="form-check-label arabic-text" for="price_fixed">
                                                    سعر ثابت
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="price_type" id="price_negotiable" value="negotiable">
                                                <label class="form-check-label arabic-text" for="price_negotiable">
                                                    قابل للتفاوض
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="price_type" id="price_free" value="free">
                                                <label class="form-check-label arabic-text" for="price_free">
                                                    مجاني
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="price_type" id="price_on_request" value="on_request">
                                                <label class="form-check-label arabic-text" for="price_on_request">
                                                    عند الطلب
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات السعر -->
                                <div id="price-fields" class="row">
                                    <!-- العملة -->
                                    <div class="col-md-4 mb-3">
                                        <label for="currency" class="form-label arabic-text">
                                            <i class="fas fa-coins me-1"></i>
                                            العملة
                                        </label>
                                        <select class="form-select @error('currency') is-invalid @enderror" id="currency" name="currency">
                                            <option value="YER" {{ old('currency') == 'YER' ? 'selected' : '' }}>ريال يمني (ر.ي)</option>
                                            <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>دولار أمريكي ($)</option>
                                            <option value="SAR" {{ old('currency') == 'SAR' ? 'selected' : '' }}>ريال سعودي (ر.س)</option>
                                            <option value="AED" {{ old('currency') == 'AED' ? 'selected' : '' }}>درهم إماراتي (د.إ)</option>
                                            <option value="EGP" {{ old('currency') == 'EGP' ? 'selected' : '' }}>جنيه مصري (ج.م)</option>
                                        </select>
                                        @error('currency')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- السعر الحالي -->
                                    <div class="col-md-4 mb-3">
                                        <label for="price" class="form-label arabic-text">
                                            <i class="fas fa-tag me-1"></i>
                                            السعر الحالي
                                        </label>
                                        <input type="number"
                                               class="form-control @error('price') is-invalid @enderror"
                                               id="price"
                                               name="price"
                                               value="{{ old('price') }}"
                                               min="0"
                                               step="0.01"
                                               placeholder="0.00">
                                        @error('price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- السعر الأصلي -->
                                    <div class="col-md-4 mb-3">
                                        <label for="original_price" class="form-label arabic-text">
                                            <i class="fas fa-tag me-1"></i>
                                            السعر الأصلي (قبل الخصم)
                                        </label>
                                        <input type="number"
                                               class="form-control @error('original_price') is-invalid @enderror"
                                               id="original_price"
                                               name="original_price"
                                               value="{{ old('original_price') }}"
                                               min="0"
                                               step="0.01"
                                               placeholder="0.00">
                                        <div class="form-text arabic-text">
                                            اتركه فارغاً إذا لم يكن هناك خصم
                                        </div>
                                        @error('original_price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- خيارات إضافية -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="is_negotiable" id="is_negotiable" value="1" {{ old('is_negotiable') ? 'checked' : '' }}>
                                            <label class="form-check-label arabic-text" for="is_negotiable">
                                                <i class="fas fa-handshake me-1"></i>
                                                السعر قابل للتفاوض
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="is_limited_offer" id="is_limited_offer" value="1" {{ old('is_limited_offer') ? 'checked' : '' }}>
                                            <label class="form-check-label arabic-text" for="is_limited_offer">
                                                <i class="fas fa-clock me-1"></i>
                                                عرض محدود
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- تاريخ انتهاء الخصم -->
                                <div class="mb-3" id="discount-expiry" style="display: none;">
                                    <label for="discount_expires_at" class="form-label arabic-text">
                                        <i class="fas fa-calendar-times me-1"></i>
                                        تاريخ انتهاء الخصم
                                    </label>
                                    <input type="date"
                                           class="form-control @error('discount_expires_at') is-invalid @enderror"
                                           id="discount_expires_at"
                                           name="discount_expires_at"
                                           value="{{ old('discount_expires_at') }}"
                                           min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                                    @error('discount_expires_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- ملاحظات السعر -->
                                <div class="mb-3">
                                    <label for="price_notes" class="form-label arabic-text">
                                        <i class="fas fa-sticky-note me-1"></i>
                                        ملاحظات إضافية على السعر
                                    </label>
                                    <textarea class="form-control @error('price_notes') is-invalid @enderror"
                                              id="price_notes"
                                              name="price_notes"
                                              rows="2"
                                              placeholder="مثال: يشمل التوصيل، خصم للكمية، إلخ..."
                                              dir="rtl">{{ old('price_notes') }}</textarea>
                                    @error('price_notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- تاريخ انتهاء الصلاحية -->
                        <div class="mb-4">
                            <label for="expires_at" class="form-label arabic-text">
                                <i class="fas fa-calendar-alt me-1"></i>
                                {{ __('Ad Expiry Date') }}
                            </label>
                            <input type="date"
                                   class="form-control @error('expires_at') is-invalid @enderror"
                                   id="expires_at"
                                   name="expires_at"
                                   value="{{ old('expires_at') }}"
                                   min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                            <div class="form-text arabic-text">
                                {{ __('If no date is specified, the ad will remain active indefinitely') }}
                            </div>
                            @error('expires_at')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('dashboard.ads.index') }}" class="btn btn-outline-secondary arabic-text">
                                <i class="fas fa-times me-2"></i>
                                {{ __('Cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary arabic-text" id="submitBtn">
                                <i class="fas fa-save me-2"></i>
                                {{ __('Publish Ad') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- ملاحظات مهمة -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0 arabic-text">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('Important Notes') }}
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0 arabic-text">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Your ad will be reviewed before publishing to ensure it complies with site terms') }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Make sure your contact information is correct to ensure interested parties can reach you') }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Use clear title and description to increase your ad\'s chances of appearing in search results') }}
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('You can edit or delete your ad anytime from My Ads page') }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        }

        reader.readAsDataURL(input.files[0]);
    }
}

function removeImage() {
    const input = document.getElementById('image');
    const preview = document.getElementById('imagePreview');

    input.value = '';
    preview.style.display = 'none';
}

// إدارة حقول الأسعار
document.addEventListener('DOMContentLoaded', function() {
    const priceTypeRadios = document.querySelectorAll('input[name="price_type"]');
    const priceFields = document.getElementById('price-fields');
    const discountExpiry = document.getElementById('discount-expiry');
    const originalPriceField = document.getElementById('original_price');
    const isLimitedOfferCheckbox = document.getElementById('is_limited_offer');

    // إدارة إظهار/إخفاء حقول السعر
    priceTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'free' || this.value === 'on_request') {
                priceFields.style.display = 'none';
                discountExpiry.style.display = 'none';
            } else {
                priceFields.style.display = 'block';
            }
        });
    });

    // إدارة تاريخ انتهاء الخصم
    originalPriceField.addEventListener('input', function() {
        if (this.value && parseFloat(this.value) > 0) {
            discountExpiry.style.display = 'block';
        } else {
            discountExpiry.style.display = 'none';
        }
    });

    // إدارة العرض المحدود
    isLimitedOfferCheckbox.addEventListener('change', function() {
        if (this.checked) {
            discountExpiry.style.display = 'block';
        }
    });

    // حساب نسبة الخصم تلقائياً
    const priceField = document.getElementById('price');

    function calculateDiscount() {
        const originalPrice = parseFloat(originalPriceField.value) || 0;
        const currentPrice = parseFloat(priceField.value) || 0;

        if (originalPrice > 0 && currentPrice > 0 && originalPrice > currentPrice) {
            const discountPercentage = Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
            const savingsAmount = originalPrice - currentPrice;

            // عرض معلومات الخصم
            let discountInfo = document.getElementById('discount-info');
            if (!discountInfo) {
                discountInfo = document.createElement('div');
                discountInfo.id = 'discount-info';
                discountInfo.className = 'alert alert-success mt-2';
                originalPriceField.parentNode.appendChild(discountInfo);
            }

            discountInfo.innerHTML = `
                <i class="fas fa-percentage me-1"></i>
                نسبة الخصم: <strong>${discountPercentage}%</strong> |
                مبلغ التوفير: <strong>${savingsAmount.toFixed(2)}</strong>
            `;
        } else {
            const discountInfo = document.getElementById('discount-info');
            if (discountInfo) {
                discountInfo.remove();
            }
        }
    }

    originalPriceField.addEventListener('input', calculateDiscount);
    priceField.addEventListener('input', calculateDiscount);
});

// تحسين تجربة الإرسال
document.getElementById('createAdForm').addEventListener('submit', function() {
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{{ __("Publishing...") }}';
    submitBtn.disabled = true;
});
</script>
@endsection
