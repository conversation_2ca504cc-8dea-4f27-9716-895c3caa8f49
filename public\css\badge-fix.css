/*
 * ===== إصلاح مشكلة عرض الشارات =====
 * ملف CSS مخصص لحل مشكلة عرض شارة التصنيف بعرض كامل
 * تم تحسينه لتقليل استخدام !important واستخدام specificity أعلى
 *
 * المشكلة: كانت شارات التصنيف تظهر بعرض كامل بدلاً من عرض المحتوى
 * الحل: استخدام selectors محددة أكثر مع قواعد CSS قوية
 */

/*
 * إصلاح شارة التصنيف - استخدام specificity عالي بدلاً من !important
 * يستهدف جميع أنواع البطاقات والحاويات المختلفة
 * يضمن عرض الشارة بحجم المحتوى وليس العرض الكامل
 */
.ad-card .category-badge-container .category-badge-link .category-badge,
.ad-card .category-badge-container .category-badge,
.ad-card .category-badge,
.card .category-badge-container .category-badge,
.category-badge-container .category-badge-link .category-badge,
.ad-card-favorites .category-badge-container .category-badge,
.favorites-page .ad-card .category-badge {
    display: inline-flex;        /* عرض مرن في خط واحد */
    width: auto;                 /* عرض تلقائي حسب المحتوى */
    max-width: fit-content;      /* الحد الأقصى للعرض حسب المحتوى */
    min-width: auto;             /* الحد الأدنى للعرض تلقائي */
    flex: none;                  /* عدم التمدد أو الانكماش */
    flex-grow: 0;               /* عدم النمو */
    flex-shrink: 0;             /* عدم الانكماش */
    white-space: nowrap;        /* منع كسر النص */
    box-sizing: border-box;     /* حساب الحدود والحشو ضمن العرض */
}

/*
 * إصلاح حاوي شارة التصنيف
 * يضمن أن الحاوي لا يأخذ العرض الكامل ويحتوي فقط على الشارة
 * يستخدم specificity عالي لتجاوز أنماط Bootstrap والأنماط الأخرى
 */
.ad-card .category-badge-container,
.card .category-badge-container,
.ad-card-favorites .category-badge-container,
.favorites-page .ad-card .category-badge-container,
.category-badge-container {
    display: inline-block;       /* عرض كتلة في خط واحد */
    width: auto;                 /* عرض تلقائي حسب المحتوى */
    max-width: fit-content;      /* الحد الأقصى للعرض حسب المحتوى */
    min-width: auto;             /* الحد الأدنى للعرض تلقائي */
    flex: none;                  /* عدم التمدد في حاويات flex */
    box-sizing: border-box;      /* حساب الحدود والحشو ضمن العرض */
}

/*
 * إصلاح رابط شارة التصنيف
 * يضمن أن الرابط لا يأخذ العرض الكامل ولا يحتوي على تنسيق نص افتراضي
 * يحافظ على شكل الشارة كرابط قابل للنقر
 */
.ad-card .category-badge-container .category-badge-link,
.card .category-badge-container .category-badge-link,
.ad-card-favorites .category-badge-container .category-badge-link,
.favorites-page .ad-card .category-badge-link,
.category-badge-link {
    display: inline-block;       /* عرض كتلة في خط واحد */
    width: auto;                 /* عرض تلقائي حسب المحتوى */
    max-width: fit-content;      /* الحد الأقصى للعرض حسب المحتوى */
    min-width: auto;             /* الحد الأدنى للعرض تلقائي */
    text-decoration: none;       /* إزالة تسطير الرابط */
    box-sizing: border-box;      /* حساب الحدود والحشو ضمن العرض */
}

/*
 * إلغاء تأثيرات Bootstrap والمكتبات الأخرى
 * يستهدف الحالات التي قد تطبق فيها classes أخرى على شارة التصنيف
 * يضمن الحفاظ على التنسيق الصحيح حتى مع وجود classes متعارضة
 */
.ad-card .category-badge.badge,
.ad-card .category-badge.btn,
.ad-card .category-badge.d-block,
.ad-card .category-badge.w-100,
.category-badge.badge,
.category-badge.btn,
.category-badge.d-block,
.category-badge.w-100 {
    display: inline-flex;        /* عرض مرن في خط واحد */
    width: auto;                 /* عرض تلقائي حسب المحتوى */
    max-width: fit-content;      /* الحد الأقصى للعرض حسب المحتوى */
}

/*
 * إصلاح خاص بصفحة المفضلة
 * يضمن المسافة السفلية المناسبة والعرض الصحيح في صفحة المفضلة
 */
.favorites-page .ad-card .category-badge-container {
    margin-bottom: 1rem;        /* مسافة سفلية */
    display: inline-block;      /* عرض كتلة في خط واحد */
    width: auto;                /* عرض تلقائي حسب المحتوى */
}

.favorites-page .ad-card .category-badge {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: white !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 8px !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    display: inline-flex !important;
    align-items: center !important;
    width: auto !important;
    max-width: fit-content !important;
    white-space: nowrap !important;
}

/* إصلاح شارات الحالة */
.new-badge,
.featured-badge,
.urgent-badge,
.ad-card .new-badge,
.ad-card .featured-badge,
.ad-card .urgent-badge {
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    max-width: fit-content !important;
    width: auto !important;
    flex: none !important;
}

/* تأكيد عدم تأثير أي CSS خارجي */
.category-badge * {
    box-sizing: border-box;
}

/*
 * إصلاح للأجهزة المحمولة والأجهزة اللوحية
 * نقطة الكسر موحدة مع responsive.css: 767.98px
 * تحسينات خاصة بالشارات للشاشات الصغيرة
 */
@media (max-width: 767.98px) {
    .category-badge,
    .ad-card .category-badge,
    .favorites-page .category-badge {
        font-size: 0.7rem !important;
        padding: 0.3rem 0.6rem !important;
        display: inline-flex !important;
        width: auto !important;
        max-width: fit-content !important;
    }

    .category-badge-container,
    .ad-card .category-badge-container,
    .favorites-page .category-badge-container {
        display: inline-block !important;
        width: auto !important;
        margin-bottom: 0.75rem !important;
    }

    /* شارات الحالة في بطاقات المفضلة - الأجهزة المحمولة */
    .ad-card-favorites .new-badge,
    .favorites-page .new-badge {
        top: 8px !important;
        left: 8px !important;
        font-size: 0.65rem !important;
        padding: 0.25rem 0.5rem !important;
        transform: none !important;
    }

    .ad-card-favorites .featured-badge,
    .favorites-page .featured-badge {
        top: 8px !important;
        left: 8px !important;
        font-size: 0.65rem !important;
        padding: 0.25rem 0.5rem !important;
        transform: translateY(36px) !important;
    }

    .ad-card-favorites .urgent-badge,
    .favorites-page .urgent-badge {
        top: 8px !important;
        left: 8px !important;
        font-size: 0.65rem !important;
        padding: 0.25rem 0.5rem !important;
        transform: translateY(72px) !important;
    }

    /* أيقونة إزالة المفضلة - الأجهزة المحمولة */
    .ad-card-favorites .favorite-remove-icon {
        top: 8px !important;
        right: 8px !important;
    }

    .ad-card-favorites .btn-favorite-remove {
        width: 30px !important;
        height: 30px !important;
    }
}

/* إصلاح إضافي للتأكد */
.category-badge:not(.new-badge):not(.featured-badge):not(.urgent-badge) {
    position: static !important;
    display: inline-flex !important;
    width: auto !important;
    max-width: fit-content !important;
}

/* منع أي تداخل مع CSS آخر */
.ad-card .card-body .category-badge-container {
    display: inline-block !important;
    width: auto !important;
    max-width: fit-content !important;
    margin-bottom: 1rem !important;
}

.ad-card .card-body .category-badge-container .category-badge-link {
    display: inline-block !important;
    width: auto !important;
    max-width: fit-content !important;
}

.ad-card .card-body .category-badge-container .category-badge-link .category-badge {
    display: inline-flex !important;
    width: auto !important;
    max-width: fit-content !important;
    white-space: nowrap !important;
}

/* إصلاح نهائي - أعلى أولوية */
.category-badge[class*="category-badge"] {
    display: inline-flex !important;
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
    flex: none !important;
    white-space: nowrap !important;
}

.category-badge-container[class*="category-badge-container"] {
    display: inline-block !important;
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
}

.category-badge-link[class*="category-badge-link"] {
    display: inline-block !important;
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
    text-decoration: none !important;
}

/* ===== إصلاح موضع شارات الحالة في بطاقات المفضلة ===== */

/* شارات الحالة في بطاقات المفضلة - الجهة اليسرى */
.ad-card-favorites .new-badge,
.favorites-page .new-badge {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    right: auto !important;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    color: white !important;
    z-index: 5 !important;
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3) !important;
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    max-width: fit-content !important;
    width: auto !important;
    transform: none !important;
}

.ad-card-favorites .featured-badge,
.favorites-page .featured-badge {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    right: auto !important;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    color: white !important;
    z-index: 4 !important;
    transform: translateY(35px) !important;
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3) !important;
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    max-width: fit-content !important;
    width: auto !important;
}

.ad-card-favorites .urgent-badge,
.favorites-page .urgent-badge {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    right: auto !important;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
    color: white !important;
    z-index: 3 !important;
    transform: translateY(70px) !important;
    animation: pulse 2s infinite !important;
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3) !important;
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    max-width: fit-content !important;
    width: auto !important;
}

/* تأثير النبضة للشارة العاجلة */
@keyframes pulse {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3), 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    50% {
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3), 0 0 0 8px rgba(239, 68, 68, 0);
    }
}

/* التأكد من عدم تداخل الشارات مع أيقونة إزالة المفضلة */
.ad-card-favorites .favorite-remove-icon {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    left: auto !important;
    z-index: 10 !important;
}
