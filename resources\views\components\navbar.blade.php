<nav class="navbar navbar-expand-lg navbar-custom sticky-top">
    <div class="container">
        <!-- شعار الموقع -->
        <a class="navbar-brand arabic-text" href="{{ route('home') }}">
            <i class="fas fa-bullhorn me-2"></i>
            {{ __('site_name') }}
        </a>

        <!-- زر القائمة للجوال -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
            <span class="navbar-toggler-icon">
                <i class="fas fa-bars text-white"></i>
            </span>
        </button>

        <!-- عناصر التنقل -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- القائمة الرئيسية -->
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link arabic-text {{ request()->routeIs('home') ? 'active' : '' }}"
                       href="{{ route('home') }}">
                        <i class="fas fa-home me-1"></i>
                        {{ __('Home') }}
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link arabic-text {{ request()->routeIs('categories.*') ? 'active' : '' }}"
                       href="{{ route('categories.index') }}">
                        <i class="fas fa-th-large me-1"></i>
                        {{ __('Categories') }}
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link arabic-text {{ request()->routeIs('ads.*') ? 'active' : '' }}"
                       href="{{ route('ads.index') }}">
                        <i class="fas fa-list me-1"></i>
                        {{ __('All Ads') }}
                    </a>
                </li>







                <!-- أيقونة الإعدادات (للمديرين فقط) -->
                @auth
                    @if(Auth::user()->is_admin ?? false)
                        <li class="nav-item">
                            <a class="nav-link settings-icon"
                               href="{{ route('admin.settings') }}"
                               title="{{ __('Site Settings') }}">
                                <i class="fas fa-cog fa-lg"></i>
                            </a>
                        </li>
                    @endif
                @endauth
            </ul>

            <!-- أدوات التنقل -->
            <ul class="navbar-nav">
                <!-- البحث المتقدم -->
                <li class="nav-item me-1">
                    <a class="nav-link" href="{{ route('search.index') }}"
                       title="{{ __('Advanced Search') }}"
                       data-bs-toggle="tooltip" data-bs-placement="bottom">
                        <i class="fas fa-search-plus"></i>
                    </a>
                </li>

                <!-- المفضلة (للمستخدمين المسجلين) -->
                @auth
                    <li class="nav-item me-3">
                        <a class="nav-link position-relative" href="{{ route('favorites.index') }}"
                           title="{{ __('My Favorites') }}"
                           data-bs-toggle="tooltip" data-bs-placement="bottom">
                            <i class="fas fa-heart"></i>
                            <span class="position-absolute badge bg-danger favorites-count"
                                  style="top: -8px; right: -8px; display: {{ (Auth::user()->favorites_count > 0) ? 'block' : 'none' }};">
                                {{ Auth::user()->favorites_count ?? 0 }}
                            </span>
                        </a>
                    </li>
                @endauth

                <!-- تبديل الوضع الليلي -->
                <li class="nav-item me-1">
                    <button class="nav-link border-0 bg-transparent theme-toggle"
                            onclick="toggleTheme()"
                            title="{{ __('Dark Mode') }}"
                            id="themeToggleBtn"
                            type="button">
                        <i id="theme-icon" class="fas fa-moon"></i>
                    </button>
                </li>

                <!-- تبديل اللغة -->
                <li class="nav-item me-1">
                    <a class="nav-link border-0 bg-transparent language-toggle-btn"
                       href="{{ route('language.switch', app()->getLocale() === 'ar' ? 'en' : 'ar') }}"
                       title="{{ app()->getLocale() === 'ar' ? __('Switch to English') : __('Switch to Arabic') }}"
                       aria-label="{{ app()->getLocale() === 'ar' ? __('Switch to English') : __('Switch to Arabic') }}"
                       id="languageToggleBtn">
                        <i class="fas fa-globe me-1"></i>
                        @if(app()->getLocale() === 'ar')
                            {{ __('EN') }}
                        @else
                            {{ __('AR') }}
                        @endif
                    </a>
                </li>

                @if(config('app.debug'))
                <!-- أزرار الاختبار (تظهر فقط في وضع التطوير) -->
                <li class="nav-item dropdown me-1">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-tools"></i> Test
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('language.debug') }}">
                            <i class="fas fa-info-circle text-primary"></i> Language Debug
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('logs.view') }}">
                            <i class="fas fa-file-alt text-success"></i> View Logs
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ route('language.clear.session') }}">
                            <i class="fas fa-trash text-warning"></i> Clear Session
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('language.clear.cookie') }}">
                            <i class="fas fa-cookie-bite text-warning"></i> Clear Cookie
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('language.clear.all') }}">
                            <i class="fas fa-broom text-danger"></i> Clear All
                        </a></li>
                    </ul>
                </li>
                @endif

                <!-- زر إضافة إعلان (حسب الإعدادات) -->
                @auth
                    @if(\App\Models\Setting::get('show_add_ad_button_navbar', false))
                        <li class="nav-item me-2">
                            <a class="btn btn-success btn-sm arabic-text" href="{{ route('ads.create') }}">
                                <i class="fas fa-plus me-1"></i>
                                {{ __('Add Ad') }}
                            </a>
                        </li>
                    @endif
                @endauth

                <!-- قائمة المستخدم -->
                @auth
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle arabic-text" href="#" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i>
                            {{ Auth::user()->name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item arabic-text" href="{{ route('auth.profile') }}">
                                    <i class="fas fa-user me-2"></i>
                                    {{ __('Profile') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item arabic-text" href="{{ route('dashboard.index') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    {{ __('Manage My Ads') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item arabic-text" href="{{ route('favorites.index') }}">
                                    <i class="fas fa-heart me-2 text-danger"></i>
                                    {{ __('My Favorites') }}
                                    <span class="badge bg-danger ms-1 favorites-count"
                                          style="display: {{ (Auth::user()->favorites_count > 0) ? 'inline' : 'none' }};">
                                        {{ Auth::user()->favorites_count ?? 0 }}
                                    </span>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form action="{{ route('auth.logout') }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="dropdown-item arabic-text text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        {{ __('Logout') }}
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                @else
                    <!-- أزرار تسجيل الدخول للضيوف -->
                    <li class="nav-item me-1">
                        <a class="btn btn-outline-light btn-sm arabic-text" href="{{ route('login') }}">
                            <i class="fas fa-sign-in-alt"></i>
                            {{ __('Login') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-light btn-sm arabic-text" href="{{ route('auth.register') }}">
                            <i class="fas fa-user-plus"></i>
                            {{ __('Register') }}
                        </a>
                    </li>
                @endauth
            </ul>
        </div>
    </div>
</nav>



<style>
/* تحسينات إضافية لشريط التنقل */
.navbar-custom .navbar-toggler:focus {
    box-shadow: none;
}

.navbar-custom .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
}

/* تحسينات للنص العربي */
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    letter-spacing: 0.5px;
    line-height: 1.6;
}

/* تحسين شريط التنقل للعربية */
@media (min-width: 992px) {
    .navbar-nav .nav-link {
        padding: 0.4rem 0.7rem !important;
        margin: 0 0.1rem;
        white-space: nowrap;
        font-size: 0.9rem;
    }

    .navbar-nav .nav-link.arabic-text {
        min-width: auto;
        text-align: center;
    }
}

/* تحسين الأزرار */
.btn-sm {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    white-space: nowrap;
}

/* تحسين أزرار navbar خاصة */
.navbar-nav .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    margin: 0 0.1rem;
}

/* تحسين أيقونة البحث المتقدم */
.navbar-nav .nav-link i.fa-search-plus {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover i.fa-search-plus {
    color: white;
    transform: scale(1.1);
}

/* تحسين أيقونة المفضلة */
.navbar-nav .nav-link i.fa-heart {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover i.fa-heart {
    color: #dc3545;
    transform: scale(1.1);
    animation: heartBeat 0.6s ease-in-out;
}

/* شارة عداد المفضلة */
.navbar-nav .nav-link .badge.favorites-count {
    font-size: 0.6rem;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تأثير خاص لأيقونة البحث المتقدم */
.navbar-nav .nav-link[title*="Advanced Search"] {
    position: relative;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link[title*="Advanced Search"]:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

/* تحسين responsive للأيقونة */
@media (max-width: 991px) {
    .navbar-nav .nav-link[title*="Advanced Search"] {
        width: auto;
        height: auto;
        border-radius: 0.5rem;
        padding: 0.5rem 0.8rem;
    }

    .navbar-nav .nav-link[title*="Advanced Search"]:after {
        content: " {{ __('Advanced Search') }}";
        margin-right: 0.5rem;
        font-size: 0.9rem;
    }
}

/* تحسين القوائم المنسدلة */
.dropdown-toggle::after {
    margin-left: 0.5rem;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 991px) {
    .navbar-nav {
        text-align: center;
        padding-top: 1rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem !important;
        margin: 0.25rem 0;
        border-radius: 0.5rem;
    }

    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* تحسين المساحات للعربية */
.navbar-custom {
    padding: 0.75rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 600;
}

/* تحسين الأزرار في الشريط العلوي */
.navbar-nav .btn {
    margin: 0 0.1rem;
}

/* تحسين المسافات للعناصر الجانبية */
.navbar-nav .nav-item.me-2 {
    margin-right: 0.3rem !important;
}

.navbar-nav .nav-item.dropdown {
    margin-right: 0.2rem !important;
}

/* تحسين النصوص المختلطة */
.navbar-nav .nav-link i {
    width: 16px;
    text-align: center;
}

/* تحسين أيقونة الإعدادات */
.settings-icon {
    position: relative;
    transition: all 0.3s ease;
}

.settings-icon:hover i {
    transform: rotate(90deg);
    color: #ffc107 !important;
}

.settings-icon.active i {
    color: #ffc107 !important;
    animation: settingsRotate 2s ease-in-out infinite;
}

@keyframes settingsRotate {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}



.dropdown-menu {
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-radius: 0.75rem;
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.dropdown-item.active {
    background-color: var(--primary-color);
    color: white;
}

/* تحسين البحث السريع */
.dropdown-menu .form-control {
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
}

.dropdown-menu .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* تحسين الاستجابة للجوال */
@media (max-width: 991px) {
    .navbar-nav {
        text-align: center;
        padding-top: 1rem;
    }

    .navbar-nav .nav-item {
        margin-bottom: 0.5rem;
    }

    .btn-outline-light,
    .btn-light {
        margin: 0.25rem;
        width: 150px;
    }
}

/* تأثير الشريط المتحرك */
marquee {
    font-size: 0.9rem;
}

/* تحسين أيقونات التنقل */
.nav-link i {
    width: 16px;
    text-align: center;
}

/* تحسينات خاصة لمنع التداخل */
.contact-link {
    position: relative;
    z-index: 10;
}

/* تصميم زر تبديل اللغة الجديد */
.language-toggle-btn {
    position: relative;
    z-index: 20;
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 0.9rem;
}

.language-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    color: var(--primary-color);
    text-decoration: none;
}

.language-toggle-btn:active {
    transform: translateY(0) scale(0.98);
}

.language-toggle-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات للوضع الليلي */
[data-theme="dark"] .language-toggle-btn {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

[data-theme="dark"] .language-toggle-btn:hover {
    background: rgba(0, 0, 0, 0.5);
    color: var(--accent-color);
}

.theme-toggle {
    position: relative;
    z-index: 15;
    transition: transform 0.15s ease;
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* تحسين القوائم المنسدلة لمنع التداخل */
.dropdown-menu {
    z-index: 1050;
    pointer-events: auto;
}

.dropdown-item {
    pointer-events: auto;
}

/* تأثيرات بصرية للتأكيد على عدم التداخل */
.nav-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* تحسينات إضافية للزر الجديد */
.language-toggle-btn .fas.fa-globe {
    transition: transform 0.3s ease;
}

.language-toggle-btn:hover .fas.fa-globe {
    transform: rotate(15deg);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .language-toggle-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
        min-width: 50px;
        justify-content: center;
    }

    .language-toggle-btn .fas.fa-globe {
        margin-left: 0.25rem;
        margin-right: 0.25rem;
    }

    /* تحسين المسافة بين الأزرار في الموبايل */
    .navbar-nav .nav-item.me-1 {
        margin-left: 0.25rem !important;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .language-toggle-btn {
        padding: 0.3rem 0.5rem;
        font-size: 0.8rem;
        min-width: 45px;
    }

    .language-toggle-btn .fas.fa-globe {
        font-size: 0.9rem;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    .language-toggle-btn {
        padding: 0.6rem 0.9rem;
        font-size: 1rem;
    }
}
</style>

<script>
// JavaScript لشريط التنقل
document.addEventListener('DOMContentLoaded', function() {
    // منع إغلاق القائمة المنسدلة عند النقر داخلها
    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
    dropdownMenus.forEach(function(menu) {
        menu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
