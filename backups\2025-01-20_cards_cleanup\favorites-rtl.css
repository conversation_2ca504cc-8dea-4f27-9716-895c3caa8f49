/**
 * تحسينات RTL خاصة بصفحة المفضلة
 * يتم تحميل هذا الملف فقط عند استخدام اللغة العربية
 */

/* تحسينات RTL للمفضلة */
[dir="rtl"] .favorites-page {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

[dir="rtl"] .favorites-page .favorite-icon {
    left: auto;
    right: 10px;
}

[dir="rtl"] .favorites-page .category-badge {
    right: auto;
    left: 10px;
}

/* شارة التصنيف الجديدة في RTL */
[dir="rtl"] .favorites-page .category-badge-body {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] .favorites-page .category-badge-body i {
    margin-left: 0;
    margin-right: 0.3rem;
}

[dir="rtl"] .favorites-page .favorite-date {
    right: auto;
    left: 10px;
}

[dir="rtl"] .favorites-page .meta-item {
    direction: rtl;
    text-align: right;
}

/* تحسينات RTL لقسم الأسعار */
[dir="rtl"] .favorites-page .price-section {
    direction: rtl;
    text-align: center;
}

[dir="rtl"] .favorites-page .discount-badge {
    align-self: flex-end;
}

[dir="rtl"] .favorites-page .original-price,
[dir="rtl"] .favorites-page .current-price,
[dir="rtl"] .favorites-page .savings-amount,
[dir="rtl"] .favorites-page .negotiable-badge {
    direction: rtl;
}

[dir="rtl"] .favorites-page .price-free i,
[dir="rtl"] .favorites-page .price-on-request i,
[dir="rtl"] .favorites-page .discount-badge i,
[dir="rtl"] .favorites-page .savings-amount i,
[dir="rtl"] .favorites-page .negotiable-badge i {
    margin-left: 0;
    margin-right: 0.3rem;
}

[dir="rtl"] .favorites-page .meta-item i {
    margin-left: 0;
    margin-right: 0.5rem;
}

[dir="rtl"] .favorites-page .ad-title {
    text-align: right;
    direction: rtl;
}

[dir="rtl"] .favorites-page .ad-description {
    text-align: right;
    direction: rtl;
    line-height: 1.8;
}

/* تحسين الأزرار في RTL */
[dir="rtl"] .favorites-page .ad-actions {
    direction: rtl;
}

[dir="rtl"] .favorites-page .ad-actions .btn {
    margin-left: 0.25rem;
    margin-right: 0;
}

[dir="rtl"] .favorites-page .ad-actions .btn:first-child {
    margin-left: 0;
}

[dir="rtl"] .favorites-page .ad-actions .btn:last-child {
    margin-right: 0;
}

/* تحسين الأيقونات في RTL */
[dir="rtl"] .favorites-page .btn i {
    margin-left: 0.5rem;
    margin-right: 0;
}

[dir="rtl"] .favorites-page .btn i.me-2 {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* تحسين التخطيط العام في RTL */
[dir="rtl"] .favorites-page .card-body {
    text-align: right;
}

[dir="rtl"] .favorites-page .card-footer {
    text-align: right;
}

/* تحسين الشارات في RTL */
[dir="rtl"] .favorites-page .badge {
    direction: rtl;
}

[dir="rtl"] .favorites-page .favorites-count {
    direction: ltr; /* الأرقام تبقى LTR */
}

/* تحسين النماذج في RTL */
[dir="rtl"] .favorites-page .form-select {
    text-align: right;
    direction: rtl;
}

[dir="rtl"] .favorites-page .form-label {
    text-align: right;
}

/* تحسين الفلاتر في RTL */
[dir="rtl"] .favorites-page .d-flex {
    direction: rtl;
}

[dir="rtl"] .favorites-page .text-end {
    text-align: left !important;
}

[dir="rtl"] .favorites-page .text-start {
    text-align: right !important;
}

/* تحسين الـ Pagination في RTL */
[dir="rtl"] .favorites-page .pagination {
    direction: ltr; /* الأرقام والتنقل يبقى LTR */
}

/* تحسين الحالة الفارغة في RTL */
[dir="rtl"] .favorites-page .empty-state {
    text-align: center;
    direction: rtl;
}

[dir="rtl"] .favorites-page .empty-state h3,
[dir="rtl"] .favorites-page .empty-state p {
    direction: rtl;
}

/* تحسين الأنيميشن في RTL */
[dir="rtl"] .favorites-page .favorite-card-wrapper:hover {
    transform: translateY(-5px) scale(1.02);
}

/* تحسين responsive في RTL */
@media (max-width: 768px) {
    [dir="rtl"] .favorites-page .ad-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    [dir="rtl"] .favorites-page .ad-actions .btn {
        margin: 0;
        width: 100%;
    }
    
    [dir="rtl"] .favorites-page .meta-item {
        justify-content: flex-end;
    }
}

/* تحسين الخطوط العربية */
[dir="rtl"] .favorites-page .arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    letter-spacing: 0.3px;
    line-height: 1.8;
}

/* تحسين الأرقام والتواريخ */
[dir="rtl"] .favorites-page .meta-item small {
    direction: ltr;
    unicode-bidi: embed;
}

[dir="rtl"] .favorites-page .favorite-date {
    direction: ltr;
    unicode-bidi: embed;
}

/* تحسين الروابط في RTL */
[dir="rtl"] .favorites-page a {
    direction: inherit;
}

[dir="rtl"] .favorites-page .ad-title a {
    direction: rtl;
    text-decoration: none;
}

[dir="rtl"] .favorites-page .ad-title a:hover {
    color: #007bff;
}

/* تحسين التباعد في RTL */
[dir="rtl"] .favorites-page .me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .favorites-page .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .favorites-page .ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .favorites-page .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

/* تحسين الحدود في RTL */
[dir="rtl"] .favorites-page .border-start {
    border-left: 0 !important;
    border-right: var(--bs-border-width) solid var(--bs-border-color) !important;
}

[dir="rtl"] .favorites-page .border-end {
    border-right: 0 !important;
    border-left: var(--bs-border-width) solid var(--bs-border-color) !important;
}

/* تحسين الظلال في RTL */
[dir="rtl"] .favorites-page .shadow-start {
    box-shadow: -0.125rem 0 0.25rem rgba(0, 0, 0, 0.075) !important;
}

[dir="rtl"] .favorites-page .shadow-end {
    box-shadow: 0.125rem 0 0.25rem rgba(0, 0, 0, 0.075) !important;
}
