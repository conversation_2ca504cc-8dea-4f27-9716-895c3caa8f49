/**
 * ملف JavaScript للتفاعل مع شارات الحالة والميزات
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على وظائف التفاعل مع الشارات والتأثيرات البصرية
 */

// متغيرات عامة
const StatusBadges = {
    // إعدادات التطبيق
    config: {
        animationDuration: 300,
        tooltipDelay: 500,
        logLevel: 'info'
    },

    // حالة التطبيق
    state: {
        isInitialized: false,
        activeTooltips: new Set(),
        badgeInteractions: 0
    },

    /**
     * تهيئة نظام الشارات
     */
    init() {
        if (this.state.isInitialized) {
            console.warn('StatusBadges already initialized');
            return;
        }

        try {
            this.log('info', 'تهيئة نظام شارات الحالة والميزات');
            
            this.initializeBadgeInteractions();
            this.initializeTooltips();
            this.initializeAnimations();
            this.setupEventListeners();
            
            this.state.isInitialized = true;
            this.log('info', 'تم تهيئة نظام الشارات بنجاح');
            
        } catch (error) {
            this.log('error', 'خطأ في تهيئة نظام الشارات', error);
        }
    },

    /**
     * تهيئة تفاعلات الشارات
     */
    initializeBadgeInteractions() {
        const badges = document.querySelectorAll('.status-badge, .feature-badge, .warning-badge, .achievement-badge');
        
        badges.forEach(badge => {
            // تأثير التركيز
            badge.addEventListener('mouseenter', (e) => {
                this.handleBadgeHover(e.target, true);
            });
            
            badge.addEventListener('mouseleave', (e) => {
                this.handleBadgeHover(e.target, false);
            });
            
            // تفاعل النقر
            badge.addEventListener('click', (e) => {
                this.handleBadgeClick(e.target);
            });
        });
        
        this.log('info', `تم تهيئة ${badges.length} شارة للتفاعل`);
    },

    /**
     * تهيئة التلميحات
     */
    initializeTooltips() {
        const badgesWithTooltips = document.querySelectorAll('[title]');
        
        badgesWithTooltips.forEach(element => {
            // إنشاء تلميح مخصص
            this.createCustomTooltip(element);
        });
        
        this.log('info', `تم تهيئة ${badgesWithTooltips.length} تلميح`);
    },

    /**
     * تهيئة الحركات والتأثيرات
     */
    initializeAnimations() {
        // تأثير الظهور التدريجي للشارات
        const badgeContainers = document.querySelectorAll('.badges-container');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateBadgesIn(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
        
        badgeContainers.forEach(container => {
            observer.observe(container);
        });
        
        this.log('info', `تم تهيئة الحركات لـ ${badgeContainers.length} حاوي شارات`);
    },

    /**
     * إعداد مستمعي الأحداث العامة
     */
    setupEventListeners() {
        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // مراقبة تغيير الوضع المظلم
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addEventListener('change', (e) => {
                this.handleDarkModeChange(e.matches);
            });
        }
    },

    /**
     * معالجة تفاعل الماوس مع الشارات
     */
    handleBadgeHover(badge, isHovering) {
        if (isHovering) {
            badge.style.transform = 'translateY(-2px) scale(1.05)';
            badge.style.zIndex = '20';
            
            // إضافة تأثير الإضاءة
            this.addGlowEffect(badge);
        } else {
            badge.style.transform = 'translateY(0) scale(1)';
            badge.style.zIndex = '10';
            
            // إزالة تأثير الإضاءة
            this.removeGlowEffect(badge);
        }
    },

    /**
     * معالجة النقر على الشارة
     */
    handleBadgeClick(badge) {
        // تأثير النقر
        badge.style.transform = 'translateY(0) scale(0.95)';
        
        setTimeout(() => {
            badge.style.transform = 'translateY(-2px) scale(1.05)';
        }, 150);
        
        // تسجيل التفاعل
        this.state.badgeInteractions++;
        this.log('info', 'تفاعل مع الشارة', {
            badgeType: badge.className,
            interactions: this.state.badgeInteractions
        });
        
        // إظهار معلومات إضافية
        this.showBadgeInfo(badge);
    },

    /**
     * إنشاء تلميح مخصص
     */
    createCustomTooltip(element) {
        const title = element.getAttribute('title');
        if (!title) return;
        
        // إزالة title الأصلي لمنع التلميح الافتراضي
        element.removeAttribute('title');
        element.setAttribute('data-original-title', title);
        
        let tooltip = null;
        
        element.addEventListener('mouseenter', () => {
            tooltip = this.showCustomTooltip(element, title);
        });
        
        element.addEventListener('mouseleave', () => {
            if (tooltip) {
                this.hideCustomTooltip(tooltip);
                tooltip = null;
            }
        });
    },

    /**
     * إظهار تلميح مخصص
     */
    showCustomTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'custom-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            pointer-events: none;
            white-space: nowrap;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(tooltip);
        
        // حساب الموضع
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        tooltip.style.left = `${rect.left + (rect.width - tooltipRect.width) / 2}px`;
        tooltip.style.top = `${rect.bottom + 8}px`;
        
        // إظهار التلميح
        setTimeout(() => {
            tooltip.style.opacity = '1';
            tooltip.style.transform = 'translateY(0)';
        }, 100);
        
        return tooltip;
    },

    /**
     * إخفاء تلميح مخصص
     */
    hideCustomTooltip(tooltip) {
        tooltip.style.opacity = '0';
        tooltip.style.transform = 'translateY(10px)';
        
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 300);
    },

    /**
     * إضافة تأثير الإضاءة
     */
    addGlowEffect(badge) {
        const glowColor = this.getBadgeGlowColor(badge);
        badge.style.boxShadow = `0 4px 20px ${glowColor}`;
    },

    /**
     * إزالة تأثير الإضاءة
     */
    removeGlowEffect(badge) {
        badge.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
    },

    /**
     * الحصول على لون الإضاءة للشارة
     */
    getBadgeGlowColor(badge) {
        if (badge.classList.contains('new-badge')) return 'rgba(16, 185, 129, 0.5)';
        if (badge.classList.contains('featured-badge')) return 'rgba(245, 158, 11, 0.5)';
        if (badge.classList.contains('free-badge')) return 'rgba(6, 182, 212, 0.5)';
        if (badge.classList.contains('discount-badge')) return 'rgba(249, 115, 22, 0.5)';
        if (badge.classList.contains('limited-badge')) return 'rgba(236, 72, 153, 0.5)';
        if (badge.classList.contains('popular-badge')) return 'rgba(59, 130, 246, 0.5)';
        return 'rgba(0, 0, 0, 0.3)';
    },

    /**
     * تحريك الشارات عند الظهور
     */
    animateBadgesIn(container) {
        const badges = container.querySelectorAll('.status-badge, .feature-badge, .warning-badge, .achievement-badge');
        
        badges.forEach((badge, index) => {
            badge.style.opacity = '0';
            badge.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                badge.style.transition = 'all 0.5s ease';
                badge.style.opacity = '1';
                badge.style.transform = 'translateY(0)';
            }, index * 100);
        });
    },

    /**
     * إظهار معلومات الشارة
     */
    showBadgeInfo(badge) {
        const badgeType = this.getBadgeType(badge);
        const info = this.getBadgeInfo(badgeType);
        
        if (info) {
            this.showToast(info, 'info');
        }
    },

    /**
     * الحصول على نوع الشارة
     */
    getBadgeType(badge) {
        const classes = badge.className.split(' ');
        for (const cls of classes) {
            if (cls.endsWith('-badge')) {
                return cls.replace('-badge', '');
            }
        }
        return 'unknown';
    },

    /**
     * الحصول على معلومات الشارة
     */
    getBadgeInfo(type) {
        const info = {
            'new': 'هذا إعلان جديد تم نشره خلال آخر 3 أيام',
            'featured': 'هذا إعلان مميز يظهر في المقدمة',
            'free': 'هذا إعلان مجاني بدون تكلفة',
            'negotiable': 'يمكن التفاوض على سعر هذا الإعلان',
            'limited': 'هذا عرض محدود لفترة قصيرة',
            'discount': 'يوجد خصم على هذا الإعلان',
            'popular': 'هذا إعلان شائع بمشاهدات عالية',
            'expires_soon': 'هذا الإعلان سينتهي قريباً',
            'discount_expires': 'الخصم على هذا الإعلان سينتهي قريباً'
        };
        
        return info[type] || null;
    },

    /**
     * معالجة تغيير حجم النافذة
     */
    handleResize() {
        this.log('info', 'تغيير حجم النافذة - إعادة ترتيب الشارات');
        // إعادة حساب مواضع التلميحات إذا لزم الأمر
    },

    /**
     * معالجة تغيير الوضع المظلم
     */
    handleDarkModeChange(isDark) {
        this.log('info', 'تغيير الوضع المظلم', { isDark });
        // تطبيق تعديلات الوضع المظلم على الشارات
    },

    /**
     * إظهار رسالة Toast
     */
    showToast(message, type = 'info') {
        // استخدام نظام Toast الموحد إذا كان متوفراً
        if (typeof showToast === 'function') {
            showToast(message, type);
            return;
        }

        // إنشاء Toast مخصص
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    },

    /**
     * دالة تأخير التنفيذ
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * تسجيل الأحداث
     */
    log(level, message, data = null) {
        if (this.config.logLevel === 'none') return;

        const logData = {
            timestamp: new Date().toISOString(),
            component: 'StatusBadges',
            message,
            data
        };

        switch (level) {
            case 'error':
                console.error('[StatusBadges]', message, data);
                break;
            case 'warn':
                console.warn('[StatusBadges]', message, data);
                break;
            case 'info':
            default:
                console.log('[StatusBadges]', message, data);
                break;
        }
    }
};

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    StatusBadges.init();
});

// تصدير للاستخدام العام
window.StatusBadges = StatusBadges;
