
<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
    
    <?php if(session('success')): ?>
        <div class="toast align-items-center text-white bg-success border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo e(session('success')); ?>

                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    
    <?php if(session('error')): ?>
        <div class="toast align-items-center text-white bg-danger border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo e(session('error')); ?>

                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    
    <?php if(session('warning')): ?>
        <div class="toast align-items-center text-dark bg-warning border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo e(session('warning')); ?>

                </div>
                <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    
    <?php if(session('info')): ?>
        <div class="toast align-items-center text-white bg-info border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php echo e(session('info')); ?>

                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>
</div>


<style>
.toast-container {
    z-index: 9999 !important;
}

.toast {
    min-width: 300px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
    font-family: 'Cairo', sans-serif;
    animation: slideInRight 0.3s ease-out;
}

.toast.show {
    opacity: 1;
}

.toast .toast-body {
    padding: 1rem;
    font-size: 0.95rem;
    font-weight: 500;
}

.toast .btn-close {
    padding: 0.5rem;
}

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast.hiding {
    animation: slideOutRight 0.3s ease-in;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 576px) {
    .toast-container {
        left: 1rem;
        right: 1rem;
        top: 1rem;
    }

    .toast {
        min-width: auto;
        width: 100%;
    }
}

/* ألوان مخصصة للتوست */
.toast.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.toast.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%) !important;
}

.toast.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%) !important;
}

.toast.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%) !important;
}

/* تحسين الخط العربي */
.toast .toast-body {
    direction: <?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>;
    text-align: <?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?>;
}
</style>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل جميع التوست بطريقة مبسطة
    document.querySelectorAll('.toast').forEach(function(toastEl) {
        const toast = new bootstrap.Toast(toastEl, {
            autohide: true,
            delay: 3000
        });

        toast.show();

        // إضافة تأثير الاختفاء وإزالة من DOM
        toastEl.addEventListener('hide.bs.toast', () => toastEl.classList.add('hiding'));
        toastEl.addEventListener('hidden.bs.toast', () => {
            setTimeout(() => toastEl.remove(), 300);
        });
    });
});

// دالة موحدة لإنشاء توست جديد - محسنة ومطورة
function showToast(message, type = 'success', duration = 3000) {
    // منع الرسائل المكررة
    const existingToasts = document.querySelectorAll('.custom-toast');
    for (let toast of existingToasts) {
        if (toast.textContent.includes(message)) {
            return; // لا تظهر نفس الرسالة مرتين
        }
    }

    // إنشاء حاوي Toast إذا لم يكن موجوداً
    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }

    // تحسين التكوين مع إضافة المزيد من الأنواع
    const config = {
        success: { icon: 'fa-check-circle', bg: 'bg-success', text: 'text-white', title: 'نجح' },
        error: { icon: 'fa-exclamation-circle', bg: 'bg-danger', text: 'text-white', title: 'خطأ' },
        warning: { icon: 'fa-exclamation-triangle', bg: 'bg-warning', text: 'text-dark', title: 'تحذير' },
        info: { icon: 'fa-info-circle', bg: 'bg-info', text: 'text-white', title: 'معلومات' }
    };

    const { icon, bg, text, title } = config[type] || config.info;
    const closeClass = type === 'warning' ? 'btn-close' : 'btn-close btn-close-white';
    const toastId = 'toast-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5);

    // تحسين HTML مع إضافة عنوان
    container.insertAdjacentHTML('beforeend', `
        <div id="${toastId}" class="toast custom-toast align-items-center ${text} ${bg} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <div class="d-flex align-items-center">
                        <i class="fas ${icon} me-2"></i>
                        <div>
                            <small class="fw-bold">${title}</small>
                            <div>${message}</div>
                        </div>
                    </div>
                </div>
                <button type="button" class="${closeClass} me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
            </div>
        </div>
    `);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: duration > 0,
        delay: duration
    });

    // إضافة تأثير الظهور
    setTimeout(() => {
        toast.show();
    }, 10);

    // تحسين event listeners
    toastElement.addEventListener('hide.bs.toast', () => {
        toastElement.classList.add('hiding');
    });

    toastElement.addEventListener('hidden.bs.toast', () => {
        setTimeout(() => {
            if (toastElement.parentNode) {
                toastElement.remove();
            }
        }, 300);
    });

    return toastId; // إرجاع ID للتحكم اللاحق
}

// تصدير الدالة للاستخدام العام
window.showToast = showToast;
</script>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/toast.blade.php ENDPATH**/ ?>