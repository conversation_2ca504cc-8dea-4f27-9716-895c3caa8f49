{{-- مكون عرض التقييمات والمراجعات المتقدم --}}
@props(['ad', 'detailed' => true, 'showForm' => true, 'limit' => null])

{{-- إضافة Logging لعرض التقييمات --}}
@php
    if ($detailed) {
        Log::info('عرض التقييمات والمراجعات المتقدمة', [
            'ad_id' => $ad->id,
            'ad_title' => $ad->title,
            'reviews_count' => $ad->reviews_count,
            'average_rating' => $ad->average_rating,
            'user_has_reviewed' => $ad->hasUserReviewed(),
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'timestamp' => now()
        ]);
    }
    
    // الحصول على التقييمات
    $reviews = $ad->approvedReviews()
                  ->with(['user'])
                  ->orderBy('created_at', 'desc');
    
    if ($limit) {
        $reviews = $reviews->limit($limit);
    }
    
    $reviews = $reviews->get();
    
    // إحصائيات التقييمات
    $averageRating = $ad->average_rating;
    $totalReviews = $ad->reviews_count;
    $ratingDistribution = $ad->rating_distribution;
    $userHasReviewed = $ad->hasUserReviewed();
    $userReview = $ad->getUserReview();
@endphp

<div class="reviews-section {{ $detailed ? 'reviews-detailed' : 'reviews-compact' }}">
    {{-- رأس قسم التقييمات --}}
    <div class="reviews-header">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="reviews-title">
                <i class="fas fa-star text-warning me-2"></i>
                {{ __('Reviews & Ratings') }}
                <span class="reviews-count">({{ $totalReviews }})</span>
            </h4>
            
            @if($showForm && auth()->check() && !$userHasReviewed)
                <button class="btn btn-outline-primary btn-sm" onclick="ReviewsManager.showAddReviewForm()">
                    <i class="fas fa-plus me-1"></i>
                    {{ __('Add Review') }}
                </button>
            @endif
        </div>
        
        {{-- ملخص التقييمات --}}
        @if($totalReviews > 0)
            <div class="reviews-summary">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="average-rating-display">
                            <div class="rating-number">{{ number_format($averageRating, 1) }}</div>
                            <div class="rating-stars">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= $averageRating ? 'text-warning' : 'text-muted' }}"></i>
                                @endfor
                            </div>
                            <div class="rating-text">{{ __('out of 5 stars') }}</div>
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <div class="rating-distribution">
                            @for($i = 5; $i >= 1; $i--)
                                @php
                                    $count = $ratingDistribution[$i] ?? 0;
                                    $percentage = $totalReviews > 0 ? ($count / $totalReviews) * 100 : 0;
                                @endphp
                                <div class="rating-bar-row">
                                    <span class="rating-label">{{ $i }} <i class="fas fa-star text-warning"></i></span>
                                    <div class="rating-bar">
                                        <div class="rating-bar-fill" style="width: {{ $percentage }}%"></div>
                                    </div>
                                    <span class="rating-count">{{ $count }}</span>
                                </div>
                            @endfor
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    {{-- نموذج إضافة تقييم --}}
    @if($showForm && auth()->check())
        <div class="add-review-form" id="addReviewForm" style="display: {{ $userHasReviewed ? 'none' : 'block' }};">
            <div class="review-form-card">
                <h5 class="mb-3">
                    <i class="fas fa-edit me-2"></i>
                    {{ $userHasReviewed ? __('Update Your Review') : __('Add Your Review') }}
                </h5>
                
                <form id="reviewForm" onsubmit="ReviewsManager.submitReview(event)">
                    @csrf
                    <input type="hidden" name="ad_id" value="{{ $ad->id }}">
                    
                    {{-- تقييم النجوم --}}
                    <div class="mb-3">
                        <label class="form-label">{{ __('Your Rating') }}</label>
                        <div class="star-rating-input" id="starRating">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star star-input" data-rating="{{ $i }}" 
                                   onclick="ReviewsManager.setRating({{ $i }})"></i>
                            @endfor
                        </div>
                        <input type="hidden" name="rating" id="ratingInput" value="{{ $userReview->rating ?? '' }}" required>
                    </div>
                    
                    {{-- عنوان المراجعة --}}
                    <div class="mb-3">
                        <label for="reviewTitle" class="form-label">{{ __('Review Title') }}</label>
                        <input type="text" class="form-control" id="reviewTitle" name="title" 
                               value="{{ $userReview->title ?? '' }}" 
                               placeholder="{{ __('Summarize your experience') }}" maxlength="100">
                    </div>
                    
                    {{-- نص المراجعة --}}
                    <div class="mb-3">
                        <label for="reviewComment" class="form-label">{{ __('Your Review') }}</label>
                        <textarea class="form-control" id="reviewComment" name="comment" rows="4" 
                                  placeholder="{{ __('Share your experience with this ad') }}" 
                                  maxlength="1000">{{ $userReview->comment ?? '' }}</textarea>
                        <div class="form-text">{{ __('Maximum 1000 characters') }}</div>
                    </div>
                    
                    {{-- أزرار التحكم --}}
                    <div class="review-form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {{ $userReview ? __('Update Review') : __('Submit Review') }}
                        </button>
                        
                        @if($userReview)
                            <button type="button" class="btn btn-outline-danger ms-2" 
                                    onclick="ReviewsManager.deleteReview({{ $userReview->id }})">
                                <i class="fas fa-trash me-1"></i>
                                {{ __('Delete Review') }}
                            </button>
                        @endif
                        
                        <button type="button" class="btn btn-outline-secondary ms-2" 
                                onclick="ReviewsManager.hideAddReviewForm()">
                            <i class="fas fa-times me-1"></i>
                            {{ __('Cancel') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    @endif

    {{-- قائمة التقييمات --}}
    <div class="reviews-list">
        @if($reviews->count() > 0)
            @foreach($reviews as $review)
                <div class="review-item" data-review-id="{{ $review->id }}">
                    <div class="review-header">
                        <div class="reviewer-info">
                            <div class="reviewer-avatar">
                                @if($review->user->avatar)
                                    <img src="{{ $review->user->avatar_url }}" alt="{{ $review->user->name }}" class="avatar-sm">
                                @else
                                    <div class="avatar-placeholder-sm">
                                        <i class="fas fa-user"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="reviewer-details">
                                <div class="reviewer-name">
                                    {{ $review->user->name }}
                                    @if($review->user->is_verified)
                                        <i class="fas fa-check-circle text-success ms-1" title="{{ __('Verified User') }}"></i>
                                    @endif
                                    @if($review->is_verified)
                                        <span class="badge badge-verified ms-1">{{ __('Verified Purchase') }}</span>
                                    @endif
                                </div>
                                
                                <div class="review-meta">
                                    <div class="review-rating">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star {{ $i <= $review->rating ? 'text-warning' : 'text-muted' }}"></i>
                                        @endfor
                                    </div>
                                    <span class="review-date">{{ $review->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="review-actions">
                            @auth
                                <button class="btn btn-sm btn-outline-secondary" 
                                        onclick="ReviewsManager.toggleHelpful({{ $review->id }})">
                                    <i class="fas fa-thumbs-up me-1"></i>
                                    {{ __('Helpful') }} ({{ $review->helpful_count }})
                                </button>
                                
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="#" 
                                               onclick="ReviewsManager.reportReview({{ $review->id }})">
                                                <i class="fas fa-flag me-2"></i>{{ __('Report') }}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            @endauth
                        </div>
                    </div>
                    
                    <div class="review-content">
                        @if($review->title)
                            <h6 class="review-title">{{ $review->title }}</h6>
                        @endif
                        
                        @if($review->comment)
                            <p class="review-text">{{ $review->comment }}</p>
                        @endif
                    </div>
                </div>
            @endforeach
            
            {{-- رابط عرض المزيد --}}
            @if($limit && $totalReviews > $limit)
                <div class="text-center mt-4">
                    <a href="#" class="btn btn-outline-primary" onclick="ReviewsManager.loadMoreReviews()">
                        <i class="fas fa-chevron-down me-1"></i>
                        {{ __('Load More Reviews') }} ({{ $totalReviews - $limit }} {{ __('remaining') }})
                    </a>
                </div>
            @endif
        @else
            <div class="no-reviews">
                <div class="text-center py-5">
                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{{ __('No Reviews Yet') }}</h5>
                    <p class="text-muted">{{ __('Be the first to review this ad') }}</p>
                    
                    @if($showForm && auth()->check() && !$userHasReviewed)
                        <button class="btn btn-primary mt-3" onclick="ReviewsManager.showAddReviewForm()">
                            <i class="fas fa-star me-1"></i>
                            {{ __('Write First Review') }}
                        </button>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>
