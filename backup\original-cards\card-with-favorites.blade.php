{{-- بطاقة الإعلان المحسنة مع نظام المفضلة --}}
@if(!isset($noWrapper) || !$noWrapper)
<div class="col-xl-4 col-lg-6 col-md-6 mb-5">
@endif
<div class="card ad-card h-100 shadow-sm border-0" data-ad-id="{{ $ad->id }}">
        <div class="card-header-custom">
            <!-- صورة الإعلان -->
            <div class="ad-image">
                @if($ad->image_url)
                    <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}" class="card-img-top">
                @else
                    <div class="placeholder-image">
                        <i class="{{ $ad->category->icon }} fa-4x text-muted"></i>
                    </div>
                @endif

                <!-- أيقونة المفضلة -->
                @auth
                    <div class="favorite-icon" data-ad-id="{{ $ad->id }}">
                        <button class="btn-favorite {{ $ad->isFavoritedBy(auth()->id()) ? 'favorited' : '' }}"
                                title="{{ $ad->isFavoritedBy(auth()->id()) ? __('Remove from Favorites') : __('Add to Favorites') }}">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                @endauth

                <!-- شارة الحالة في أعلى يمين الصورة -->
                @if($ad->created_at->diffInDays(now()) <= 3)
                    <div class="category-badge new-badge">
                        <i class="fas fa-star me-1"></i>
                        <span class="arabic-text">{{ __('New') }}</span>
                    </div>
                @endif
            </div>
        </div>

        <div class="card-body p-3" style="padding: 1.5rem !important;">
            <!-- شارة التصنيف -->
            <div class="category-badge-body mb-2">
                <i class="{{ $ad->category->icon }} me-1"></i>
                <span class="arabic-text">{{ $ad->category->name }}</span>
            </div>

            <!-- عنوان الإعلان -->
            <h5 class="ad-title arabic-text fw-bold">
                <a href="{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}" class="text-decoration-none text-dark">
                    {{ $ad->title }}
                </a>
            </h5>

            <!-- وصف مختصر -->
            <p class="ad-description text-muted arabic-text">
                {{ Str::limit($ad->description, 100) }}
            </p>

            <!-- معلومات إضافية -->
            <div class="ad-meta mb-2">
                <div class="row g-2">
                    <!-- الموقع -->
                    @if($ad->location)
                        <div class="col-6">
                            <div class="meta-item">
                                <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                <small class="arabic-text">{{ $ad->location }}</small>
                            </div>
                        </div>
                    @endif

                    <!-- عدد المشاهدات -->
                    <div class="col-6">
                        <div class="meta-item">
                            <i class="fas fa-eye text-info me-1"></i>
                            <small class="arabic-text">{{ $ad->views_count }} {{ __('views') }}</small>
                        </div>
                    </div>

                    <!-- تاريخ النشر -->
                    <div class="col-12">
                        <div class="meta-item">
                            <i class="fas fa-calendar text-success me-1"></i>
                            <small class="arabic-text">{{ $ad->created_at->diffForHumans() }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات التواصل -->
            @if($ad->contact_info)
                <div class="contact-info mb-2">
                    <div class="contact-preview">
                        <i class="fas fa-phone text-primary me-2"></i>
                        <small class="text-muted arabic-text">{{ __('Contact available') }}</small>
                    </div>
                </div>
            @endif

            <!-- قسم الأسعار المتقدم -->
            <div class="price-section">
                @if($ad->is_free)
                    <!-- إعلان مجاني -->
                    <div class="price-free">
                        <i class="fas fa-gift me-2"></i>
                        <span class="free-text">مجاني</span>
                    </div>
                @elseif($ad->price_type === 'on_request')
                    <!-- السعر عند الطلب -->
                    <div class="price-on-request">
                        <i class="fas fa-phone me-2"></i>
                        <span class="request-text">السعر عند الطلب</span>
                    </div>
                @elseif($ad->price)
                    <div class="price-container">
                        @if($ad->hasDiscount())
                            <!-- عرض مع خصم -->
                            <div class="price-with-discount">
                                <!-- نسبة الخصم -->
                                <div class="discount-badge">
                                    <i class="fas fa-tag me-1"></i>
                                    خصم {{ $ad->calculated_discount_percentage }}%
                                </div>

                                <!-- السعر الأصلي -->
                                <div class="original-price">
                                    <span class="price-label">كان:</span>
                                    <span class="price-value">{{ $ad->formatted_original_price }}</span>
                                </div>

                                <!-- السعر الحالي -->
                                <div class="current-price">
                                    <span class="price-value">{{ $ad->formatted_price }}</span>
                                </div>

                                <!-- مبلغ التوفير -->
                                <div class="savings-amount">
                                    <i class="fas fa-piggy-bank me-1"></i>
                                    وفر {{ number_format($ad->savings_amount, 0) }} {{ $ad->currency === 'YER' ? 'ر.ي' : $ad->currency }}
                                </div>
                            </div>
                        @else
                            <!-- سعر عادي -->
                            <div class="regular-price">
                                <div class="current-price">
                                    <span class="price-value">{{ $ad->formatted_price }}</span>
                                </div>
                            </div>
                        @endif

                        @if($ad->is_negotiable)
                            <div class="negotiable-badge">
                                <i class="fas fa-handshake me-1"></i>
                                قابل للتفاوض
                            </div>
                        @endif

                        @if($ad->is_limited_offer)
                            <div class="limited-offer-badge">
                                <i class="fas fa-clock me-1"></i>
                                عرض محدود
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>

        <div class="card-footer bg-transparent border-0 p-3 pt-0">
            <!-- أزرار الإجراءات -->
            <div class="ad-actions">
                <div class="row g-2">
                    <div class="col-8">
                        <a href="{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}"
                           class="btn btn-primary btn-sm w-100 arabic-text">
                            <i class="fas fa-eye me-2"></i>
                            {{ __('View Details') }}
                        </a>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-outline-secondary btn-sm w-100"
                                onclick="shareAd('{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}', '{{ $ad->title }}')"
                                title="{{ __('Share') }}">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- تاريخ انتهاء الصلاحية مع تحذير القرب -->
            @if($ad->expires_at)
                <div class="expiry-info mt-2 text-center d-flex align-items-center justify-content-center gap-2">
                    <small class="text-muted arabic-text">
                        <i class="fas fa-hourglass-half me-1"></i>
                        {{ __('Expires') }}: {{ $ad->expires_at->format('Y/m/d') }}
                    </small>
                    @if($ad->expires_at->diffInDays(now()) <= 7)
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-clock me-1"></i>
                            ينتهي قريباً
                        </span>
                    @endif
                </div>
            @endif
        </div>

        <!-- تأثير hover -->
        <div class="card-overlay"></div>
    </div>
@if(!isset($noWrapper) || !$noWrapper)
</div>
@endif

@push('styles')
<style>
/* بطاقة الإعلان */
.ad-card {
    background: white;
    border-radius: 18px;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid #e9ecef;
    min-height: 550px;
}

.ad-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* صورة الإعلان */
.ad-image {
    position: relative;
    height: 280px;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
}

.ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* أيقونة المفضلة */
.favorite-icon {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
}

.btn-favorite {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-favorite:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-favorite i {
    font-size: 18px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.btn-favorite.favorited i {
    color: #dc3545;
    animation: heartBeat 0.6s ease-in-out;
}

.btn-favorite:hover i {
    color: #dc3545;
}

/* تأثير نبضة القلب */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* شارات الحالة */
.category-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    backdrop-filter: blur(10px);
}

/* شارات الحالة المخصصة */
.category-badge.urgent-badge {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.category-badge.new-badge {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.status-badge {
    position: absolute;
    top: 60px;
    left: 10px;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: bold;
}

.status-badge.new {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.status-badge.urgent {
    background: rgba(220, 53, 69, 0.9);
    color: white;
}

/* باقي الأنماط كما هي في الملف الأصلي */
.ad-title {
    font-size: 1.2rem;
    line-height: 1.4;
    min-height: 2.5rem;
    margin-bottom: 1rem;
}

.ad-title a:hover {
    color: #007bff !important;
}

.ad-description {
    font-size: 0.95rem;
    line-height: 1.5;
    min-height: 2.5rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.meta-item i {
    width: 16px;
    text-align: center;
}

.contact-preview {
    background: rgba(0, 123, 255, 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
}

/* قسم الأسعار المتقدم */
.price-section {
    padding: 1.5rem;
    margin: 1rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid rgba(0, 123, 255, 0.1);
}

/* السعر المجاني */
.price-free {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 8px;
    font-size: 1.2rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.price-free i {
    font-size: 1.3rem;
    animation: bounce 2s infinite;
}

/* السعر عند الطلب */
.price-on-request {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
}

/* حاوي السعر */
.price-container {
    position: relative;
}

/* السعر مع خصم */
.price-with-discount {
    position: relative;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* شارة الخصم */
.discount-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    animation: pulse 2s infinite;
}

/* السعر الأصلي */
.original-price {
    text-align: center;
    margin-bottom: 0.5rem;
}

.original-price .price-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-right: 0.5rem;
}

.original-price .price-value {
    font-size: 1rem;
    color: #dc3545;
    text-decoration: line-through;
    font-weight: 500;
}

/* السعر الحالي */
.current-price {
    text-align: center;
    margin-bottom: 1rem;
}

.current-price .price-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #28a745;
    text-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

/* مبلغ التوفير */
.savings-amount {
    text-align: center;
    padding: 0.5rem;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

/* السعر العادي */
.regular-price .current-price .price-value {
    font-size: 1.6rem;
    font-weight: 600;
    color: #007bff;
}

/* شارة قابل للتفاوض */
.negotiable-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0.5rem;
    margin-right: 0.5rem;
}

/* شارة عرض محدود */
.limited-offer-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0.5rem;
    animation: blink 1.5s infinite;
}

/* الأنيميشن والتأثيرات */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0.7;
    }
}

/* تأثيرات hover للأسعار */
.price-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    transition: all 0.3s ease;
}

.price-free:hover,
.price-on-request:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.price-with-discount:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.ad-actions .btn {
    border-radius: 8px;
    font-size: 0.85rem;
    padding: 0.5rem;
}

.expiry-info {
    background: rgba(255, 193, 7, 0.1);
    padding: 0.25rem;
    border-radius: 5px;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(0, 123, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.ad-card:hover .card-overlay {
    opacity: 1;
}

/* العرض القائمة المحسن */
.list-view .ad-card {
    flex-direction: row !important;
    align-items: stretch !important;
    min-height: 220px;
    border-radius: 18px;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.list-view .ad-image {
    width: 280px;
    height: 200px;
    flex-shrink: 0;
    border-radius: 18px 0 0 18px;
}

.list-view .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 2rem !important;
}

.list-view .ad-title {
    min-height: auto;
    font-size: 1.4rem;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.list-view .ad-description {
    min-height: auto;
    flex: 1;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* تأثيرات hover لعرض القائمة */
.list-view .ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* تحسين معلومات الإعلان في عرض القائمة */
.list-view .ad-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.list-view .meta-item {
    font-size: 0.95rem;
    color: #6c757d;
}

/* تحسين الأزرار في عرض القائمة */
.list-view .ad-actions {
    margin-top: auto;
    padding-top: 1rem;
}

.list-view .ad-actions .btn {
    font-size: 0.95rem;
    padding: 0.6rem 1.2rem;
    margin-right: 0.5rem;
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .ad-image {
        height: 320px;
    }

    .ad-card {
        min-height: 600px;
    }

    /* تحسينات عرض القائمة للشاشات الكبيرة جداً */
    .list-view .ad-image {
        width: 320px;
        height: 240px;
    }

    .list-view .ad-card {
        min-height: 260px;
    }

    .list-view .ad-title {
        font-size: 1.5rem;
    }

    .list-view .ad-description {
        font-size: 1.15rem;
    }
}

/* تحسينات للشاشات المتوسطة */
@media (min-width: 992px) and (max-width: 1399px) {
    .ad-image {
        height: 300px;
    }

    .ad-card {
        min-height: 580px;
    }

    /* تحسينات عرض القائمة للشاشات المتوسطة */
    .list-view .ad-image {
        width: 300px;
        height: 220px;
    }

    .list-view .ad-card {
        min-height: 240px;
    }

    .list-view .ad-title {
        font-size: 1.35rem;
    }

    .list-view .ad-description {
        font-size: 1.05rem;
    }
}

/* تحسينات للتابلت في عرض القائمة */
@media (min-width: 769px) and (max-width: 991px) {
    .list-view .ad-image {
        width: 250px;
        height: 180px;
    }

    .list-view .ad-card {
        min-height: 200px;
    }

    .list-view .ad-title {
        font-size: 1.25rem;
    }

    .list-view .ad-description {
        font-size: 1rem;
    }

    .list-view .card-body {
        padding: 1.5rem !important;
    }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .ad-image {
        height: 250px;
    }

    .btn-favorite {
        width: 35px;
        height: 35px;
    }
    
    .btn-favorite i {
        font-size: 16px;
    }
    
    .status-badge {
        top: 55px;
    }

    /* تحسينات عرض القائمة للشاشات الصغيرة */
    .list-view .ad-card {
        flex-direction: column !important;
        min-height: 420px;
        margin-bottom: 1.5rem;
    }

    .list-view .ad-image {
        width: 100%;
        height: 250px;
        border-radius: 18px 18px 0 0;
    }

    .list-view .card-body {
        padding: 1.5rem !important;
    }

    .list-view .ad-title {
        font-size: 1.2rem;
    }

    .list-view .ad-description {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .ad-image {
        height: 220px;
    }

    .btn-favorite {
        width: 32px;
        height: 32px;
    }
    
    .btn-favorite i {
        font-size: 14px;
    }
    
    .status-badge {
        top: 50px;
    }

    /* تحسينات عرض القائمة للشاشات الصغيرة جداً */
    .list-view .ad-card {
        min-height: 380px;
        margin-bottom: 1rem;
    }

    .list-view .ad-image {
        height: 200px;
    }

    .list-view .card-body {
        padding: 1rem !important;
    }

    .list-view .ad-title {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
    }

    .list-view .ad-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .list-view .ad-actions .btn {
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
    }
}
</style>
@endpush

