<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Interaction;
use App\Models\Ad;
use App\Models\SystemLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

/**
 * Controller موحد للتفاعلات
 * يحل محل: FavoriteController, RatingController, CommentController
 */
class InteractionController extends Controller
{
    /**
     * إضافة/إزالة من المفضلة
     */
    public function toggleFavorite(Request $request, $adId)
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تسجيل الدخول أولاً'
                ], 401);
            }

            $ad = Ad::findOrFail($adId);
            $user = Auth::user();

            $isFavorited = $user->hasFavorited($adId);

            if ($isFavorited) {
                $user->removeFromFavorites($adId);
                $ad->decrement('favorites_count');
                $message = 'تم إزالة الإعلان من المفضلة';
                $favorited = false;
            } else {
                $user->addToFavorites($adId);
                $ad->increment('favorites_count');
                $message = 'تم إضافة الإعلان للمفضلة';
                $favorited = true;
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'favorited' => $favorited,
                'favorites_count' => $ad->fresh()->favorites_count
            ]);

        } catch (\Exception $e) {
            SystemLog::logError('favorite_toggle_error', 'خطأ في تبديل المفضلة', [
                'ad_id' => $adId,
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في العملية'
            ], 500);
        }
    }

    /**
     * إضافة تقييم
     */
    public function addRating(Request $request, $adId)
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تسجيل الدخول أولاً'
                ], 401);
            }

            $validator = Validator::make($request->all(), [
                'rating' => 'required|integer|min:1|max:5',
                'comment' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $ad = Ad::findOrFail($adId);
            $userId = Auth::id();

            // التحقق من عدم وجود تقييم سابق
            $existingRating = Interaction::where('user_id', $userId)
                ->where('ad_id', $adId)
                ->where('type', 'rating')
                ->first();

            if ($existingRating) {
                return response()->json([
                    'success' => false,
                    'message' => 'لقد قمت بتقييم هذا الإعلان مسبقاً'
                ], 409);
            }

            $rating = Interaction::addRating(
                $userId,
                $adId,
                $request->rating,
                $request->comment
            );

            // تحديث إحصائيات الإعلان
            $ad->updateStatsFromInteractions();

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة التقييم بنجاح',
                'rating' => $rating,
                'new_average' => $ad->fresh()->rating_average
            ]);

        } catch (\Exception $e) {
            SystemLog::logError('rating_add_error', 'خطأ في إضافة التقييم', [
                'ad_id' => $adId,
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في إضافة التقييم'
            ], 500);
        }
    }

    /**
     * إضافة تعليق
     */
    public function addComment(Request $request, $adId)
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تسجيل الدخول أولاً'
                ], 401);
            }

            $validator = Validator::make($request->all(), [
                'content' => 'required|string|min:3|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $ad = Ad::findOrFail($adId);
            $userId = Auth::id();

            $comment = Interaction::addComment(
                $userId,
                $adId,
                $request->content
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة التعليق بنجاح',
                'comment' => $comment->load('user:id,name')
            ]);

        } catch (\Exception $e) {
            SystemLog::logError('comment_add_error', 'خطأ في إضافة التعليق', [
                'ad_id' => $adId,
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في إضافة التعليق'
            ], 500);
        }
    }

    /**
     * الحصول على تفاعلات الإعلان
     */
    public function getAdInteractions($adId, Request $request)
    {
        try {
            $ad = Ad::findOrFail($adId);
            $type = $request->get('type', 'all'); // all, favorite, rating, comment, view

            $query = $ad->interactions();

            if ($type !== 'all') {
                $query->where('type', $type);
            }

            $interactions = $query->with('user:id,name')
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return response()->json([
                'success' => true,
                'interactions' => $interactions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب التفاعلات'
            ], 500);
        }
    }

    /**
     * الحصول على تفاعلات المستخدم
     */
    public function getUserInteractions(Request $request)
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تسجيل الدخول أولاً'
                ], 401);
            }

            $user = Auth::user();
            $type = $request->get('type', 'all');

            $query = $user->interactions();

            if ($type !== 'all') {
                $query->where('type', $type);
            }

            $interactions = $query->with(['ad:id,title_ar,title_en,slug,main_image'])
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return response()->json([
                'success' => true,
                'interactions' => $interactions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب التفاعلات'
            ], 500);
        }
    }

    /**
     * حذف تفاعل
     */
    public function deleteInteraction($interactionId)
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تسجيل الدخول أولاً'
                ], 401);
            }

            $interaction = Interaction::where('id', $interactionId)
                ->where('user_id', Auth::id())
                ->firstOrFail();

            $interaction->delete();

            // تحديث إحصائيات الإعلان
            $ad = Ad::find($interaction->ad_id);
            if ($ad) {
                $ad->updateStatsFromInteractions();
            }

            return response()->json([
                'success' => true,
                'message' => 'تم حذف التفاعل بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في حذف التفاعل'
            ], 500);
        }
    }
}
