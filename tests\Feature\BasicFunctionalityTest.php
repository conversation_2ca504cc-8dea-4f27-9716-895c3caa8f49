<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BasicFunctionalityTest extends TestCase
{
    /**
     * اختبار أن الصفحة الرئيسية تعمل
     */
    public function test_home_page_loads_successfully()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertSee('MyAdsSite');
    }

    /**
     * اختبار أن صفحة الإعلانات تعمل
     */
    public function test_ads_page_loads_successfully()
    {
        $response = $this->get('/ads');

        $response->assertStatus(200);
    }

    /**
     * اختبار أن صفحة الفئات تعمل
     */
    public function test_categories_page_loads_successfully()
    {
        $response = $this->get('/categories');

        $response->assertStatus(200);
    }

    /**
     * اختبار أن صفحة تسجيل الدخول تعمل
     */
    public function test_login_page_loads_successfully()
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        $response->assertSee('تسجيل الدخول');
    }

    /**
     * اختبار أن صفحة التسجيل تعمل (إذا كانت موجودة)
     */
    public function test_register_page_loads_successfully()
    {
        $response = $this->get('/register');

        // إذا كانت الصفحة موجودة، يجب أن تعمل
        if ($response->status() === 200) {
            $response->assertSee('إنشاء حساب');
        } else {
            // إذا لم تكن موجودة، نتوقع 404
            $response->assertStatus(404);
        }
    }

    /**
     * اختبار أن API يعمل
     */
    public function test_api_health_check()
    {
        $response = $this->get('/api/health');

        // إذا لم يكن موجود، سيعطي 404 وهذا مقبول
        $this->assertTrue(
            $response->status() === 200 || $response->status() === 404
        );
    }

    /**
     * اختبار أن ملفات CSS تحمل بشكل صحيح
     */
    public function test_css_files_accessible()
    {
        // اختبار أن ملف CSS الرئيسي يمكن الوصول إليه
        $response = $this->get('/');

        $response->assertStatus(200);
        // التحقق من وجود CSS في الصفحة
        $response->assertSee('css');
    }

    /**
     * اختبار أن ملفات JavaScript تحمل بشكل صحيح
     */
    public function test_javascript_files_accessible()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        // التحقق من وجود JavaScript في الصفحة
        $response->assertSee('script');
    }

    /**
     * اختبار أن الموقع يدعم اللغة العربية
     */
    public function test_arabic_language_support()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        // التحقق من وجود محتوى عربي
        $this->assertTrue(
            str_contains($response->getContent(), 'العربية') ||
            str_contains($response->getContent(), 'إعلانات') ||
            str_contains($response->getContent(), 'الرئيسية')
        );
    }

    /**
     * اختبار أن الموقع يدعم الوضع المظلم
     */
    public function test_dark_mode_support()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        // التحقق من وجود كود الوضع المظلم
        $response->assertSee('data-theme');
    }
}
