<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Model موحد للتفاعلات (مفضلة، تقييمات، تعليقات، مشاهدات)
 * يحل محل: Favorite, Rating, Comment, Review
 */
class Interaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'ad_id',
        'type',
        'data',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'data' => 'array',
    ];

    /**
     * أنواع التفاعلات المسموحة
     */
    const TYPES = [
        'favorite' => 'مفضلة',
        'rating' => 'تقييم',
        'comment' => 'تعليق',
        'view' => 'مشاهدة',
        'contact_reveal' => 'كشف معلومات التواصل',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع الإعلان
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * Scope للمفضلة
     */
    public function scopeFavorites($query)
    {
        return $query->where('type', 'favorite');
    }

    /**
     * Scope للتقييمات
     */
    public function scopeRatings($query)
    {
        return $query->where('type', 'rating');
    }

    /**
     * Scope للتعليقات
     */
    public function scopeComments($query)
    {
        return $query->where('type', 'comment');
    }

    /**
     * Scope للمشاهدات
     */
    public function scopeViews($query)
    {
        return $query->where('type', 'view');
    }

    /**
     * الحصول على قيمة التقييم
     */
    public function getRatingAttribute()
    {
        return $this->type === 'rating' ? ($this->data['rating'] ?? null) : null;
    }

    /**
     * الحصول على نص التعليق
     */
    public function getCommentAttribute()
    {
        return $this->type === 'comment' ? ($this->data['comment'] ?? null) : null;
    }

    /**
     * إضافة مفضلة
     */
    public static function addFavorite($userId, $adId)
    {
        return static::updateOrCreate([
            'user_id' => $userId,
            'ad_id' => $adId,
            'type' => 'favorite'
        ], [
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * إزالة مفضلة
     */
    public static function removeFavorite($userId, $adId)
    {
        return static::where([
            'user_id' => $userId,
            'ad_id' => $adId,
            'type' => 'favorite'
        ])->delete();
    }

    /**
     * إضافة تقييم
     */
    public static function addRating($userId, $adId, $rating, $comment = null)
    {
        return static::updateOrCreate([
            'user_id' => $userId,
            'ad_id' => $adId,
            'type' => 'rating'
        ], [
            'data' => [
                'rating' => $rating,
                'comment' => $comment,
            ],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * إضافة تعليق
     */
    public static function addComment($userId, $adId, $comment)
    {
        return static::create([
            'user_id' => $userId,
            'ad_id' => $adId,
            'type' => 'comment',
            'data' => [
                'comment' => $comment,
            ],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * تسجيل مشاهدة
     */
    public static function recordView($userId, $adId)
    {
        // تسجيل مشاهدة واحدة فقط لكل مستخدم لكل إعلان
        return static::firstOrCreate([
            'user_id' => $userId,
            'ad_id' => $adId,
            'type' => 'view'
        ], [
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * تسجيل كشف معلومات التواصل
     */
    public static function recordContactReveal($userId, $adId)
    {
        return static::create([
            'user_id' => $userId,
            'ad_id' => $adId,
            'type' => 'contact_reveal',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * الحصول على إحصائيات التفاعل لإعلان
     */
    public static function getAdStats($adId)
    {
        $stats = static::where('ad_id', $adId)
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        return [
            'favorites_count' => $stats['favorite'] ?? 0,
            'views_count' => $stats['view'] ?? 0,
            'ratings_count' => $stats['rating'] ?? 0,
            'comments_count' => $stats['comment'] ?? 0,
            'contact_reveals_count' => $stats['contact_reveal'] ?? 0,
        ];
    }

    /**
     * الحصول على متوسط التقييم لإعلان
     */
    public static function getAverageRating($adId)
    {
        $ratings = static::where('ad_id', $adId)
            ->where('type', 'rating')
            ->get()
            ->pluck('data.rating')
            ->filter();

        return $ratings->count() > 0 ? round($ratings->average(), 2) : 0;
    }
}
