<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserContact;
use App\Models\ContactAccessLog;
use App\Models\UserPrivacySetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Carbon\Carbon;

/**
 * خدمة الأمان والخصوصية لمعلومات التواصل
 * تتعامل مع جميع جوانب الأمان والخصوصية لمعلومات التواصل
 */
class ContactSecurityService
{
    /**
     * التحقق من إمكانية الوصول لمعلومات التواصل
     */
    public function canAccessContacts(User $contactOwner, ?User $viewer = null, string $ipAddress = null): array
    {
        $ipAddress = $ipAddress ?? request()->ip();
        
        // التحقق من إعدادات الخصوصية
        $privacySettings = $contactOwner->privacy_settings;
        
        if (!$privacySettings->canViewContacts($viewer)) {
            return [
                'allowed' => false,
                'reason' => 'إعدادات الخصوصية تمنع الوصول',
                'code' => 'PRIVACY_DENIED'
            ];
        }

        // التحقق من Rate Limiting
        if ($this->isRateLimited($contactOwner->id, $ipAddress, $viewer?->id)) {
            return [
                'allowed' => false,
                'reason' => 'تم تجاوز الحد المسموح للوصول',
                'code' => 'RATE_LIMITED'
            ];
        }

        // التحقق من النشاط المشبوه
        if ($this->isSuspiciousActivity($ipAddress, $viewer?->id)) {
            return [
                'allowed' => false,
                'reason' => 'نشاط مشبوه تم اكتشافه',
                'code' => 'SUSPICIOUS_ACTIVITY'
            ];
        }

        return [
            'allowed' => true,
            'reason' => 'الوصول مسموح',
            'code' => 'ACCESS_GRANTED'
        ];
    }

    /**
     * تسجيل الوصول لمعلومات التواصل
     */
    public function logContactAccess(
        int $contactOwnerId,
        string $accessType,
        ?int $contactId = null,
        ?int $accessorUserId = null,
        array $metadata = []
    ): ContactAccessLog {
        return ContactAccessLog::logAccess(
            $contactOwnerId,
            $accessType,
            $contactId,
            $accessorUserId,
            $metadata
        );
    }

    /**
     * التحقق من Rate Limiting
     */
    protected function isRateLimited(int $contactOwnerId, string $ipAddress, ?int $accessorUserId = null): bool
    {
        $privacySettings = UserPrivacySetting::where('user_id', $contactOwnerId)->first();
        
        if (!$privacySettings || !$privacySettings->enable_contact_rate_limiting) {
            return false;
        }

        // إذا كان المستخدم مسجل ومالك المعلومات، لا يوجد حد
        if ($accessorUserId && $accessorUserId === $contactOwnerId) {
            return false;
        }

        $cacheKeyHour = "contact_access_hour:{$contactOwnerId}:{$ipAddress}";
        $cacheKeyDay = "contact_access_day:{$contactOwnerId}:{$ipAddress}";

        $hourlyCount = Cache::get($cacheKeyHour, 0);
        $dailyCount = Cache::get($cacheKeyDay, 0);

        // فحص الحد الأقصى للساعة
        if ($hourlyCount >= $privacySettings->max_contact_views_per_hour) {
            Log::warning('Rate limit exceeded (hourly)', [
                'contact_owner_id' => $contactOwnerId,
                'ip_address' => $ipAddress,
                'count' => $hourlyCount,
                'limit' => $privacySettings->max_contact_views_per_hour
            ]);
            return true;
        }

        // فحص الحد الأقصى لليوم
        if ($dailyCount >= $privacySettings->max_contact_views_per_day) {
            Log::warning('Rate limit exceeded (daily)', [
                'contact_owner_id' => $contactOwnerId,
                'ip_address' => $ipAddress,
                'count' => $dailyCount,
                'limit' => $privacySettings->max_contact_views_per_day
            ]);
            return true;
        }

        // تحديث العدادات
        Cache::put($cacheKeyHour, $hourlyCount + 1, 3600); // ساعة واحدة
        Cache::put($cacheKeyDay, $dailyCount + 1, 86400); // يوم واحد

        return false;
    }

    /**
     * التحقق من النشاط المشبوه
     */
    protected function isSuspiciousActivity(string $ipAddress, ?int $userId = null): bool
    {
        // فحص عدد الطلبات السريعة من نفس IP
        $recentCount = ContactAccessLog::where('ip_address', $ipAddress)
                                     ->where('created_at', '>=', Carbon::now()->subMinutes(5))
                                     ->count();

        if ($recentCount > 20) {
            Log::warning('Suspicious activity detected: too many requests', [
                'ip_address' => $ipAddress,
                'user_id' => $userId,
                'count' => $recentCount
            ]);
            return true;
        }

        // فحص النشاط المشبوه المسجل مسبقاً
        $suspiciousCount = ContactAccessLog::where('ip_address', $ipAddress)
                                         ->where('is_suspicious', true)
                                         ->where('created_at', '>=', Carbon::now()->subHour())
                                         ->count();

        if ($suspiciousCount > 3) {
            Log::warning('Suspicious activity detected: multiple suspicious logs', [
                'ip_address' => $ipAddress,
                'user_id' => $userId,
                'suspicious_count' => $suspiciousCount
            ]);
            return true;
        }

        return false;
    }

    /**
     * تشفير معلومات التواصل
     */
    public function encryptContactValue(string $value, int $userId): string
    {
        $privacySettings = UserPrivacySetting::where('user_id', $userId)->first();
        
        if (!$privacySettings || !$privacySettings->enable_contact_encryption) {
            return $value;
        }

        try {
            return Crypt::encrypt($value);
        } catch (\Exception $e) {
            Log::error('Failed to encrypt contact value', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return $value;
        }
    }

    /**
     * فك تشفير معلومات التواصل
     */
    public function decryptContactValue(string $encryptedValue, int $userId): string
    {
        $privacySettings = UserPrivacySetting::where('user_id', $userId)->first();
        
        if (!$privacySettings || !$privacySettings->enable_contact_encryption) {
            return $encryptedValue;
        }

        try {
            return Crypt::decrypt($encryptedValue);
        } catch (\Exception $e) {
            Log::error('Failed to decrypt contact value', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return $encryptedValue;
        }
    }

    /**
     * إخفاء جزء من معلومات التواصل للأمان
     */
    public function maskContactValue(string $value, string $type): string
    {
        switch ($type) {
            case 'phone':
            case 'whatsapp':
                if (strlen($value) > 6) {
                    return substr($value, 0, 3) . '****' . substr($value, -2);
                }
                break;
                
            case 'email':
                $parts = explode('@', $value);
                if (count($parts) === 2) {
                    $username = $parts[0];
                    $domain = $parts[1];
                    
                    if (strlen($username) > 3) {
                        $maskedUsername = substr($username, 0, 2) . '***';
                    } else {
                        $maskedUsername = $username;
                    }
                    
                    return $maskedUsername . '@' . $domain;
                }
                break;
                
            default:
                if (strlen($value) > 6) {
                    return substr($value, 0, 3) . '***' . substr($value, -1);
                }
        }

        return $value;
    }

    /**
     * الحصول على إحصائيات الأمان للمستخدم
     */
    public function getSecurityStats(int $userId, int $days = 30): array
    {
        return ContactAccessLog::getAccessStatsForUser($userId, $days);
    }

    /**
     * الحصول على النشاطات المشبوهة الأخيرة للمستخدم
     */
    public function getSuspiciousActivities(int $userId, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return ContactAccessLog::where('contact_owner_id', $userId)
                              ->where('is_suspicious', true)
                              ->with(['contact', 'accessorUser'])
                              ->latest()
                              ->limit($limit)
                              ->get();
    }

    /**
     * حظر IP address مؤقتاً
     */
    public function blockIpTemporarily(string $ipAddress, int $minutes = 60, string $reason = 'نشاط مشبوه'): void
    {
        $cacheKey = "blocked_ip:{$ipAddress}";
        
        Cache::put($cacheKey, [
            'blocked_at' => Carbon::now(),
            'reason' => $reason,
            'expires_at' => Carbon::now()->addMinutes($minutes)
        ], $minutes * 60);

        Log::warning('IP address blocked temporarily', [
            'ip_address' => $ipAddress,
            'reason' => $reason,
            'duration_minutes' => $minutes
        ]);
    }

    /**
     * التحقق من كون IP محظور
     */
    public function isIpBlocked(string $ipAddress): bool
    {
        $cacheKey = "blocked_ip:{$ipAddress}";
        return Cache::has($cacheKey);
    }

    /**
     * إلغاء حظر IP address
     */
    public function unblockIp(string $ipAddress): void
    {
        $cacheKey = "blocked_ip:{$ipAddress}";
        Cache::forget($cacheKey);

        Log::info('IP address unblocked', [
            'ip_address' => $ipAddress
        ]);
    }

    /**
     * تنظيف البيانات القديمة
     */
    public function cleanupOldData(): array
    {
        $deletedLogs = ContactAccessLog::cleanOldLogs(90);
        
        // تنظيف cache القديم
        // يمكن إضافة منطق تنظيف إضافي هنا
        
        return [
            'deleted_logs' => $deletedLogs,
            'cleanup_date' => Carbon::now()
        ];
    }
}
