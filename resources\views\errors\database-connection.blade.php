@extends('layouts.app')

@section('title', 'خطأ في الاتصال بقاعدة البيانات')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 text-center">
        <div class="mb-4">
            <svg class="mx-auto h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900 mb-4">خطأ في الاتصال</h1>
        
        <p class="text-gray-600 mb-6">
            نعتذر، هناك مشكلة مؤقتة في الاتصال بقاعدة البيانات. 
            يرجى المحاولة مرة أخرى خلال بضع دقائق.
        </p>
        
        <div class="space-y-3">
            <button onclick="window.location.reload()" 
                    class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                إعادة المحاولة
            </button>
            
            <a href="{{ route('home') }}" 
               class="block w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                العودة للصفحة الرئيسية
            </a>
        </div>
        
        <div class="mt-6 text-sm text-gray-500">
            <p>إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني</p>
            <p class="mt-1">رقم الخطأ: DB_CONNECTION_ERROR</p>
        </div>
    </div>
</div>

<script>
// إعادة المحاولة التلقائية كل 30 ثانية
setTimeout(function() {
    window.location.reload();
}, 30000);
</script>
@endsection
