<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use App\Services\CacheService;
use App\Services\EncryptionService;

/**
 * نموذج الإعلانات
 * يحتوي على جميع الإعلانات المنشورة في الموقع
 */
class Ad extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',        // معرف المستخدم الذي أنشأ الإعلان
        'title_ar',       // العنوان بالعربية
        'title_en',       // العنوان بالإنجليزية
        'description_ar', // الوصف بالعربية
        'description_en', // الوصف بالإنجليزية
        'image',          // مسار الصورة
        'category_id',    // معرف التصنيف
        'status',         // حالة الإعلان
        'phone',          // رقم الهاتف
        'email',          // البريد الإلكتروني
        'location',       // الموقع أو المدينة
        'expires_at',     // تاريخ انتهاء الصلاحية
        'slug',           // الرابط المختصر
        'views_count',    // عدد المشاهدات
        'is_featured',    // هل الإعلان مميز
        'rejection_reason', // سبب الرفض
        // حقول الأسعار المتقدمة
        'price',          // السعر الحالي
        'original_price', // السعر الأصلي قبل الخصم
        'discount_percentage', // نسبة الخصم
        'discount_amount', // مبلغ الخصم
        'is_negotiable',  // هل السعر قابل للتفاوض
        'is_free',        // هل الإعلان مجاني
        'currency',       // نوع العملة
        'price_type',     // نوع السعر
        'discount_expires_at', // تاريخ انتهاء الخصم
        'is_limited_offer', // هل هو عرض محدود
        'price_notes',    // ملاحظات إضافية على السعر
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'expires_at' => 'date',           // تحويل إلى تاريخ
        'views_count' => 'integer',       // تحويل إلى رقم صحيح
        'is_featured' => 'boolean',       // تحويل إلى boolean
        // حقول الأسعار المتقدمة
        'price' => 'decimal:2',           // السعر الحالي
        'original_price' => 'decimal:2',  // السعر الأصلي
        'discount_percentage' => 'decimal:2', // نسبة الخصم
        'discount_amount' => 'decimal:2', // مبلغ الخصم
        'is_negotiable' => 'boolean',     // قابل للتفاوض
        'is_free' => 'boolean',           // مجاني
        'discount_expires_at' => 'date',  // تاريخ انتهاء الخصم
        'is_limited_offer' => 'boolean',  // عرض محدود
    ];

    /**
     * الأحداث التي تحدث عند إنشاء إعلان جديد
     */
    protected static function boot()
    {
        parent::boot();

        // إنشاء slug تلقائياً عند إنشاء إعلان جديد
        static::creating(function ($ad) {
            if (empty($ad->slug)) {
                $ad->slug = Str::slug($ad->title_en ?: $ad->title_ar) . '-' . time();
            }
        });

        // مسح الكاش عند إنشاء أو تحديث أو حذف إعلان
        static::created(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });

        static::updated(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });

        static::deleted(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });
    }

    /**
     * علاقة متعدد إلى واحد مع التصنيفات
     * إعلان واحد ينتمي إلى تصنيف واحد
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * علاقة متعدد إلى واحد مع المستخدمين
     * إعلان واحد ينتمي إلى مستخدم واحد
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * الحصول على عنوان الإعلان حسب اللغة الحالية
     */
    public function getTitleAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->title_ar : $this->title_en;
    }

    /**
     * الحصول على وصف الإعلان حسب اللغة الحالية
     */
    public function getDescriptionAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : $this->description_en;
    }

    /**
     * البحث في الإعلانات النشطة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * البحث في الإعلانات غير المنتهية الصلاحية
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * البحث في الإعلانات المعلقة (في انتظار الموافقة)
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * البحث في الإعلانات المميزة
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * البحث في إعلانات مستخدم معين
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * زيادة عدد المشاهدات
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * تشفير رقم الهاتف عند الحفظ
     */
    public function setPhoneAttribute($value)
    {
        $this->attributes['phone'] = EncryptionService::encryptPhone($value);
    }

    /**
     * فك تشفير رقم الهاتف عند الاستدعاء
     */
    public function getPhoneAttribute($value)
    {
        return EncryptionService::decryptPhone($value);
    }

    /**
     * تشفير البريد الإلكتروني عند الحفظ
     */
    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = EncryptionService::encryptEmail($value);
    }

    /**
     * فك تشفير البريد الإلكتروني عند الاستدعاء
     */
    public function getEmailAttribute($value)
    {
        return EncryptionService::decryptEmail($value);
    }

    /**
     * الحصول على رقم الهاتف مخفي للعرض
     */
    public function getMaskedPhoneAttribute()
    {
        return EncryptionService::maskPhone($this->phone);
    }

    /**
     * الحصول على البريد الإلكتروني مخفي للعرض
     */
    public function getMaskedEmailAttribute()
    {
        return EncryptionService::maskEmail($this->email);
    }

    /**
     * علاقة التقييمات (polymorphic)
     */
    public function reviews()
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    /**
     * التقييمات المعتمدة فقط
     */
    public function approvedReviews()
    {
        return $this->reviews()->approved();
    }

    /**
     * الحصول على متوسط التقييم (من نظام التقييمات الجديد)
     */
    public function getAverageRatingAttribute(): float
    {
        return Rating::getAverageRating($this->id);
    }

    /**
     * الحصول على السعر المنسق مع العملة (بدون نص قابل للتفاوض)
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->is_free) {
            return 'مجاني';
        }

        if ($this->price_type === 'on_request') {
            return 'السعر عند الطلب';
        }

        if (!$this->price) {
            return 'غير محدد';
        }

        $currencySymbols = [
            'YER' => 'ر.ي',
            'USD' => '$',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م'
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;
        $price = number_format($this->price, 0);

        return $price . ' ' . $symbol;
    }

    /**
     * الحصول على مسار الصورة للعرض
     */
    public function getImageUrlAttribute(): ?string
    {
        if (!$this->image) {
            return null;
        }

        // التحقق من وجود الصورة في storage
        if (\Storage::exists('public/' . $this->image)) {
            return asset('storage/' . $this->image);
        }

        // إذا لم توجد الصورة الأصلية، البحث عن الأحجام المختلفة
        $pathInfo = pathinfo($this->image);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];

        // البحث عن الأحجام المختلفة
        $sizes = ['medium', 'small', 'thumbnail', 'large'];
        foreach ($sizes as $size) {
            $sizePath = $directory . '/' . $size . '_' . $filename . '.' . $extension;
            if (\Storage::exists('public/' . $sizePath)) {
                return asset('storage/' . $sizePath);
            }
        }

        return null;
    }



    /**
     * الحصول على السعر الأصلي المنسق
     */
    public function getFormattedOriginalPriceAttribute(): string
    {
        if (!$this->original_price) {
            return '';
        }

        $currencySymbols = [
            'YER' => 'ر.ي',
            'USD' => '$',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م'
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;
        return number_format($this->original_price, 0) . ' ' . $symbol;
    }

    /**
     * الحصول على السعر المنسق مع رمز عملة صغير
     */
    public function getFormattedPriceWithSmallCurrency(): string
    {
        if ($this->is_free) {
            return 'مجاني';
        }

        if ($this->price_type === 'on_request') {
            return 'السعر عند الطلب';
        }

        if (!$this->price) {
            return 'غير محدد';
        }

        $currencySymbols = [
            'YER' => 'ر.ي',
            'USD' => '$',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م'
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;
        $price = number_format($this->price, 0);

        return $price . ' <span class="currency-small">' . $symbol . '</span>';
    }

    /**
     * الحصول على السعر الأصلي المنسق مع رمز عملة صغير
     */
    public function getFormattedOriginalPriceWithSmallCurrency(): string
    {
        if (!$this->original_price) {
            return '';
        }

        $currencySymbols = [
            'YER' => 'ر.ي',
            'USD' => '$',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م'
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;
        $price = number_format($this->original_price, 0);

        return $price . ' <span class="currency-small">' . $symbol . '</span>';
    }

    /**
     * التحقق من وجود خصم
     */
    public function hasDiscount(): bool
    {
        return $this->original_price && $this->price && $this->original_price > $this->price;
    }

    /**
     * حساب نسبة الخصم
     */
    public function getCalculatedDiscountPercentageAttribute(): float
    {
        if (!$this->hasDiscount()) {
            return 0;
        }

        return round((($this->original_price - $this->price) / $this->original_price) * 100, 0);
    }

    /**
     * حساب مبلغ التوفير
     */
    public function getSavingsAmountAttribute(): float
    {
        if (!$this->hasDiscount()) {
            return 0;
        }

        return $this->original_price - $this->price;
    }

    /**
     * التحقق من انتهاء صلاحية الخصم
     */
    public function isDiscountExpired(): bool
    {
        return $this->discount_expires_at && $this->discount_expires_at->isPast();
    }

    /**
     * الحصول على عدد التقييمات
     */
    public function getReviewsCountAttribute(): int
    {
        return Review::getReviewsCount($this);
    }

    /**
     * الحصول على توزيع التقييمات (من نظام التقييمات الجديد)
     */
    public function getRatingDistributionAttribute(): array
    {
        return Rating::getRatingDistribution($this->id);
    }

    /**
     * التحقق من وجود تقييم من مستخدم معين
     */
    public function hasUserReviewed(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Review::hasUserReviewed($userId, $this) : false;
    }

    /**
     * الحصول على تقييم المستخدم
     */
    public function getUserReview(?int $userId = null): ?Review
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Review::getUserReview($userId, $this) : null;
    }

    /**
     * علاقة واحد إلى متعدد مع المفضلة
     * إعلان واحد يمكن أن يكون في مفضلة عدة مستخدمين
     */
    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    /**
     * علاقة واحد إلى متعدد مع التقييمات
     * إعلان واحد يمكن أن يحصل على عدة تقييمات
     */
    public function ratings()
    {
        return $this->hasMany(Rating::class);
    }

    /**
     * علاقة واحد إلى متعدد مع التعليقات
     * إعلان واحد يمكن أن يحصل على عدة تعليقات
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * علاقة مع التعليقات المعتمدة فقط
     */
    public function approvedComments()
    {
        return $this->hasMany(Comment::class)->approved();
    }

    /**
     * علاقة متعدد إلى متعدد مع المستخدمين الذين أضافوا الإعلان للمفضلة
     */
    public function favoritedByUsers()
    {
        return $this->belongsToMany(User::class, 'favorites', 'ad_id', 'user_id')
                    ->withTimestamps()
                    ->orderBy('favorites.created_at', 'desc');
    }

    /**
     * التحقق من إضافة المستخدم للإعلان في المفضلة
     */
    public function isFavoritedBy(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Favorite::isFavorited($userId, $this->id) : false;
    }

    /**
     * الحصول على عدد المرات التي تم إضافة الإعلان للمفضلة
     */
    public function getFavoritesCountAttribute(): int
    {
        return Favorite::getAdFavoritesCount($this->id);
    }

    /**
     * إضافة الإعلان إلى مفضلة المستخدم الحالي
     */
    public function addToUserFavorites(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Favorite::addToFavorites($userId, $this->id) : false;
    }

    /**
     * إزالة الإعلان من مفضلة المستخدم الحالي
     */
    public function removeFromUserFavorites(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Favorite::removeFromFavorites($userId, $this->id) : false;
    }

    /**
     * تبديل حالة الإعلان في مفضلة المستخدم الحالي
     */
    public function toggleUserFavorite(?int $userId = null): array
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Favorite::toggleFavorite($userId, $this->id) : [
            'success' => false,
            'action' => 'error',
            'is_favorited' => false,
            'message' => 'يجب تسجيل الدخول أولاً'
        ];
    }

    /**
     * الحصول على عدد التقييمات
     */
    public function getRatingsCountAttribute(): int
    {
        return Rating::getRatingsCount($this->id);
    }

    /**
     * الحصول على إحصائيات التقييم الشاملة
     */
    public function getRatingStatsAttribute(): array
    {
        return Rating::getAdRatingStats($this->id);
    }

    /**
     * التحقق من تقييم المستخدم للإعلان
     */
    public function isRatedBy(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Rating::hasUserRated($userId, $this->id) : false;
    }

    /**
     * الحصول على تقييم المستخدم للإعلان
     */
    public function getUserRating(?int $userId = null): ?Rating
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Rating::getUserRating($userId, $this->id) : null;
    }

    /**
     * إضافة أو تحديث تقييم المستخدم
     */
    public function addOrUpdateUserRating(array $data, ?int $userId = null): ?Rating
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Rating::addOrUpdateRating($userId, $this->id, $data) : null;
    }

    /**
     * الحصول على عدد التعليقات
     */
    public function getCommentsCountAttribute(): int
    {
        return Comment::getCommentsCount($this->id);
    }

    /**
     * الحصول على التعليقات مع الردود
     */
    public function getCommentsWithReplies(int $perPage = 10)
    {
        return Comment::getCommentsWithReplies($this->id, $perPage);
    }

    /**
     * إضافة تعليق جديد
     */
    public function addComment(array $data, ?int $userId = null): ?Comment
    {
        $userId = $userId ?? auth()->id();
        if (!$userId) return null;

        $data['user_id'] = $userId;
        $data['ad_id'] = $this->id;

        return Comment::addComment($data);
    }
}
