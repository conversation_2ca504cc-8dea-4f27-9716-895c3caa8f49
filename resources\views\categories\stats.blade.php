@extends('layouts.app')

@section('title', __('Category Statistics') . ' - ' . $category->name)

@section('content')
<div class="container-fluid my-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 fw-bold text-primary arabic-text">
                        <i class="{{ $category->icon }} me-2"></i>
                        {{ __('Statistics for') }} {{ $category->name }}
                    </h1>
                    <p class="text-muted arabic-text">{{ $category->description }}</p>
                </div>
                <div>
                    <a href="{{ route('categories.index') }}" class="btn btn-outline-primary me-2 arabic-text">
                        <i class="fas fa-list me-1"></i>
                        {{ __('All Categories') }}
                    </a>
                    <a href="{{ route('ads.category', $category->slug) }}" class="btn btn-outline-secondary arabic-text">
                        <i class="fas fa-eye me-1"></i>
                        {{ __('View Ads') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ $stats['total_ads'] }}</h4>
                            <p class="mb-0 arabic-text">{{ __('Total Ads') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullhorn fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ $stats['active_ads'] }}</h4>
                            <p class="mb-0 arabic-text">{{ __('Active Ads') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ $stats['pending_ads'] }}</h4>
                            <p class="mb-0 arabic-text">{{ __('Pending Review') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ $stats['featured_ads'] }}</h4>
                            <p class="mb-0 arabic-text">{{ __('Featured Ads') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات زمنية -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>
                        {{ __('Today Ads') }}
                    </h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="text-info fw-bold">{{ $stats['ads_today'] }}</h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('New ads today') }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-week me-2"></i>
                        {{ __('Weekly Ads') }}
                    </h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="text-success fw-bold">{{ $stats['ads_this_week'] }}</h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Ads this week') }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        {{ __('Monthly Ads') }}
                    </h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="text-warning fw-bold">{{ $stats['ads_this_month'] }}</h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Ads this month') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أحدث الإعلانات والمستخدمين النشطين -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        {{ __('Latest Ads') }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($recentAds->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th class="arabic-text">{{ __('Title') }}</th>
                                        <th class="arabic-text">{{ __('User') }}</th>
                                        <th class="arabic-text">{{ __('Status') }}</th>
                                        <th class="arabic-text">{{ __('Created Date') }}</th>
                                        <th class="arabic-text">{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentAds as $ad)
                                    <tr>
                                        <td>
                                            <strong>{{ Str::limit($ad->title, 30) }}</strong>
                                        </td>
                                        <td>{{ $ad->user->name }}</td>
                                        <td>
                                            @if($ad->status === 'pending')
                                                <span class="badge bg-warning arabic-text">{{ __('Pending Review') }}</span>
                                            @elseif($ad->status === 'active')
                                                <span class="badge bg-success arabic-text">{{ __('Active') }}</span>
                                            @else
                                                <span class="badge bg-danger arabic-text">{{ __('Rejected') }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $ad->created_at->diffForHumans() }}</td>
                                        <td>
                                            <a href="{{ route('ads.show', [$category->slug, $ad->slug]) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted arabic-text">{{ __('No ads found in this category yet') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- المستخدمون النشطون -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        {{ __('Top Users') }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($userStats->count() > 0)
                        @foreach($userStats as $userStat)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ $userStat->user->name }}</strong>
                            </div>
                            <div>
                                <span class="badge bg-primary">{{ $userStat->ads_count }} {{ __('ads') }}</span>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-user-slash fa-2x text-muted mb-3"></i>
                            <p class="text-muted arabic-text">{{ __('No active users yet') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .btn {
        border-radius: 25px;
    }
    
    .display-6 {
        font-weight: bold;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
    }
</style>
@endpush
