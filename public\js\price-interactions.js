/**
 * تحسينات التفاعل للأسعار والعروض
 * Price Interactions Enhancement
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePriceInteractions();
});

/**
 * تهيئة تفاعلات الأسعار
 */
function initializePriceInteractions() {
    // تأثيرات الخصومات
    initializeDiscountEffects();
    
    // تأثيرات الأسعار
    initializePriceEffects();
    
    // تأثيرات الشارات
    initializeBadgeEffects();
    
    // تحسين الأداء
    optimizePerformance();
    
    // مراقبة التغييرات
    observeChanges();
}

/**
 * تأثيرات الخصومات
 */
function initializeDiscountEffects() {
    const discountTags = document.querySelectorAll('.discount-tag');
    
    discountTags.forEach(tag => {
        // تأثير النقر
        tag.addEventListener('click', function(e) {
            e.preventDefault();
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = 'pulse 2s infinite';
            }, 100);
        });
        
        // تأثير المرور
        tag.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
            this.style.transition = 'all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
        });
        
        tag.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });
}

/**
 * تأثيرات الأسعار
 */
function initializePriceEffects() {
    const priceElements = document.querySelectorAll('.price-current, .price-main');
    
    priceElements.forEach(price => {
        // تأثير التركيز على السعر
        price.addEventListener('mouseenter', function() {
            this.style.textShadow = '0 0 10px rgba(5, 150, 105, 0.5)';
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'all 0.3s ease';
        });
        
        price.addEventListener('mouseleave', function() {
            this.style.textShadow = '0 1px 2px rgba(5, 150, 105, 0.2)';
            this.style.transform = 'scale(1)';
        });
        
        // تأثير النقر لنسخ السعر
        price.addEventListener('click', function() {
            const priceText = this.textContent.trim();
            copyToClipboard(priceText);
            showPriceToast('تم نسخ السعر: ' + priceText);
        });
    });
}

/**
 * تأثيرات الشارات
 */
function initializeBadgeEffects() {
    const badges = document.querySelectorAll('.mini-badge');
    
    badges.forEach(badge => {
        // تأثير التموج
        badge.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // تأثير الاهتزاز للعروض المحدودة
        if (badge.classList.contains('limited-offer-mini')) {
            setInterval(() => {
                badge.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    badge.style.animation = 'blink 1.5s infinite';
                }, 500);
            }, 10000); // كل 10 ثواني
        }
    });
}

/**
 * تحسين الأداء
 */
function optimizePerformance() {
    // تأجيل تحميل التأثيرات غير الضرورية
    const priceContainers = document.querySelectorAll('.price-display-container');
    
    // استخدام Intersection Observer للتأثيرات
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('price-visible');
                animatePriceEntry(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });
    
    priceContainers.forEach(container => {
        observer.observe(container);
    });
}

/**
 * مراقبة التغييرات في DOM
 */
function observeChanges() {
    const observer = new MutationObserver((mutations) => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        const priceElements = node.querySelectorAll?.('.price-section-modern');
                        if (priceElements?.length > 0) {
                            initializePriceInteractions();
                        }
                    }
                });
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * تحريك دخول السعر
 */
function animatePriceEntry(container) {
    const elements = container.querySelectorAll('.price-current, .price-main, .discount-tag, .mini-badge');
    
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

/**
 * نسخ النص للحافظة
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).catch(err => {
            console.error('فشل في نسخ النص:', err);
        });
    } else {
        // fallback للمتصفحات القديمة
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
}

/**
 * إظهار رسالة للسعر
 */
function showPriceToast(message) {
    // إنشاء عنصر الرسالة
    const toast = document.createElement('div');
    toast.className = 'price-toast';
    toast.textContent = message;
    
    // تطبيق الأنماط
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        color: 'white',
        padding: '12px 20px',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        zIndex: '9999',
        fontSize: '14px',
        fontWeight: '600',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        direction: 'rtl'
    });
    
    document.body.appendChild(toast);
    
    // تحريك الدخول
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // إزالة الرسالة
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

/**
 * CSS للتأثيرات الإضافية
 */
const additionalStyles = `
<style>
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

.price-visible {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mini-badge {
    position: relative;
    overflow: hidden;
}

.price-toast {
    font-family: 'Cairo', sans-serif;
}
</style>
`;

// إضافة الأنماط للصفحة
document.head.insertAdjacentHTML('beforeend', additionalStyles);
