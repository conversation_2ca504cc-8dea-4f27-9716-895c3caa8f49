@extends('layouts.app')

@section('title', 'اختبار Modal')

@section('content')
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h3>اختبار Modal معلومات التواصل</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>تعليمات الاختبار:</strong>
                        <ol>
                            <li>انقر على الزر أدناه لفتح Modal</li>
                            <li>إذا لم يفتح، افتح Developer Tools (F12)</li>
                            <li>تحقق من وجود أخطاء في Console</li>
                        </ol>
                    </div>
                    
                    <div class="text-center">
                        <button type="button" class="btn btn-primary btn-lg" onclick="testModal()">
                            <i class="fas fa-plus me-2"></i>
                            اختبار فتح Modal
                        </button>
                        
                        <button type="button" class="btn btn-success btn-lg ms-2" data-bs-toggle="modal" data-bs-target="#testModal">
                            <i class="fas fa-cog me-2"></i>
                            اختبار Bootstrap Modal
                        </button>
                    </div>
                    
                    <hr>
                    
                    <div id="debug-info" class="mt-4">
                        <h5>معلومات التشخيص:</h5>
                        <div id="debug-output" class="bg-light p-3 border rounded">
                            <p>جاري التحميل...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal الاختبار -->
<div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModalLabel">Modal اختبار</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>✅ Modal يعمل بشكل صحيح!</p>
                <p>هذا يعني أن Bootstrap محمل بشكل صحيح.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal معلومات التواصل -->
<div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactModalTitle">إضافة طريقة تواصل جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="contactForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contact_type" class="form-label">نوع التواصل</label>
                            <select class="form-select" id="contact_type" name="contact_type">
                                <option value="">اختر نوع التواصل</option>
                                <option value="phone">هاتف</option>
                                <option value="whatsapp">واتساب</option>
                                <option value="email">إيميل</option>
                                <option value="telegram">تليجرام</option>
                                <option value="instagram">إنستجرام</option>
                                <option value="facebook">فيسبوك</option>
                                <option value="twitter">تويتر</option>
                                <option value="linkedin">لينكد إن</option>
                                <option value="website">موقع إلكتروني</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contact_value" class="form-label">قيمة التواصل *</label>
                            <input type="text" class="form-control" id="contact_value" name="contact_value" 
                                   placeholder="مثال: +967771234567" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="display_label" class="form-label">تسمية مخصصة</label>
                            <input type="text" class="form-control" id="display_label" name="display_label" 
                                   placeholder="مثال: هاتف العمل">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="privacy_level" class="form-label">مستوى الخصوصية</label>
                            <select class="form-select" id="privacy_level" name="privacy_level" required>
                                <option value="public">عام للجميع</option>
                                <option value="registered_users">للمستخدمين المسجلين فقط</option>
                                <option value="private">خاص (لا يظهر)</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_primary" name="is_primary">
                                <label class="form-check-label" for="is_primary">
                                    طريقة التواصل الأساسية من هذا النوع
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_public" name="is_public" checked>
                                <label class="form-check-label" for="is_public">
                                    إظهار في الإعلانات
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="testSaveContact()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const debugOutput = document.getElementById('debug-output');
    
    function log(message) {
        debugOutput.innerHTML += `<div>${message}</div>`;
    }
    
    // تشخيص البيئة
    log('🔍 بدء التشخيص...');
    log(`✅ jQuery متوفر: ${typeof $ !== 'undefined' ? 'نعم' : 'لا'}`);
    log(`✅ Bootstrap متوفر: ${typeof bootstrap !== 'undefined' ? 'نعم' : 'لا'}`);
    log(`✅ Modal class متوفر: ${typeof bootstrap !== 'undefined' && bootstrap.Modal ? 'نعم' : 'لا'}`);
    
    // تحقق من وجود العناصر
    const contactModal = document.getElementById('contactModal');
    const contactForm = document.getElementById('contactForm');
    const contactModalTitle = document.getElementById('contactModalTitle');
    
    log(`✅ contactModal موجود: ${contactModal ? 'نعم' : 'لا'}`);
    log(`✅ contactForm موجود: ${contactForm ? 'نعم' : 'لا'}`);
    log(`✅ contactModalTitle موجود: ${contactModalTitle ? 'نعم' : 'لا'}`);
    
    // تحقق من CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    log(`✅ CSRF Token موجود: ${csrfToken ? 'نعم' : 'لا'}`);
    
    log('🎉 انتهى التشخيص');
    
    // تعريف الوظائف في النطاق العام
    window.testModal = function() {
        log('🔄 محاولة فتح Modal...');
        
        try {
            const modal = new bootstrap.Modal(document.getElementById('contactModal'));
            modal.show();
            log('✅ تم فتح Modal بنجاح');
        } catch (error) {
            log(`❌ خطأ في فتح Modal: ${error.message}`);
            console.error('Modal error:', error);
        }
    };
    
    window.testSaveContact = function() {
        log('🔄 اختبار حفظ معلومة التواصل...');
        
        const form = document.getElementById('contactForm');
        const formData = new FormData(form);
        
        const data = {};
        formData.forEach((value, key) => {
            if (key === 'is_primary' || key === 'is_public') {
                data[key] = form.querySelector(`[name="${key}"]`).checked;
            } else {
                data[key] = value;
            }
        });
        
        log('📤 البيانات المرسلة:');
        log(JSON.stringify(data, null, 2));
        
        // محاولة إرسال البيانات
        fetch('/dashboard/contacts', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            log(`📡 حالة الاستجابة: ${response.status}`);
            return response.json();
        })
        .then(data => {
            log('📦 استجابة الخادم:');
            log(JSON.stringify(data, null, 2));
            
            if (data.success) {
                log('✅ تم حفظ معلومة التواصل بنجاح');
                const modal = bootstrap.Modal.getInstance(document.getElementById('contactModal'));
                if (modal) {
                    modal.hide();
                }
            } else {
                log(`❌ فشل الحفظ: ${data.message}`);
            }
        })
        .catch(error => {
            log(`❌ خطأ في الشبكة: ${error.message}`);
            console.error('Save error:', error);
        });
    };
});
</script>
@endsection
