<?php

namespace App\Services;

use App\Models\Review;
use App\Models\Ad;
use App\Models\User;
use App\Models\SecurityLog;
use App\Models\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * خدمة إدارة التقييمات والمراجعات
 * تدير جميع عمليات التقييمات مع الأمان والتحقق
 */
class ReviewService
{
    /**
     * إنشاء تقييم جديد
     */
    public static function createReview(
        int $userId,
        $reviewable,
        int $rating,
        ?string $title = null,
        ?string $comment = null
    ): array {
        try {
            // التحقق من صحة التقييم
            if ($rating < 1 || $rating > 5) {
                return [
                    'success' => false,
                    'message' => 'التقييم يجب أن يكون بين 1 و 5 نجوم'
                ];
            }

            // التحقق من عدم وجود تقييم سابق
            if (Review::hasUserReviewed($userId, $reviewable)) {
                return [
                    'success' => false,
                    'message' => 'لقد قمت بتقييم هذا العنصر مسبقاً'
                ];
            }

            // التحقق من أن المستخدم لا يقيم نفسه أو إعلانه
            if (self::isOwnContent($userId, $reviewable)) {
                return [
                    'success' => false,
                    'message' => 'لا يمكنك تقييم المحتوى الخاص بك'
                ];
            }

            // تنظيف البيانات
            $cleanTitle = $title ? strip_tags(trim($title)) : null;
            $cleanComment = $comment ? strip_tags(trim($comment), '<p><br>') : null;

            // فحص المحتوى المشبوه
            if (self::containsSuspiciousContent($cleanTitle . ' ' . $cleanComment)) {
                SecurityLog::logSuspiciousActivity(
                    "محاولة إنشاء تقييم يحتوي على محتوى مشبوه",
                    [
                        'user_id' => $userId,
                        'reviewable_type' => get_class($reviewable),
                        'reviewable_id' => $reviewable->id,
                        'title' => $cleanTitle,
                        'comment' => $cleanComment
                    ]
                );

                return [
                    'success' => false,
                    'message' => 'التقييم يحتوي على محتوى غير مناسب'
                ];
            }

            // إنشاء التقييم
            $review = Review::createReview(
                $userId,
                $reviewable,
                $rating,
                $cleanTitle,
                $cleanComment
            );

            // تسجيل الحدث
            SecurityLog::logEvent(
                'review_created',
                "تم إنشاء تقييم جديد",
                SecurityLog::SEVERITY_LOW,
                [
                    'review_id' => $review->id,
                    'reviewable_type' => get_class($reviewable),
                    'reviewable_id' => $reviewable->id,
                    'rating' => $rating
                ],
                $userId
            );

            // مسح الكاش المتعلق
            self::clearReviewCache($reviewable);

            return [
                'success' => true,
                'message' => 'تم إرسال التقييم بنجاح وهو في انتظار المراجعة',
                'review' => $review
            ];

        } catch (\Exception $e) {
            Log::error('فشل في إنشاء التقييم', [
                'user_id' => $userId,
                'reviewable_type' => get_class($reviewable),
                'reviewable_id' => $reviewable->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في إرسال التقييم'
            ];
        }
    }

    /**
     * تحديث تقييم موجود
     */
    public static function updateReview(
        Review $review,
        int $rating,
        ?string $title = null,
        ?string $comment = null
    ): array {
        try {
            // التحقق من الصلاحيات
            if ($review->user_id !== auth()->id()) {
                return [
                    'success' => false,
                    'message' => 'ليس لديك صلاحية لتعديل هذا التقييم'
                ];
            }

            // التحقق من صحة التقييم
            if ($rating < 1 || $rating > 5) {
                return [
                    'success' => false,
                    'message' => 'التقييم يجب أن يكون بين 1 و 5 نجوم'
                ];
            }

            $oldData = $review->toArray();

            // تنظيف البيانات
            $cleanTitle = $title ? strip_tags(trim($title)) : null;
            $cleanComment = $comment ? strip_tags(trim($comment), '<p><br>') : null;

            // فحص المحتوى المشبوه
            if (self::containsSuspiciousContent($cleanTitle . ' ' . $cleanComment)) {
                return [
                    'success' => false,
                    'message' => 'التقييم يحتوي على محتوى غير مناسب'
                ];
            }

            // تحديث التقييم
            $review->update([
                'rating' => $rating,
                'title' => $cleanTitle,
                'comment' => $cleanComment,
                'is_approved' => false, // يحتاج موافقة جديدة
            ]);

            // تسجيل التغيير
            SecurityLog::logEvent(
                'review_updated',
                "تم تحديث التقييم",
                SecurityLog::SEVERITY_LOW,
                [
                    'review_id' => $review->id,
                    'old_data' => $oldData,
                    'new_rating' => $rating
                ],
                $review->user_id
            );

            // مسح الكاش
            self::clearReviewCache($review->reviewable);

            return [
                'success' => true,
                'message' => 'تم تحديث التقييم بنجاح',
                'review' => $review->fresh()
            ];

        } catch (\Exception $e) {
            Log::error('فشل في تحديث التقييم', [
                'review_id' => $review->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في تحديث التقييم'
            ];
        }
    }

    /**
     * حذف تقييم
     */
    public static function deleteReview(Review $review): array
    {
        try {
            // التحقق من الصلاحيات
            if ($review->user_id !== auth()->id() && !auth()->user()->is_admin) {
                return [
                    'success' => false,
                    'message' => 'ليس لديك صلاحية لحذف هذا التقييم'
                ];
            }

            $reviewData = $review->toArray();
            $reviewable = $review->reviewable;

            // حذف التقييم
            $review->delete();

            // تسجيل الحدث
            SecurityLog::logEvent(
                'review_deleted',
                "تم حذف التقييم",
                SecurityLog::SEVERITY_MEDIUM,
                [
                    'review_id' => $reviewData['id'],
                    'reviewable_type' => $reviewData['reviewable_type'],
                    'reviewable_id' => $reviewData['reviewable_id']
                ],
                auth()->id()
            );

            // مسح الكاش
            if ($reviewable) {
                self::clearReviewCache($reviewable);
            }

            return [
                'success' => true,
                'message' => 'تم حذف التقييم بنجاح'
            ];

        } catch (\Exception $e) {
            Log::error('فشل في حذف التقييم', [
                'review_id' => $review->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في حذف التقييم'
            ];
        }
    }

    /**
     * اعتماد تقييم (للإدارة)
     */
    public static function approveReview(Review $review): array
    {
        try {
            $review->approve();

            // إرسال إشعار للمستخدم
            NotificationService::sendToUser(
                $review->user_id,
                'تم قبول تقييمك',
                "تم قبول تقييمك ونشره بنجاح",
                Notification::TYPE_SUCCESS
            );

            // مسح الكاش
            self::clearReviewCache($review->reviewable);

            return [
                'success' => true,
                'message' => 'تم اعتماد التقييم بنجاح'
            ];

        } catch (\Exception $e) {
            Log::error('فشل في اعتماد التقييم', [
                'review_id' => $review->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في اعتماد التقييم'
            ];
        }
    }

    /**
     * رفض تقييم (للإدارة)
     */
    public static function rejectReview(Review $review, ?string $reason = null): array
    {
        try {
            $review->reject();

            // إرسال إشعار للمستخدم
            $message = "تم رفض تقييمك";
            if ($reason) {
                $message .= "\nالسبب: {$reason}";
            }

            NotificationService::sendToUser(
                $review->user_id,
                'تم رفض تقييمك',
                $message,
                Notification::TYPE_WARNING
            );

            return [
                'success' => true,
                'message' => 'تم رفض التقييم'
            ];

        } catch (\Exception $e) {
            Log::error('فشل في رفض التقييم', [
                'review_id' => $review->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في رفض التقييم'
            ];
        }
    }

    /**
     * وضع علامة مفيد على التقييم
     */
    public static function markAsHelpful(Review $review, int $userId): array
    {
        try {
            // التحقق من عدم تقييم المستخدم لنفسه
            if ($review->user_id === $userId) {
                return [
                    'success' => false,
                    'message' => 'لا يمكنك تقييم تقييمك الخاص'
                ];
            }

            $review->incrementHelpful();

            return [
                'success' => true,
                'message' => 'شكراً لك على تقييمك',
                'helpful_count' => $review->helpful_count
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ'
            ];
        }
    }

    /**
     * التحقق من أن المحتوى ملك المستخدم
     */
    private static function isOwnContent(int $userId, $reviewable): bool
    {
        if ($reviewable instanceof Ad) {
            return $reviewable->user_id === $userId;
        }

        if ($reviewable instanceof User) {
            return $reviewable->id === $userId;
        }

        return false;
    }

    /**
     * فحص المحتوى المشبوه
     */
    private static function containsSuspiciousContent(?string $content): bool
    {
        if (empty($content)) {
            return false;
        }

        $suspiciousPatterns = [
            '/\b(spam|scam|fake|fraud)\b/i',
            '/\b(click here|visit now|buy now)\b/i',
            '/\b(free money|easy money|get rich)\b/i',
            '/\b(viagra|casino|poker|gambling)\b/i',
            '/\b(hack|crack|pirate|illegal)\b/i',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    /**
     * مسح كاش التقييمات
     */
    private static function clearReviewCache($reviewable): void
    {
        $cacheKeys = [
            'reviews_' . get_class($reviewable) . '_' . $reviewable->id,
            'rating_' . get_class($reviewable) . '_' . $reviewable->id,
            'reviews_count_' . get_class($reviewable) . '_' . $reviewable->id,
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * الحصول على إحصائيات التقييمات
     */
    public static function getReviewStats(): array
    {
        return Cache::remember('review_stats', 3600, function () {
            return Review::getStats();
        });
    }
}
