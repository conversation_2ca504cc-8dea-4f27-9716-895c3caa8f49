/*
 * ملف CSS موحد لبطاقات الإعلانات
 * يجمع جميع الأنماط المتعلقة بعرض البطاقات
 * تم إنشاؤه كجزء من خطة التوحيد والتحسين
 */

/* ===== المتغيرات العامة ===== */
:root {
    --ad-card-primary: #007bff;
    --ad-card-success: #10b981;
    --ad-card-warning: #f59e0b;
    --ad-card-danger: #ef4444;
    --ad-card-border-radius: 16px;
    --ad-card-border-radius-sm: 8px;
    --ad-card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --ad-card-shadow-hover: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --ad-card-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== البطاقة الأساسية ===== */
.ad-card {
    border-radius: var(--ad-card-border-radius) !important;
    box-shadow: var(--ad-card-shadow);
    transition: var(--ad-card-transition);
    overflow: hidden;
    position: relative;
    background: #ffffff !important;
    border: none !important;
    color: #1e293b !important;
    display: flex;
    flex-direction: column;
    height: 100%;
    /* إنشاء stacking context منفصل للبطاقة لتجنب تعارض z-index */
    isolation: isolate;
    z-index: 1;
}

.ad-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--ad-card-shadow-hover);
}

.ad-card .card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
    opacity: 0;
    transition: var(--ad-card-transition);
    pointer-events: none;
    z-index: 1;
}

.ad-card:hover .card-overlay {
    opacity: 1;
}

/* ===== أنواع البطاقات المختلفة ===== */
.ad-card-compact {
    min-height: 480px;
}

.ad-card-compact .card-body {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 0.75rem;
}

.ad-card-detailed {
    min-height: 600px;
}

.ad-card-detailed .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.ad-card-list {
    min-height: auto;
    display: flex;
    flex-direction: row;
}

.ad-card-list .ad-image {
    width: 200px;
    flex-shrink: 0;
}

.ad-card-list .card-body {
    flex: 1;
}

/* ===== قسم الصورة ===== */
.card-header-custom {
    position: relative;
    padding: 0;
    background: none;
    border: none;
}

.ad-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--ad-card-border-radius) var(--ad-card-border-radius) 0 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--ad-card-transition);
}

.ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.placeholder-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #64748b;
}

/* ===== أيقونة المفضلة ===== */
/*
 * حاوي أيقونة المفضلة (القلب) في الزاوية اليمنى العلوية للبطاقة
 * تظهر فوق جميع العناصر الأخرى في البطاقة بما في ذلك الشارات
 * تسمح للمستخدم بإضافة/إزالة الإعلان من المفضلة
 */
.favorite-icon {
    position: absolute;        /* موضع مطلق داخل البطاقة */
    top: 12px;                /* مسافة 12 بكسل من الأعلى */
    right: 12px;              /* مسافة 12 بكسل من اليمين */
    /* z-index محسن ضمن stacking context البطاقة */
    z-index: 20;              /* أعلى من جميع الشارات (15) لضمان عدم التداخل */
}

/* تم نقل تعريفات .btn-favorite إلى القسم المحسن أدناه */

/* ===== الشارات ===== */
.category-badge {
    display: inline-flex !important;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, var(--ad-card-primary) 0%, #0056b3 100%);
    color: white;
    border-radius: var(--ad-card-border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: var(--ad-card-transition);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
    white-space: nowrap !important;
    max-width: fit-content !important;
    width: auto !important;
    flex-shrink: 0 !important;
}

.category-badge:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.category-badge-container {
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
    display: inline-block !important;
    width: auto !important;
    max-width: fit-content !important;
}

.category-badge-link {
    text-decoration: none;
    display: inline-block !important;
    width: auto !important;
    max-width: fit-content !important;
}

/*
 * شارة "جديد" - تظهر في الزاوية اليسرى العلوية للإعلانات الجديدة
 * تستخدم اللون الأخضر للدلالة على الحداثة والجودة
 * تظهر للإعلانات المنشورة خلال آخر 7 أيام
 */
.new-badge {
    position: absolute;        /* موضع مطلق داخل البطاقة */
    top: 10px;                /* مسافة 10 بكسل من الأعلى */
    left: 10px;               /* مسافة 10 بكسل من اليسار */
    background: linear-gradient(135deg, var(--ad-card-success) 0%, #059669 100%); /* خلفية متدرجة خضراء */
    z-index: 15;              /* أولوية عرض عالية (أقل من المفضلة 20) */
    font-size: 0.7rem;        /* حجم خط صغير */
    padding: 0.25rem 0.5rem;  /* حشو داخلي مناسب */
    border-radius: 12px;      /* زوايا دائرية */
    font-weight: 600;         /* خط عريض */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* ظل خفيف */
    color: white;             /* نص أبيض */
    display: inline-flex;     /* عرض مرن */
    align-items: center;      /* محاذاة وسط */
    white-space: nowrap;      /* منع كسر السطر */
    max-width: fit-content;   /* عرض مناسب للمحتوى */
    
}

/*
 * شارة "مميز" - تظهر أسفل شارة "جديد" للإعلانات المميزة
 * تستخدم اللون البرتقالي للدلالة على الأهمية والتميز
 * تظهر للإعلانات التي دفع أصحابها رسوم إضافية لتمييزها
 */
.featured-badge {
    position: absolute;        /* موضع مطلق داخل البطاقة */
    top: 10px;                /* مسافة 10 بكسل من الأعلى */
    left: 10px;               /* مسافة 10 بكسل من اليسار */
    background: linear-gradient(135deg, var(--ad-card-warning) 0%, #d97706 100%); /* خلفية متدرجة برتقالية */
    z-index: 14;              /* أولوية أقل من "جديد" (15) */
    transform: translateY(40px); /* إزاحة 35 بكسل للأسفل لتجنب التداخل مع "جديد" */
    font-size: 0.7rem;        /* حجم خط صغير */
    padding: 0.25rem 0.5rem;  /* حشو داخلي مناسب */
    border-radius: 12px;      /* زوايا دائرية */
    font-weight: 600;         /* خط عريض */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* ظل خفيف */
    color: white;             /* نص أبيض */
    display: inline-flex;     /* عرض مرن */
    align-items: center;      /* محاذاة وسط */
    white-space: nowrap;      /* منع كسر السطر */
    max-width: fit-content;   /* عرض مناسب للمحتوى */
}

/*
 * شارة "عاجل" - تظهر أسفل شارة "مميز" للإعلانات العاجلة
 * تستخدم اللون الأحمر مع تأثير النبضة للدلالة على الاستعجال
 * تظهر للإعلانات التي تحتاج بيع سريع أو لها مهلة زمنية محددة
 */
.urgent-badge {
    position: absolute;        /* موضع مطلق داخل البطاقة */
    top: 10px;                /* مسافة 10 بكسل من الأعلى */
    left: 10px;               /* مسافة 10 بكسل من اليسار */
    background: linear-gradient(135deg, var(--ad-card-danger) 0%, #dc2626 100%); /* خلفية متدرجة حمراء */
    z-index: 13;              /* أولوية أقل من "مميز" (14) */
    transform: translateY(70px); /* إزاحة 70 بكسل للأسفل لتجنب التداخل مع الشارات الأخرى */
    animation: pulse 2s infinite; /* تأثير نبضة مستمر لجذب الانتباه */
    font-size: 0.7rem;        /* حجم خط صغير */
    padding: 0.25rem 0.5rem;  /* حشو داخلي مناسب */
    border-radius: 12px;      /* زوايا دائرية */
    font-weight: 600;         /* خط عريض */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* ظل خفيف */
    color: white;             /* نص أبيض */
    display: inline-flex;     /* عرض مرن */
    align-items: center;      /* محاذاة وسط */
    white-space: nowrap;      /* منع كسر السطر */
    max-width: fit-content;   /* عرض مناسب للمحتوى */
}

/*
 * تأثير النبضة للشارة العاجلة
 * يقوم بتغيير الشفافية بشكل دوري لجذب انتباه المستخدم
 * يعمل لمدة ثانيتين ويتكرر إلى ما لا نهاية
 */
@keyframes pulse {
    0%, 100% { opacity: 1; }    /* شفافية كاملة في البداية والنهاية */
    50% { opacity: 0.7; }       /* شفافية أقل في المنتصف لإنشاء تأثير النبضة */
}

/* ===== العنوان والوصف ===== */
.ad-title {
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 0.75rem;
    color: #1e293b;
    font-size: 1.1rem;
}

.ad-title a {
    color: inherit;
    transition: var(--ad-card-transition);
    text-decoration: none;
}

.ad-title a:hover {
    color: var(--ad-card-primary);
    text-decoration: none;
}

.ad-description {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #64748b;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ===== المعلومات الإضافية ===== */
.ad-meta {
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.4rem;
    font-size: 0.85rem;
    color: #64748b;
}

.meta-item:last-child {
    margin-bottom: 0;
}

.meta-item i {
    margin-left: 0.4rem;
    color: var(--ad-card-primary);
    font-size: 0.9rem;
}

.meta-simple {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

/* ===== معلومات التواصل ===== */
.contact-info {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: var(--ad-card-border-radius-sm);
    border-left: 4px solid var(--ad-card-primary);
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.contact-item:last-child {
    margin-bottom: 0;
}

/* ===== قسم الأسعار ===== */
.pricing-section {
    margin-top: auto;
    margin-bottom: 0;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: var(--ad-card-border-radius-sm);
    border: 1px solid #e2e8f0;
}

.price-display {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--ad-card-success);
    margin-bottom: 0.25rem;
}

.price-label {
    font-size: 0.8rem;
    color: #64748b;
    margin-bottom: 0;
}

/* ===== تذييل البطاقة ===== */
.card-footer {
    background: transparent !important;
    border-top: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
}

.action-buttons {
    gap: 0.4rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.action-buttons .btn {
    border-radius: var(--ad-card-border-radius-sm);
    font-weight: 500;
    transition: var(--ad-card-transition);
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
    flex: 1;
    min-width: 0;
}

.action-buttons .btn:not(:last-child) {
    margin-left: 0.25rem;
}

/* ===== أنماط خاصة بالصفحات المختلفة ===== */

/* بطاقات الصفحة الرئيسية */
.featured-ad-card,
.popular-ad-card {
    transition: all 0.3s ease;
}

.featured-ad-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 123, 255, 0.15);
}

.popular-ad-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(255, 193, 7, 0.15);
}

/* ===== أنماط عداد المفضلة المحسنة ===== */

/* عداد المفضلة بسيط وواضح */
.navbar-nav .nav-link .badge.favorites-count {
    font-size: 12px;
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
    font-weight: bold;
    z-index: 10;
}

/* بدون تأثيرات معقدة - بساطة تامة */

/* عداد المفضلة في القائمة المنسدلة - تصميم محسن */
.dropdown-menu .badge.favorites-count {
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff6b6b 0%, #dc3545 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
    font-weight: 600;
    transition: all 0.2s ease;
}

.dropdown-item:hover .badge.favorites-count {
    transform: scale(1.05);
    box-shadow: 0 3px 8px rgba(220, 53, 69, 0.4);
}

/* عداد المفضلة العام - تصميم محسن */
.favorites-count {
    background: linear-gradient(135deg, #ff6b6b 0%, #dc3545 100%);
    color: white;
    border-radius: 50%;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 700;
    min-width: 20px;
    text-align: center;
    display: inline-block;
    line-height: 1;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
    transition: all 0.3s ease;
    position: relative;
}

/* تأثيرات النبض المحسنة */
@keyframes heartbeat {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
    }
    25% {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.5);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 3px 9px rgba(220, 53, 69, 0.4);
    }
    75% {
        transform: scale(1.15);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow:
            0 2px 8px rgba(220, 53, 69, 0.4),
            0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    50% {
        box-shadow:
            0 4px 16px rgba(220, 53, 69, 0.6),
            0 0 20px rgba(220, 53, 69, 0.4);
    }
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow:
            0 8px 25px rgba(255, 65, 108, 0.6),
            0 4px 15px rgba(255, 75, 43, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.7);
    }
    50% {
        box-shadow:
            0 12px 35px rgba(255, 65, 108, 0.8),
            0 6px 20px rgba(255, 75, 43, 0.6),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }
}

/* تطبيق التأثيرات عند التحديث */
.favorites-count.updating {
    animation: heartbeat 0.6s ease-in-out;
}

.navbar-nav .nav-link .badge.favorites-count.updating {
    animation: glow 0.8s ease-in-out;
}

/* ===== تحسينات خاصة لرابط المفضلة في شريط التنقل ===== */

.favorites-nav-link {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    padding: 12px 16px !important;
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.favorites-nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(220, 53, 69, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 12px;
}

.favorites-nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow:
        0 6px 20px rgba(255, 107, 107, 0.3),
        0 0 30px rgba(255, 65, 108, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.favorites-nav-link:hover::before {
    opacity: 1;
}

.favorites-nav-link .fa-heart {
    font-size: 1.3em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    color: rgba(255, 255, 255, 0.9);
}

.favorites-nav-link:hover .fa-heart {
    color: #ff6b9d !important;
    transform: scale(1.2);
    text-shadow: 0 0 20px rgba(255, 107, 157, 0.8);
    filter: drop-shadow(0 4px 8px rgba(255, 107, 157, 0.4));
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* تأثير خاص عند وجود مفضلة - مع fallback للمتصفحات القديمة */
.favorites-nav-link .fa-heart {
    transition: all 0.3s ease;
}

/* Fallback للمتصفحات التي لا تدعم :has() */
.favorites-nav-link.has-favorites .fa-heart {
    color: #ff4757 !important;
    animation: heartbeat 2.5s ease-in-out infinite;
    text-shadow: 0 0 10px rgba(255, 71, 87, 0.4);
}

/* للمتصفحات الحديثة التي تدعم :has() */
@supports selector(:has(*)) {
    .favorites-nav-link:has(.favorites-count[style*="inline"]) .fa-heart {
        color: #ff4757 !important;
        animation: heartbeat 2.5s ease-in-out infinite;
        text-shadow: 0 0 10px rgba(255, 71, 87, 0.4);
    }
}

/* تحسين شكل العداد في شريط التنقل */
.favorites-nav-link .favorites-count {
    font-size: 0.75rem !important;
    min-width: 24px !important;
    height: 24px !important;
    line-height: 1 !important;
    font-weight: 900 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    border-radius: 50% !important;
    /* ضمان الظهور الكامل */
    visibility: visible !important;
    opacity: 1 !important;
    display: flex !important;
}

/* بساطة في التصميم */

/* إصلاح شامل لمشكلة القطع */
.navbar-nav .nav-item,
.navbar-nav .nav-link,
.navbar-collapse,
.navbar,
.container-fluid {
    overflow: visible !important;
}

/* تأكيد موضع العداد */
.favorites-count-badge,
.navbar-nav .nav-link .favorites-count {
    position: absolute !important;
    top: -12px !important;
    right: -12px !important;
    z-index: 9999 !important;
    overflow: visible !important;
    clip: unset !important;
    clip-path: none !important;
}

/* للأجهزة المحمولة */
@media (max-width: 768px) {
    .navbar-nav .nav-item {
        margin-right: 20px !important;
    }

    .favorites-count-badge,
    .navbar-nav .nav-link .favorites-count {
        top: -10px !important;
        right: -10px !important;
        min-width: 22px !important;
        height: 22px !important;
        font-size: 0.7rem !important;
    }
}

/* إخفاء العداد عندما يكون صفر */
.favorites-count[style*="display: none"] {
    display: none !important;
}

/* إظهار العداد عندما يكون أكبر من صفر */
.favorites-count[style*="display: inline"] {
    display: inline !important;
}

/* ===== أنماط أزرار المفضلة المحسنة في البطاقات ===== */

/* زر المفضلة - تصميم محسن ودائري أكثر */
.btn-favorite {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #6c757d;
    color: #6c757d;
    border-radius: 50%;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 3px 12px rgba(108, 117, 125, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    font-size: 16px;
    backdrop-filter: blur(10px);
    /* جعل الزر أكثر استدارة */
    aspect-ratio: 1;
    min-width: 42px;
    min-height: 42px;
}

/* تأثير الوهج الداخلي للزر */
.btn-favorite::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.1), transparent);
    transition: left 0.6s ease;
}

/* تأثير النبضة للقلب */
.btn-favorite::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(220, 53, 69, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

/* تأثيرات hover المحسنة - حالة غير مفعلة */
.btn-favorite:hover {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    transform: translateY(-2px) scale(1.08);
    box-shadow:
        0 8px 25px rgba(220, 53, 69, 0.35),
        0 0 20px rgba(220, 53, 69, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-color: #dc3545;
    /* تأثير إضافي للاستدارة */
    border-radius: 50%;
}

.btn-favorite:hover::before {
    left: 100%;
}

.btn-favorite:hover::after {
    width: 100%;
    height: 100%;
    opacity: 0;
}

/* الحالة المفعلة - قلب ممتلئ (تصميم محسن) */
.btn-favorite.favorited {
    background: linear-gradient(135deg, #ff6b6b 0%, #dc3545 50%, #c82333 100%);
    color: white;
    border-color: #dc3545;
    box-shadow:
        0 5px 18px rgba(220, 53, 69, 0.4),
        0 0 25px rgba(220, 53, 69, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    animation: favoriteAdded 0.6s ease-out;
    /* تأكيد الاستدارة في الحالة المفعلة */
    border-radius: 50%;
    transform: scale(1.02);
}

.btn-favorite.favorited:hover {
    background: linear-gradient(135deg, #ff5252 0%, #c82333 50%, #bd2130 100%);
    transform: translateY(-2px) scale(1.1);
    box-shadow:
        0 12px 35px rgba(220, 53, 69, 0.5),
        0 0 30px rgba(220, 53, 69, 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    border-radius: 50%;
}

/* تأثير النقر المحسن */
.btn-favorite:active {
    transform: translateY(0) scale(0.95);
    transition: all 0.1s ease;
}

/* تأثير التحميل المحسن */
.btn-favorite.loading {
    pointer-events: none;
    opacity: 0.8;
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border-color: #6c757d;
}

.btn-favorite.loading i {
    animation: heartPulse 1.2s ease-in-out infinite;
}

/* تأثيرات الحركة المحسنة */
@keyframes favoriteAdded {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
        box-shadow:
            0 6px 20px rgba(220, 53, 69, 0.6),
            0 0 30px rgba(220, 53, 69, 0.4);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes heartPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* تأثير الموجة عند النقر */
.btn-favorite.clicked::after {
    animation: ripple 0.6s ease-out;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .btn-favorite {
        width: 38px;
        height: 38px;
        min-width: 38px;
        min-height: 38px;
        font-size: 14px;
        border-radius: 50%;
        aspect-ratio: 1;
        transition: all 0.2s ease;
    }

    .btn-favorite:hover {
        transform: translateY(-1px) scale(1.05);
        border-radius: 50%;
    }

    .btn-favorite.favorited {
        border-radius: 50%;
    }

    .btn-favorite:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* تحسين عداد شريط التنقل للموبايل */
    .navbar-nav .nav-link .badge.favorites-count {
        animation: none; /* إيقاف الأنيميشن المستمر على الموبايل */
        font-size: 0.7rem;
        min-width: 22px;
        height: 22px;
        top: -6px;
        right: -6px;
        border-width: 2px;
        font-weight: 800;
    }

    .favorites-nav-link {
        padding: 8px 12px !important;
    }

    .favorites-nav-link .fa-heart {
        font-size: 1.1em;
    }
}

/* تحسينات إضافية للاستدارة المثالية */
.btn-favorite,
.btn-favorite:hover,
.btn-favorite:focus,
.btn-favorite:active,
.btn-favorite.favorited,
.btn-favorite.favorited:hover {
    border-radius: 50% !important;
    -webkit-border-radius: 50% !important;
    -moz-border-radius: 50% !important;
}

/* تحسين الحالة غير المفعلة - لون رمادي أنيق */
.btn-favorite:not(.favorited) {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #adb5bd;
    color: #6c757d;
    box-shadow:
        0 3px 12px rgba(173, 181, 189, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.btn-favorite:not(.favorited):hover {
    border-color: #dc3545;
    color: white;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    box-shadow:
        0 8px 25px rgba(220, 53, 69, 0.35),
        0 0 20px rgba(220, 53, 69, 0.25);
}

/* تأثيرات إضافية للتفاعل */
.btn-favorite:focus {
    outline: none;
    box-shadow:
        0 0 0 3px rgba(220, 53, 69, 0.3),
        0 4px 15px rgba(220, 53, 69, 0.4);
}

/* تحسين الأداء */
.btn-favorite {
    will-change: transform, box-shadow;
    backface-visibility: hidden;
}

/* ===== تأثيرات CSS متقدمة إضافية ===== */

/* تأثير الظل المتحرك للبطاقات عند hover */
.ad-card:hover {
    transform: translateY(-5px);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.1),
        0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثير الوهج للعناصر التفاعلية */
.favorites-glow {
    position: relative;
}

.favorites-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6b6b, #dc3545, #c82333, #ff6b6b);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: gradientShift 3s ease infinite;
}

.favorites-glow:hover::before {
    opacity: 0.7;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* تأثير الجسيمات المتحركة */
.particle-effect {
    position: relative;
    overflow: hidden;
}

.particle-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: #ff6b6b;
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    transition: all 0.3s ease;
}

.particle-effect.active::after {
    transform: translate(-50%, -50%) scale(20);
    opacity: 0.3;
}

/* تأثير النص المتحرك */
.text-shimmer {
    background: linear-gradient(90deg, #dc3545 0%, #ff6b6b 50%, #dc3545 100%);
    background-size: 200% 100%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* تأثير الموجة الدائرية */
.wave-effect {
    position: relative;
    overflow: hidden;
}

.wave-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(220, 53, 69, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.wave-effect.active::before {
    width: 300px;
    height: 300px;
    opacity: 0;
}

/* تأثير الاهتزاز اللطيف */
@keyframes gentleShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

.shake-gentle {
    animation: gentleShake 0.5s ease-in-out;
}

/* تأثير الإضاءة الخلفية */
.backlight-effect {
    position: relative;
    z-index: 1;
}

.backlight-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(220, 53, 69, 0.1) 0%, transparent 70%);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.backlight-effect:hover::before {
    opacity: 1;
}

/* تأثير التدرج المتحرك للخلفية */
.gradient-bg {
    background: linear-gradient(-45deg, #ff6b6b, #dc3545, #c82333, #ff5252);
    background-size: 400% 400%;
    animation: gradientMove 4s ease infinite;
}

@keyframes gradientMove {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير الظل الملون */
.colored-shadow {
    box-shadow:
        0 4px 15px rgba(220, 53, 69, 0.3),
        0 0 20px rgba(255, 107, 107, 0.2);
    transition: box-shadow 0.3s ease;
}

.colored-shadow:hover {
    box-shadow:
        0 8px 25px rgba(220, 53, 69, 0.4),
        0 0 30px rgba(255, 107, 107, 0.3);
}

/* تأثير الحدود المتوهجة */
.glowing-border {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #ff6b6b, #dc3545) border-box;
    transition: all 0.3s ease;
}

.glowing-border:hover {
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #ff5252, #c82333) border-box;
    box-shadow: 0 0 20px rgba(220, 53, 69, 0.4);
}

/* ===== تحسينات التوافق والأداء ===== */

/* تحسين الأداء للتأثيرات */
.favorites-count,
.btn-favorite,
.navbar-nav .nav-link .badge {
    will-change: transform, opacity, box-shadow;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تم دمج هذا القسم مع القسم السابق لتجنب التكرار */

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .favorites-count,
    .btn-favorite {
        -webkit-font-smoothing: subpixel-antialiased;
    }
}

/* تحسينات للوضع المظلم (معطل مؤقتاً) */
/* تم إلغاء تفعيل الوضع المظلم مؤقتاً - سيتم تفعيله لاحقاً عند الطلب */
/*
@media (prefers-color-scheme: dark) {
    .btn-favorite {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        border-color: #ff6b6b;
        color: #ff6b6b;
    }

    .btn-favorite:hover {
        background: linear-gradient(135deg, #ff6b6b 0%, #dc3545 100%);
        color: white;
    }
}
*/

/* تحسينات لتقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
    .favorites-count,
    .btn-favorite,
    .navbar-nav .nav-link .badge {
        animation: none !important;
        transition: none !important;
    }

    .btn-favorite:hover {
        transform: none;
    }
}

/* تحسينات للطباعة */
@media print {
    .btn-favorite,
    .favorites-count {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        border: 1px solid black !important;
    }
}

/* تحسينات إضافية للأداء */
.ad-card {
    contain: layout style paint;
}

.btn-favorite {
    contain: layout style;
}

/* تحسين التباين للوصولية */
@media (prefers-contrast: high) {
    .favorites-count {
        background: #000000 !important;
        color: #ffffff !important;
        border: 3px solid #ffffff !important;
        font-weight: 900 !important;
    }

    .btn-favorite {
        border-width: 3px !important;
        font-weight: bold !important;
    }

    .btn-favorite:not(.favorited) {
        background: #ffffff !important;
        color: #000000 !important;
        border-color: #000000 !important;
    }

    .btn-favorite.favorited {
        background: #000000 !important;
        color: #ffffff !important;
        border-color: #ffffff !important;
    }
}

/* ===== تحسينات نهائية للجودة والوضوح ===== */

/* تحسين الظلال للوضوح */
.btn-favorite,
.favorites-count {
    filter: contrast(1.1) saturate(1.1);
}

/* تحسين الخطوط للوضوح */
.favorites-count {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-variant-numeric: tabular-nums;
}

/* تحسين التفاعل للمس */
@media (hover: none) and (pointer: coarse) {
    .btn-favorite:hover {
        transform: none;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .btn-favorite.favorited:hover {
        transform: scale(1.02);
    }

    .favorites-nav-link:hover {
        transform: none;
        background: rgba(255, 255, 255, 0.1);
    }
}

/* تحسين الأداء */
.btn-favorite,
.favorites-count,
.favorites-nav-link {
    contain: layout style paint;
    will-change: transform, opacity;
}

/* بطاقات نتائج البحث */
.search-result-card {
    border-left: 4px solid var(--ad-card-primary);
    transition: all 0.3s ease;
}

.search-result-card:hover {
    border-left-color: var(--ad-card-success);
    transform: translateX(5px);
}

/* بطاقات المفضلة */
.ad-card-favorites {
    border: 2px solid #dc3545 !important;
    transition: all 0.3s ease;
    position: relative;
    background: linear-gradient(135deg, #fff 0%, #fff5f5 100%) !important;
}

.ad-card-favorites:hover {
    border-color: #dc3545 !important;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.25) !important;
}

.ad-card-favorites::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #dc3545, #fd7e14, #ffc107);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ad-card-favorites:hover::before {
    opacity: 0.1;
}

/* أزرار إزالة المفضلة */
.remove-favorite-btn {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: rgba(220, 53, 69, 0.9) !important;
    border: none !important;
    backdrop-filter: blur(10px);
    color: white !important;
    border-radius: 50% !important;
}

.remove-favorite-btn:hover {
    transform: scale(1.1);
    background: rgba(220, 53, 69, 1) !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    color: white !important;
}

.remove-favorite-btn i {
    font-size: 14px;
}

/* معلومات المفضلة */
.favorites-meta {
    border-left: 3px solid #dc3545 !important;
    background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%) !important;
    border-radius: 8px !important;
    border: 1px solid #fecaca !important;
}

.favorites-meta small {
    font-size: 0.75rem;
    line-height: 1.4;
}

.favorites-meta .text-primary {
    color: #dc3545 !important;
}

.favorites-meta .text-info {
    color: #0ea5e9 !important;
}

/* بطاقات التفصيلية للبحث */
.ad-card-detailed {
    min-height: 600px;
}

.ad-card-detailed .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.ad-card-detailed .ad-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.ad-card-detailed .ad-description {
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    -webkit-line-clamp: 3;
    line-clamp: 3;
}

/* بطاقات مضغوطة للصفحة الرئيسية */
.ad-card-compact {
    min-height: 480px;
}

.ad-card-compact .card-body {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 0.75rem;
}

.ad-card-compact .ad-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.ad-card-compact .ad-description {
    font-size: 0.85rem;
    -webkit-line-clamp: 2;
    line-clamp: 2;
}

/*
 * تحسينات للأجهزة المحمولة - الأجهزة اللوحية والأصغر
 * نقطة الكسر موحدة مع responsive.css: 767.98px
 */
@media (max-width: 767.98px) {
    .remove-favorite-btn {
        width: 30px;
        height: 30px;
    }

    .favorites-meta {
        font-size: 0.8rem;
    }

    .ad-card-detailed {
        min-height: 500px;
    }

    .ad-card-compact {
        min-height: 400px;
    }

    .search-result-card:hover {
        transform: none;
    }
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
}

.expiry-info {
    padding-top: 0.5rem;
    border-top: 1px solid #f1f5f9;
}

/* ===== الاستجابة للشاشات المختلفة ===== */
/*
 * تحسينات للأجهزة اللوحية والأصغر
 * نقطة الكسر موحدة: 767.98px (أقل من md في Bootstrap)
 */
@media (max-width: 767.98px) {
    .ad-card-list {
        flex-direction: column;
    }

    .ad-card-list .ad-image {
        width: 100%;
        height: 200px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }

    .action-buttons .left-actions,
    .action-buttons .right-actions {
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .meta-simple {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/*
 * تحسينات للهواتف المحمولة
 * نقطة الكسر موحدة: 575.98px (أقل من sm في Bootstrap)
 */
@media (max-width: 575.98px) {
    .ad-card {
        margin-bottom: 1rem;
    }

    .ad-card-compact,
    .ad-card-detailed {
        min-height: auto;
    }

    .favorite-icon {
        top: 8px;
        right: 8px;
    }

    .btn-favorite {
        width: 36px;
        height: 36px;
    }

    .new-badge {
        top: 8px;
        left: 8px;
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
    }

    .featured-badge {
        top: 8px;
        left: 8px;
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        transform: translateY(36px); /* إزاحة مناسبة للشاشات الصغيرة */
    }

    .urgent-badge {
        top: 8px;
        left: 8px;
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        transform: translateY(72px); /* إزاحة أكثر للشاشات الصغيرة */
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.ad-card:focus-within {
    outline: 2px solid var(--ad-card-primary);
    outline-offset: 2px;
}

.btn-favorite:focus {
    outline: 2px solid var(--ad-card-primary);
    outline-offset: 2px;
}

.category-badge:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

/* إصلاح مشكلة عرض شارة التصنيف - قواعد قوية */
.ad-card .category-badge,
.card .category-badge,
.category-badge-container .category-badge,
.category-badge-link .category-badge {
    display: inline-flex !important;
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    white-space: nowrap !important;
}

.ad-card .category-badge-container,
.card .category-badge-container {
    display: inline-block !important;
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
}

.ad-card .category-badge-link,
.card .category-badge-link {
    display: inline-block !important;
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
}

/* ===== تحسينات الأداء ===== */
.ad-card * {
    box-sizing: border-box;
}

.ad-image img {
    will-change: transform;
}

.btn-favorite {
    will-change: transform, background-color;
}

/* ===== فرض الوضع الفاتح دائماً ===== */
.ad-card {
    background: #ffffff !important;
    color: #1e293b !important;
}

.ad-title {
    color: #1e293b !important;
}

.ad-description {
    color: #64748b !important;
}

.contact-info {
    background: #f8fafc !important;
    border-left-color: var(--ad-card-primary) !important;
}

.card-footer {
    border-top-color: #e2e8f0 !important;
}

.expiry-info {
    border-top-color: #f1f5f9 !important;
}

/* ===== تأثيرات خاصة ===== */
.ad-card.featured {
    border: 2px solid #8b5cf6;
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.ad-card.urgent {
    border: 2px solid var(--ad-card-danger);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.ad-card.sold {
    opacity: 0.6;
    position: relative;
}

.ad-card.sold::after {
    content: "{{ __('Sold') }}";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    background: var(--ad-card-danger);
    color: white;
    padding: 0.5rem 2rem;
    font-weight: bold;
    font-size: 1.2rem;
    z-index: 20;
    border-radius: var(--ad-card-border-radius-sm);
}