{{-- قسم التعليقات --}}
@if($config['showComments'] ?? false)
    <div class="comments-section mt-3">
        @php
            $commentsCount = $ad->comments_count;
        @endphp

        <!-- رأس قسم التعليقات -->
        <div class="comments-header d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">
                <i class="fas fa-comments me-2"></i>
                {{ __('Comments') }} 
                <span class="badge bg-secondary">{{ $commentsCount }}</span>
            </h6>
            
            @auth
                <button class="btn btn-outline-primary btn-sm" 
                        onclick="toggleCommentForm({{ $ad->id }})"
                        id="addCommentBtn{{ $ad->id }}">
                    <i class="fas fa-plus me-1"></i>
                    {{ __('Add Comment') }}
                </button>
            @else
                <small class="text-muted">
                    <a href="{{ route('login') }}" class="text-decoration-none">
                        {{ __('Login to comment') }}
                    </a>
                </small>
            @endauth
        </div>

        <!-- نموذج إضافة تعليق -->
        @auth
        <div class="comment-form-container" id="commentForm{{ $ad->id }}" style="display: none;">
            <div class="card border-0 bg-light mb-3">
                <div class="card-body p-3">
                    <form id="addCommentForm{{ $ad->id }}" onsubmit="submitComment(event, {{ $ad->id }})">
                        <div class="mb-3">
                            <textarea class="form-control" 
                                      id="commentContent{{ $ad->id }}"
                                      name="content" 
                                      rows="3" 
                                      placeholder="{{ __('Write your comment...') }}"
                                      required></textarea>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                {{ __('Comments are reviewed before publishing') }}
                            </small>
                            <div>
                                <button type="button" 
                                        class="btn btn-secondary btn-sm me-2" 
                                        onclick="toggleCommentForm({{ $ad->id }})">
                                    {{ __('Cancel') }}
                                </button>
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-paper-plane me-1"></i>
                                    {{ __('Submit') }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        @endauth

        <!-- قائمة التعليقات -->
        <div class="comments-list" id="commentsList{{ $ad->id }}">
            @if($commentsCount > 0)
                <div class="text-center">
                    <button class="btn btn-outline-secondary btn-sm" 
                            onclick="loadComments({{ $ad->id }})"
                            id="loadCommentsBtn{{ $ad->id }}">
                        <i class="fas fa-eye me-1"></i>
                        {{ __('Show Comments') }} ({{ $commentsCount }})
                    </button>
                </div>
            @else
                <div class="text-center text-muted py-3">
                    <i class="fas fa-comment-slash fa-2x mb-2 opacity-50"></i>
                    <p class="mb-0">{{ __('No comments yet') }}</p>
                    <small>{{ __('Be the first to comment!') }}</small>
                </div>
            @endif
        </div>
    </div>
@endif

<style>
/* تنسيق التعليقات */
.comments-section {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
}

.comment-item {
    border-left: 3px solid #007bff;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    background: #f8f9fa;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
}

.comment-item:hover {
    background: #e9ecef;
    border-left-color: #0056b3;
}

.comment-reply {
    margin-left: 2rem;
    border-left-color: #6c757d;
    background: #ffffff;
}

.comment-reply:hover {
    background: #f8f9fa;
}

.comment-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

.comment-content {
    margin: 0.5rem 0;
    line-height: 1.6;
}

.comment-actions {
    font-size: 0.8rem;
}

.comment-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.comment-form-container {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-comments {
    text-align: center;
    padding: 2rem;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
// متغيرات التعليقات
let commentsLoaded = {};
let replyForms = {};

// تبديل نموذج التعليق
function toggleCommentForm(adId) {
    const form = document.getElementById(`commentForm${adId}`);
    const btn = document.getElementById(`addCommentBtn${adId}`);
    
    if (form.style.display === 'none') {
        form.style.display = 'block';
        btn.innerHTML = '<i class="fas fa-times me-1"></i>{{ __("Cancel") }}';
        document.getElementById(`commentContent${adId}`).focus();
    } else {
        form.style.display = 'none';
        btn.innerHTML = '<i class="fas fa-plus me-1"></i>{{ __("Add Comment") }}';
        document.getElementById(`addCommentForm${adId}`).reset();
    }
}

// إرسال تعليق
function submitComment(event, adId, parentId = null) {
    event.preventDefault();
    
    const formId = parentId ? `replyForm${parentId}` : `addCommentForm${adId}`;
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    
    formData.append('ad_id', adId);
    if (parentId) {
        formData.append('parent_id', parentId);
    }
    
    // تعطيل النموذج
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>{{ __("Submitting...") }}';
    
    fetch('{{ route("dashboard.api.comments.store") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إخفاء النموذج
            if (parentId) {
                toggleReplyForm(parentId);
            } else {
                toggleCommentForm(adId);
            }
            
            // إظهار رسالة نجاح
            if (typeof showToast === 'function') {
                showToast(data.message, 'success');
            } else {
                alert(data.message);
            }
            
            // إعادة تحميل التعليقات إذا كانت محملة
            if (commentsLoaded[adId]) {
                loadComments(adId);
            }
        } else {
            if (typeof showToast === 'function') {
                showToast(data.message, 'error');
            } else {
                alert(data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof showToast === 'function') {
            showToast('{{ __("Unexpected error occurred") }}', 'error');
        } else {
            alert('{{ __("Unexpected error occurred") }}');
        }
    })
    .finally(() => {
        // إعادة تفعيل النموذج
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

// تحميل التعليقات
function loadComments(adId) {
    const container = document.getElementById(`commentsList${adId}`);
    const btn = document.getElementById(`loadCommentsBtn${adId}`);
    
    // إظهار مؤشر التحميل
    container.innerHTML = `
        <div class="loading-comments">
            <div class="loading-spinner"></div>
            <p>{{ __("Loading comments...") }}</p>
        </div>
    `;
    
    fetch(`{{ route("dashboard.api.comments.index", ":adId") }}`.replace(':adId', adId), {
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayComments(adId, data.comments.data);
            commentsLoaded[adId] = true;
        } else {
            container.innerHTML = `
                <div class="text-center text-danger py-3">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>{{ __("Error loading comments") }}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        container.innerHTML = `
            <div class="text-center text-danger py-3">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>{{ __("Error loading comments") }}</p>
            </div>
        `;
    });
}

// عرض التعليقات
function displayComments(adId, comments) {
    const container = document.getElementById(`commentsList${adId}`);
    
    if (comments.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-comment-slash fa-2x mb-2 opacity-50"></i>
                <p class="mb-0">{{ __("No comments yet") }}</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    comments.forEach(comment => {
        html += renderComment(comment);
    });
    
    container.innerHTML = html;
}

// رندر تعليق واحد
function renderComment(comment, isReply = false) {
    const replyClass = isReply ? 'comment-reply' : '';
    let html = `
        <div class="comment-item ${replyClass}" data-comment-id="${comment.id}">
            <div class="comment-meta d-flex justify-content-between align-items-start mb-2">
                <div>
                    <strong>${comment.user.name}</strong>
                    <small class="text-muted ms-2">${comment.created_at}</small>
                    ${comment.is_edited ? '<small class="badge bg-secondary ms-1">{{ __("Edited") }}</small>' : ''}
                </div>
                <div class="comment-actions">
                    ${comment.likes_count > 0 ? `<span class="me-2"><i class="fas fa-heart text-danger"></i> ${comment.likes_count}</span>` : ''}
                </div>
            </div>
            <div class="comment-content">
                ${comment.content}
            </div>
            <div class="comment-actions mt-2">
                @auth
                <button class="btn btn-link btn-sm p-0 me-3" onclick="likeComment(${comment.id})">
                    <i class="far fa-heart"></i> {{ __("Like") }}
                </button>
                ${comment.can_reply && !isReply ? `<button class="btn btn-link btn-sm p-0 me-3" onclick="toggleReplyForm(${comment.id})">
                    <i class="fas fa-reply"></i> {{ __("Reply") }}
                </button>` : ''}
                ${comment.can_edit ? `<button class="btn btn-link btn-sm p-0 me-3" onclick="editComment(${comment.id})">
                    <i class="fas fa-edit"></i> {{ __("Edit") }}
                </button>` : ''}
                @endauth
                <button class="btn btn-link btn-sm p-0 text-danger" onclick="reportComment(${comment.id})">
                    <i class="fas fa-flag"></i> {{ __("Report") }}
                </button>
            </div>
            
            <!-- نموذج الرد -->
            @auth
            <div class="reply-form mt-3" id="replyForm${comment.id}" style="display: none;">
                <form onsubmit="submitComment(event, ${comment.ad_id}, ${comment.id})">
                    <div class="mb-2">
                        <textarea class="form-control form-control-sm" 
                                  name="content" 
                                  rows="2" 
                                  placeholder="{{ __('Write your reply...') }}"
                                  required></textarea>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="button" 
                                class="btn btn-secondary btn-sm me-2" 
                                onclick="toggleReplyForm(${comment.id})">
                            {{ __('Cancel') }}
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            {{ __('Reply') }}
                        </button>
                    </div>
                </form>
            </div>
            @endauth
        </div>
    `;
    
    // إضافة الردود
    if (comment.replies && comment.replies.length > 0) {
        comment.replies.forEach(reply => {
            html += renderComment(reply, true);
        });
    }
    
    return html;
}

// باقي الدوال...
function toggleReplyForm(commentId) {
    const form = document.getElementById(`replyForm${commentId}`);
    form.style.display = form.style.display === 'none' ? 'block' : 'none';
}

function likeComment(commentId) {
    // تنفيذ الإعجاب
    fetch(`{{ route("dashboard.api.comments.like", ":commentId") }}`.replace(':commentId', commentId), {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && typeof showToast === 'function') {
            showToast(data.message, 'success');
        }
    });
}

function reportComment(commentId) {
    const reason = prompt('{{ __("Please specify the reason for reporting:") }}');
    if (reason) {
        fetch(`{{ route("dashboard.api.comments.report", ":commentId") }}`.replace(':commentId', commentId), {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (typeof showToast === 'function') {
                showToast(data.message, data.success ? 'success' : 'error');
            } else {
                alert(data.message);
            }
        });
    }
}
</script>
