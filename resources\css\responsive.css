/* ===== التصميم المتجاوب المتقدم ===== */

/* نقاط التوقف المخصصة */
:root {
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
}

/* تحسينات للشاشات الصغيرة جداً (أقل من 576px) */
@media (max-width: 575.98px) {
    /* تحسين الحاويات */
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* تحسين النصوص */
    h1 {
        font-size: 1.8rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.3rem;
    }
    
    h4 {
        font-size: 1.1rem;
    }

    /* تحسين الأزرار */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .btn-lg {
        padding: 0.6rem 1.2rem;
        font-size: 1rem;
    }

    /* تحسين البطاقات */
    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* تحسين النماذج */
    .form-control {
        font-size: 16px; /* منع التكبير في iOS */
    }

    /* تحسين الجداول */
    .table-responsive-xs {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* إخفاء عناصر غير ضرورية */
    .d-xs-none {
        display: none !important;
    }

    /* تحسين شريط التنقل */
    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-nav .nav-link {
        padding: 0.5rem 0;
    }

    /* تحسين التذييل */
    footer .col-md-3,
    footer .col-md-4,
    footer .col-md-6 {
        margin-bottom: 1.5rem;
    }
}

/* تحسينات للأجهزة اللوحية الصغيرة (576px - 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
    /* تحسين الشبكة */
    .col-sm-auto-fit {
        flex: 1 1 auto;
    }

    /* تحسين البطاقات */
    .card-columns {
        column-count: 2;
    }

    /* تحسين النماذج */
    .form-row .col-sm-6 {
        margin-bottom: 0.5rem;
    }
}

/* تحسينات للأجهزة اللوحية (768px - 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
    /* تحسين الشبكة */
    .col-md-auto-fit {
        flex: 1 1 auto;
    }

    /* تحسين البطاقات */
    .card-columns {
        column-count: 2;
        column-gap: 1.5rem;
    }

    /* تحسين الصور */
    .img-responsive-md {
        max-width: 100%;
        height: auto;
    }
}

/* تحسينات لأجهزة الكمبيوتر المحمولة (992px - 1199.98px) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    /* تحسين البطاقات */
    .card-columns {
        column-count: 3;
        column-gap: 1.5rem;
    }

    /* تحسين الحاويات */
    .container-lg-fluid {
        max-width: 100%;
    }
}

/* تحسينات للشاشات الكبيرة (1200px فما فوق) */
@media (min-width: 1200px) {
    /* تحسين البطاقات */
    .card-columns {
        column-count: 4;
        column-gap: 2rem;
    }

    /* تحسين المسافات */
    .section-padding-xl {
        padding: 5rem 0;
    }
}

/* تحسينات للشاشات الكبيرة جداً (1400px فما فوق) */
@media (min-width: 1400px) {
    /* تحسين الحاويات */
    .container-xxl {
        max-width: 1320px;
    }

    /* تحسين النصوص */
    .display-1 {
        font-size: 6rem;
    }

    .display-2 {
        font-size: 5rem;
    }
}

/* فئات مساعدة للتصميم المتجاوب */

/* إظهار/إخفاء حسب حجم الشاشة */
.show-xs { display: block !important; }
.show-sm { display: none !important; }
.show-md { display: none !important; }
.show-lg { display: none !important; }
.show-xl { display: none !important; }

@media (min-width: 576px) {
    .show-xs { display: none !important; }
    .show-sm { display: block !important; }
}

@media (min-width: 768px) {
    .show-sm { display: none !important; }
    .show-md { display: block !important; }
}

@media (min-width: 992px) {
    .show-md { display: none !important; }
    .show-lg { display: block !important; }
}

@media (min-width: 1200px) {
    .show-lg { display: none !important; }
    .show-xl { display: block !important; }
}

/* أحجام النصوص المتجاوبة */
.text-responsive {
    font-size: clamp(0.9rem, 2.5vw, 1.2rem);
}

.heading-responsive {
    font-size: clamp(1.5rem, 5vw, 3rem);
}

/* مسافات متجاوبة */
.spacing-responsive {
    padding: clamp(1rem, 3vw, 3rem);
}

.margin-responsive {
    margin: clamp(0.5rem, 2vw, 2rem);
}

/* عرض متجاوب */
.width-responsive {
    width: clamp(300px, 90vw, 1200px);
}

/* ارتفاع متجاوب */
.height-responsive {
    height: clamp(200px, 50vh, 600px);
}

/* تحسينات للطباعة */
@media print {
    /* إخفاء عناصر غير ضرورية */
    .no-print,
    .navbar,
    .sidebar,
    .btn,
    .pagination,
    .breadcrumb {
        display: none !important;
    }

    /* تحسين النصوص */
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }

    /* تحسين الروابط */
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 10pt;
        color: #666;
    }

    /* تحسين الصفحات */
    .page-break {
        page-break-before: always;
    }

    .no-page-break {
        page-break-inside: avoid;
    }
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* تحسين الصور */
    .img-retina {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    /* تحسين الحدود */
    .border-retina {
        border-width: 0.5px;
    }
}

/* تحسينات للوضع الأفقي على الأجهزة المحمولة */
@media (max-width: 767.98px) and (orientation: landscape) {
    /* تقليل ارتفاع العناصر */
    .navbar {
        min-height: 50px;
    }

    .hero-section {
        min-height: 60vh;
    }

    /* تحسين النماذج */
    .modal-dialog {
        margin: 10px auto;
    }
}

/* تحسينات للوضع العمودي على الأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
    /* تحسين التخطيط */
    .container {
        max-width: 90%;
    }

    /* تحسين الأعمدة */
    .col-tablet-full {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* تحسينات لإمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
    /* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تم إلغاء تفعيل الوضع المظلم مؤقتاً */
/*
@media (prefers-color-scheme: dark) {
    :root {
        color-scheme: dark;
    }
}
*/

/* تحسينات للشاشات الصغيرة جداً (أقل من 320px) */
@media (max-width: 319.98px) {
    .container-fluid {
        padding-left: 5px;
        padding-right: 5px;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    h1 {
        font-size: 1.5rem;
    }
}

/* تحسينات للشاشات العريضة جداً (أكبر من 1920px) */
@media (min-width: 1920px) {
    .container-ultra-wide {
        max-width: 1600px;
        margin: 0 auto;
    }

    .section-padding-ultra {
        padding: 8rem 0;
    }
}
