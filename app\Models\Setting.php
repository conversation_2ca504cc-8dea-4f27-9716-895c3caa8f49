<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

/**
 * Model موحد للإعدادات
 * يحل محل: AdSetting, AnnouncementSetting
 */
class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_editable',
    ];

    protected $casts = [
        'is_editable' => 'boolean',
    ];

    /**
     * أنواع البيانات المدعومة
     */
    const TYPES = [
        'string' => 'نص',
        'integer' => 'رقم صحيح',
        'boolean' => 'صحيح/خطأ',
        'json' => 'JSON',
        'text' => 'نص طويل',
    ];

    /**
     * مجموعات الإعدادات
     */
    const GROUPS = [
        'general' => 'عام',
        'ads' => 'الإعلانات',
        'announcements' => 'الإعلانات الإدارية',
        'security' => 'الأمان',
        'notifications' => 'الإشعارات',
        'appearance' => 'المظهر',
    ];

    /**
     * Scope حسب المجموعة
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Scope للإعدادات القابلة للتعديل
     */
    public function scopeEditable($query)
    {
        return $query->where('is_editable', true);
    }

    /**
     * الحصول على قيمة الإعداد مع التحويل المناسب
     */
    public function getValueAttribute($value)
    {
        switch ($this->type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * تعيين قيمة الإعداد مع التحويل المناسب
     */
    public function setValueAttribute($value)
    {
        switch ($this->type) {
            case 'boolean':
                $this->attributes['value'] = $value ? '1' : '0';
                break;
            case 'json':
                $this->attributes['value'] = json_encode($value);
                break;
            default:
                $this->attributes['value'] = $value;
        }
    }

    /**
     * الحصول على قيمة إعداد
     */
    public static function get($key, $default = null)
    {
        $cacheKey = "setting_{$key}";
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * تعيين قيمة إعداد
     */
    public static function set($key, $value, $type = 'string', $group = 'general', $description = null)
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'group' => $group,
                'description' => $description,
            ]
        );

        // مسح الكاش
        Cache::forget("setting_{$key}");
        Cache::forget("settings_group_{$group}");

        return $setting;
    }

    /**
     * الحصول على جميع إعدادات مجموعة
     */
    public static function getGroup($group)
    {
        $cacheKey = "settings_group_{$group}";
        
        return Cache::remember($cacheKey, 3600, function () use ($group) {
            return static::where('group', $group)
                ->pluck('value', 'key')
                ->map(function ($value, $key) {
                    $setting = static::where('key', $key)->first();
                    return $setting ? $setting->value : $value;
                })
                ->toArray();
        });
    }

    /**
     * الحصول على جميع الإعدادات
     */
    public static function getAll()
    {
        return Cache::remember('all_settings', 3600, function () {
            $settings = static::all();
            $result = [];
            
            foreach ($settings as $setting) {
                $result[$setting->key] = $setting->value;
            }
            
            return $result;
        });
    }

    /**
     * مسح كاش الإعدادات
     */
    public static function clearCache($group = null)
    {
        if ($group) {
            Cache::forget("settings_group_{$group}");
        } else {
            Cache::forget('all_settings');
            // مسح كاش جميع المجموعات
            foreach (array_keys(static::GROUPS) as $groupKey) {
                Cache::forget("settings_group_{$groupKey}");
            }
        }
    }

    /**
     * إنشاء الإعدادات الافتراضية
     */
    public static function createDefaults()
    {
        $defaults = [
            // إعدادات عامة
            'site_name' => ['value' => 'MyAdsSite', 'type' => 'string', 'group' => 'general'],
            'site_description' => ['value' => 'موقع الإعلانات المبوبة', 'type' => 'text', 'group' => 'general'],
            'contact_email' => ['value' => '<EMAIL>', 'type' => 'string', 'group' => 'general'],
            'items_per_page' => ['value' => '12', 'type' => 'integer', 'group' => 'general'],
            
            // إعدادات الإعلانات
            'max_images_per_ad' => ['value' => '5', 'type' => 'integer', 'group' => 'ads'],
            'ad_expiry_days' => ['value' => '30', 'type' => 'integer', 'group' => 'ads'],
            'require_ad_approval' => ['value' => '1', 'type' => 'boolean', 'group' => 'ads'],
            'allow_contact_reveal' => ['value' => '1', 'type' => 'boolean', 'group' => 'ads'],
            
            // إعدادات الأمان
            'max_login_attempts' => ['value' => '5', 'type' => 'integer', 'group' => 'security'],
            'lockout_duration' => ['value' => '15', 'type' => 'integer', 'group' => 'security'],
            'password_min_length' => ['value' => '8', 'type' => 'integer', 'group' => 'security'],
            'require_email_verification' => ['value' => '1', 'type' => 'boolean', 'group' => 'security'],
            
            // إعدادات الإشعارات
            'enable_email_notifications' => ['value' => '1', 'type' => 'boolean', 'group' => 'notifications'],
            'enable_sms_notifications' => ['value' => '0', 'type' => 'boolean', 'group' => 'notifications'],
            'notification_frequency' => ['value' => 'immediate', 'type' => 'string', 'group' => 'notifications'],
        ];

        foreach ($defaults as $key => $config) {
            static::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'group' => $config['group'],
                    'description' => "إعداد افتراضي: {$key}",
                    'is_editable' => true,
                ]
            );
        }
    }

    /**
     * التحقق من صحة نوع البيانات
     */
    public function validateValue($value)
    {
        switch ($this->type) {
            case 'integer':
                return is_numeric($value);
            case 'boolean':
                return in_array($value, [true, false, '1', '0', 1, 0]);
            case 'json':
                json_decode($value);
                return json_last_error() === JSON_ERROR_NONE;
            default:
                return true;
        }
    }
}
