<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Category;
use App\Models\Ad;
use App\Models\Interaction;
use App\Models\SystemLog;
use App\Models\Setting;
use App\Services\InputSanitizationService;
use App\Services\EncryptionService;

/**
 * بذر قاعدة البيانات الرئيسية - محدث للهيكل المحسن
 * يحتوي على جميع البيانات المطلوبة للاختبار الكامل مع النظام الجديد
 */
class DatabaseSeeder extends Seeder
{
    /**
     * تشغيل البذر الشامل للنظام المحسن
     */
    public function run(): void
    {
        $this->command->info('🌱 بدء بذر البيانات الشاملة للنظام المحسن...');
        $this->command->newLine();

        // 1. إنشاء المستخدم الإداري
        $this->createAdminUser();

        // 2. إنشاء المستخدمين التجريبيين
        $this->createTestUsers();

        // 3. إنشاء الفئات
        $this->createCategories();

        // 4. إنشاء الإعدادات الافتراضية
        $this->createDefaultSettings();

        // 5. إنشاء الإعلانات التجريبية
        $this->createTestAds();

        // 6. إنشاء إعلانات الأسعار المتقدمة
        $this->createAdvancedPricingTestAds();

        // 7. إنشاء التفاعلات التجريبية
        $this->createTestInteractions();

        // 8. إنشاء السجلات التجريبية
        $this->createTestSystemLogs();

        $this->command->newLine();
        $this->command->info('🎉 تم بذر جميع البيانات بنجاح!');
        $this->command->info('👤 يمكنك تسجيل الدخول كمدير: <EMAIL>');
        $this->command->warn('🔑 كلمة المرور الإدارية تم عرضها أعلاه - احفظها في مكان آمن!');
        $this->command->info('🧪 المستخدمين التجريبيين: <EMAIL>, <EMAIL>, <EMAIL>');
        $this->command->info('🔑 كلمة مرور المستخدمين التجريبيين: TestPass123!');

        // عرض إحصائيات البيانات المنشأة
        $this->displayDataStatistics();
    }

    /**
     * إنشاء المستخدم الإداري مع الحقول الجديدة
     */
    private function createAdminUser(): void
    {
        $this->command->info('👑 إنشاء المستخدم الإداري...');

        // التحقق من وجود المستخدم الإداري
        $existingAdmin = User::where('email', '<EMAIL>')->first();

        if ($existingAdmin) {
            $this->command->warn('⚠️  المستخدم الإداري موجود بالفعل');
            return;
        }

        // إنشاء كلمة مرور قوية
        $securePassword = $this->generateSecurePassword();

        // إنشاء المستخدم الإداري مع الحقول الجديدة
        User::create([
            'name' => 'المدير العام',
            'email' => '<EMAIL>',
            'password' => Hash::make($securePassword),
            'is_admin' => true,
            'email_verified_at' => now(),
            'phone' => EncryptionService::encryptText('+967-1-555000'),
            'whatsapp' => EncryptionService::encryptText('+967-777-555000'),
            'preferred_currency' => 'YER',
            'privacy_settings' => json_encode([
                'show_phone' => true,
                'show_email' => true,
                'show_whatsapp' => true,
                'allow_contact_reveal' => true,
                'show_online_status' => false,
            ]),
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $this->command->info('✅ تم إنشاء المستخدم الإداري بنجاح');
        $this->command->info('📧 البريد الإلكتروني: <EMAIL>');
        $this->command->line('🔑 كلمة المرور: <fg=red>' . $securePassword . '</fg=red>');
        $this->command->warn('⚠️  احفظ كلمة المرور في مكان آمن!');
    }

    /**
     * إنشاء المستخدمين التجريبيين مع الحقول الجديدة
     */
    private function createTestUsers(): void
    {
        $this->command->info('👥 إنشاء المستخدمين التجريبيين...');

        $testUsers = [
            ['name' => 'أحمد محمد السالم', 'email' => '<EMAIL>', 'password' => 'TestPass123!'],
            ['name' => 'فاطمة علي الزهراني', 'email' => '<EMAIL>', 'password' => 'TestPass123!'],
            ['name' => 'محمد سالم العمري', 'email' => '<EMAIL>', 'password' => 'TestPass123!'],
            ['name' => 'عائشة أحمد الحسني', 'email' => '<EMAIL>', 'password' => 'TestPass123!'],
            ['name' => 'يوسف عبدالله المقطري', 'email' => '<EMAIL>', 'password' => 'TestPass123!'],
        ];

        foreach ($testUsers as $index => $userData) {
            $phoneNumbers = ['+967-777-123456', '+967-733-654321', '+967-770-987654', '+967-711-456789', '+967-777-321654'];

            User::firstOrCreate(
                ['email' => $userData['email']],
                [
                    'name' => InputSanitizationService::sanitizeText($userData['name']),
                    'email' => InputSanitizationService::sanitizeEmail($userData['email']),
                    'password' => Hash::make($userData['password']),
                    'email_verified_at' => now(),
                    'is_admin' => false,
                    'phone' => EncryptionService::encryptText($phoneNumbers[$index] ?? '+967-777-000000'),
                    'whatsapp' => EncryptionService::encryptText($phoneNumbers[$index] ?? '+967-777-000000'),
                    'preferred_currency' => ['YER', 'USD', 'SAR', 'AED'][rand(0, 3)],
                    'privacy_settings' => json_encode([
                        'show_phone' => rand(0, 1) == 1,
                        'show_email' => rand(0, 1) == 1,
                        'show_whatsapp' => rand(0, 1) == 1,
                        'allow_contact_reveal' => rand(0, 1) == 1,
                        'show_online_status' => rand(0, 1) == 1,
                    ]),
                    'is_active' => true,
                    'created_at' => now()->subDays(rand(1, 30)),
                    'updated_at' => now(),
                ]
            );
        }

        $this->command->info('✅ تم إنشاء ' . count($testUsers) . ' مستخدمين تجريبيين');
    }

    /**
     * إنشاء كلمة مرور قوية
     */
    private function generateSecurePassword(): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*';

        $password = '';
        $password .= $uppercase[rand(0, strlen($uppercase) - 1)];
        $password .= $lowercase[rand(0, strlen($lowercase) - 1)];
        $password .= $numbers[rand(0, strlen($numbers) - 1)];
        $password .= $symbols[rand(0, strlen($symbols) - 1)];

        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < 12; $i++) {
            $password .= $allChars[rand(0, strlen($allChars) - 1)];
        }

        return str_shuffle($password);
    }

    /**
     * إنشاء الإعدادات الافتراضية للنظام الجديد
     */
    private function createDefaultSettings(): void
    {
        $this->command->info('⚙️ إنشاء الإعدادات الافتراضية...');

        try {
            Setting::createDefaults();
            $this->command->info('✅ تم إنشاء الإعدادات الافتراضية بنجاح');
        } catch (\Exception $e) {
            $this->command->error('❌ فشل في إنشاء الإعدادات: ' . $e->getMessage());
        }
    }

    /**
     * إنشاء التصنيفات الشاملة والمتنوعة
     */
    private function createCategories(): void
    {
        $this->command->info('📂 إنشاء التصنيفات الشاملة...');

        $categories = [
            // السيارات والمركبات 🚗
            [
                'name_ar' => 'السيارات والمركبات',
                'name_en' => 'Cars & Vehicles',
                'description_ar' => 'سيارات للبيع والشراء، دراجات نارية، قطع غيار، خدمات السيارات',
                'description_en' => 'Cars for sale and purchase, motorcycles, spare parts, car services',
                'icon' => 'car',
                'is_active' => true,
                'sort_order' => 1,
                'subcategories' => [
                    ['name_ar' => 'سيارات للبيع', 'name_en' => 'Cars for Sale', 'icon' => 'car-side'],
                    ['name_ar' => 'سيارات للإيجار', 'name_en' => 'Car Rental', 'icon' => 'key'],
                    ['name_ar' => 'دراجات نارية', 'name_en' => 'Motorcycles', 'icon' => 'motorcycle'],
                    ['name_ar' => 'قطع غيار', 'name_en' => 'Spare Parts', 'icon' => 'cog'],
                    ['name_ar' => 'خدمات السيارات', 'name_en' => 'Car Services', 'icon' => 'tools'],
                ]
            ],
            // العقارات 🏠
            [
                'name_ar' => 'العقارات',
                'name_en' => 'Real Estate',
                'description_ar' => 'شقق، فيلات، أراضي، مكاتب تجارية للبيع والإيجار',
                'description_en' => 'Apartments, villas, lands, commercial offices for sale and rent',
                'icon' => 'home',
                'is_active' => true,
                'sort_order' => 2,
                'subcategories' => [
                    ['name_ar' => 'شقق للبيع', 'name_en' => 'Apartments for Sale', 'icon' => 'building'],
                    ['name_ar' => 'شقق للإيجار', 'name_en' => 'Apartments for Rent', 'icon' => 'home-heart'],
                    ['name_ar' => 'فيلات ومنازل', 'name_en' => 'Villas & Houses', 'icon' => 'house-chimney'],
                    ['name_ar' => 'أراضي', 'name_en' => 'Lands', 'icon' => 'mountain'],
                    ['name_ar' => 'مكاتب ومحلات', 'name_en' => 'Offices & Shops', 'icon' => 'store'],
                ]
            ],
            // الإلكترونيات 📱
            [
                'name_ar' => 'الإلكترونيات والأجهزة',
                'name_en' => 'Electronics & Devices',
                'description_ar' => 'هواتف ذكية، حاسوب، أجهزة منزلية، ألعاب، كاميرات',
                'description_en' => 'Smartphones, computers, home appliances, games, cameras',
                'icon' => 'mobile-alt',
                'is_active' => true,
                'sort_order' => 3,
                'subcategories' => [
                    ['name_ar' => 'هواتف ذكية', 'name_en' => 'Smartphones', 'icon' => 'mobile-alt'],
                    ['name_ar' => 'حاسوب ولابتوب', 'name_en' => 'Computers & Laptops', 'icon' => 'laptop'],
                    ['name_ar' => 'أجهزة منزلية', 'name_en' => 'Home Appliances', 'icon' => 'blender'],
                    ['name_ar' => 'ألعاب وترفيه', 'name_en' => 'Games & Entertainment', 'icon' => 'gamepad'],
                    ['name_ar' => 'كاميرات وتصوير', 'name_en' => 'Cameras & Photography', 'icon' => 'camera'],
                ]
            ],
            // الوظائف 💼
            [
                'name_ar' => 'الوظائف والتوظيف',
                'name_en' => 'Jobs & Employment',
                'description_ar' => 'فرص عمل، وظائف حكومية، عمل حر، تدريب ودورات',
                'description_en' => 'Job opportunities, government jobs, freelance, training courses',
                'icon' => 'briefcase',
                'is_active' => true,
                'sort_order' => 4,
                'subcategories' => [
                    ['name_ar' => 'وظائف حكومية', 'name_en' => 'Government Jobs', 'icon' => 'university'],
                    ['name_ar' => 'وظائف خاصة', 'name_en' => 'Private Jobs', 'icon' => 'building'],
                    ['name_ar' => 'عمل حر', 'name_en' => 'Freelance', 'icon' => 'user-tie'],
                    ['name_ar' => 'تدريب ودورات', 'name_en' => 'Training & Courses', 'icon' => 'graduation-cap'],
                    ['name_ar' => 'وظائف بدوام جزئي', 'name_en' => 'Part-time Jobs', 'icon' => 'clock'],
                ]
            ],
            // الخدمات 🔧
            [
                'name_ar' => 'الخدمات',
                'name_en' => 'Services',
                'description_ar' => 'خدمات تنظيف، صيانة، تعليم، استشارات، خدمات طبية',
                'description_en' => 'Cleaning services, maintenance, education, consulting, medical services',
                'icon' => 'tools',
                'is_active' => true,
                'sort_order' => 5,
                'subcategories' => [
                    ['name_ar' => 'خدمات منزلية', 'name_en' => 'Home Services', 'icon' => 'broom'],
                    ['name_ar' => 'صيانة وإصلاح', 'name_en' => 'Maintenance & Repair', 'icon' => 'wrench'],
                    ['name_ar' => 'تعليم ودروس', 'name_en' => 'Education & Tutoring', 'icon' => 'chalkboard-teacher'],
                    ['name_ar' => 'استشارات', 'name_en' => 'Consulting', 'icon' => 'handshake'],
                    ['name_ar' => 'خدمات طبية', 'name_en' => 'Medical Services', 'icon' => 'user-md'],
                ]
            ],
            // الأزياء والموضة 👗
            [
                'name_ar' => 'الأزياء والموضة',
                'name_en' => 'Fashion & Style',
                'description_ar' => 'ملابس رجالية ونسائية، أحذية، إكسسوارات، ساعات',
                'description_en' => 'Men\'s and women\'s clothing, shoes, accessories, watches',
                'icon' => 'tshirt',
                'is_active' => true,
                'sort_order' => 6,
                'subcategories' => [
                    ['name_ar' => 'ملابس رجالية', 'name_en' => 'Men\'s Clothing', 'icon' => 'male'],
                    ['name_ar' => 'ملابس نسائية', 'name_en' => 'Women\'s Clothing', 'icon' => 'female'],
                    ['name_ar' => 'أحذية', 'name_en' => 'Shoes', 'icon' => 'shoe-prints'],
                    ['name_ar' => 'إكسسوارات', 'name_en' => 'Accessories', 'icon' => 'gem'],
                    ['name_ar' => 'ساعات', 'name_en' => 'Watches', 'icon' => 'clock'],
                ]
            ],
            // الأثاث والمنزل 🛋️
            [
                'name_ar' => 'الأثاث والمنزل',
                'name_en' => 'Furniture & Home',
                'description_ar' => 'أثاث منزلي، ديكورات، أدوات مطبخ، حدائق',
                'description_en' => 'Home furniture, decorations, kitchen tools, gardens',
                'icon' => 'couch',
                'is_active' => true,
                'sort_order' => 7,
                'subcategories' => [
                    ['name_ar' => 'أثاث غرف النوم', 'name_en' => 'Bedroom Furniture', 'icon' => 'bed'],
                    ['name_ar' => 'أثاث غرف المعيشة', 'name_en' => 'Living Room Furniture', 'icon' => 'couch'],
                    ['name_ar' => 'أدوات مطبخ', 'name_en' => 'Kitchen Tools', 'icon' => 'utensils'],
                    ['name_ar' => 'ديكورات', 'name_en' => 'Decorations', 'icon' => 'palette'],
                    ['name_ar' => 'حدائق ونباتات', 'name_en' => 'Gardens & Plants', 'icon' => 'seedling'],
                ]
            ],
            // الرياضة واللياقة 🏃‍♂️
            [
                'name_ar' => 'الرياضة واللياقة',
                'name_en' => 'Sports & Fitness',
                'description_ar' => 'أجهزة رياضية، ملابس رياضية، دراجات، معدات لياقة',
                'description_en' => 'Sports equipment, sportswear, bicycles, fitness equipment',
                'icon' => 'dumbbell',
                'is_active' => true,
                'sort_order' => 8,
                'subcategories' => [
                    ['name_ar' => 'أجهزة لياقة', 'name_en' => 'Fitness Equipment', 'icon' => 'dumbbell'],
                    ['name_ar' => 'ملابس رياضية', 'name_en' => 'Sportswear', 'icon' => 'running'],
                    ['name_ar' => 'دراجات هوائية', 'name_en' => 'Bicycles', 'icon' => 'bicycle'],
                    ['name_ar' => 'كرة القدم', 'name_en' => 'Football', 'icon' => 'futbol'],
                    ['name_ar' => 'رياضات أخرى', 'name_en' => 'Other Sports', 'icon' => 'table-tennis'],
                ]
            ],
        ];

        $createdCount = 0;
        foreach ($categories as $categoryData) {
            $subcategories = $categoryData['subcategories'] ?? [];
            unset($categoryData['subcategories']);

            // إنشاء التصنيف الرئيسي
            $category = Category::firstOrCreate(
                ['name_ar' => $categoryData['name_ar']],
                array_merge($categoryData, [
                    'slug' => Str::slug($categoryData['name_en']),
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );

            $createdCount++;

            // إنشاء التصنيفات الفرعية مع أيقونات مخصصة
            foreach ($subcategories as $index => $subData) {
                Category::firstOrCreate(
                    ['name_ar' => $subData['name_ar'], 'parent_id' => $category->id],
                    [
                        'name_en' => $subData['name_en'],
                        'slug' => Str::slug($subData['name_en']),
                        'parent_id' => $category->id,
                        'description_ar' => 'تصنيف فرعي لـ ' . $category->name_ar,
                        'description_en' => 'Subcategory of ' . $category->name_en,
                        'icon' => $subData['icon'] ?? $category->icon, // استخدام أيقونة مخصصة أو الأيقونة الرئيسية
                        'is_active' => true,
                        'sort_order' => $index + 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                );
                $createdCount++;
            }
        }

        $this->command->info('✅ تم إنشاء ' . $createdCount . ' تصنيف رئيسي وفرعي');
    }
    /**
     * إنشاء إعلانات تجريبية واقعية ومتنوعة
     */
    private function createTestAds(): void
    {
        $this->command->info('📢 إنشاء الإعلانات التجريبية الواقعية...');

        $users = User::where('is_admin', false)->get();
        $categories = Category::all();

        if ($users->isEmpty() || $categories->isEmpty()) {
            $this->command->warn('⚠️ لا توجد مستخدمين أو تصنيفات لإنشاء الإعلانات');
            return;
        }

        // إعلانات السيارات
        $carAds = [
            [
                'title_ar' => 'تويوتا كامري 2020 للبيع - حالة ممتازة',
                'title_en' => 'Toyota Camry 2020 for Sale - Excellent Condition',
                'description_ar' => 'سيارة تويوتا كامري موديل 2020، لون أبيض، ماشية 45,000 كم فقط. السيارة في حالة ممتازة، صيانة دورية منتظمة، جميع الأوراق سليمة. السعر قابل للتفاوض البسيط. للجادين فقط.',
                'description_en' => 'Toyota Camry 2020, white color, only 45,000 km. Car in excellent condition, regular maintenance, all papers valid. Price slightly negotiable. Serious buyers only.',
                'price' => 85000,
                'currency' => 'SAR',
                'location' => 'الرياض - حي الملز',
                'city' => 'الرياض',
                'country' => 'Saudi Arabia',
                'phone' => '+966-50-123-4567',
                'email' => '<EMAIL>',
                'whatsapp' => '+966-50-123-4567',
                'is_featured' => true,
                'is_urgent' => false,
                'category_name' => 'السيارات والمركبات',
            ],
            [
                'title_ar' => 'هونداي إلنترا 2019 - فحص كامل',
                'title_en' => 'Hyundai Elantra 2019 - Full Inspection',
                'description_ar' => 'هونداي إلنترا 2019، لون رمادي، ماشية 62,000 كم. تم عمل فحص كامل مؤخراً، تكييف بارد، إطارات جديدة. السيارة نظيفة جداً من الداخل والخارج. متوفرة للمعاينة في أي وقت.',
                'description_en' => 'Hyundai Elantra 2019, gray color, 62,000 km. Recently fully inspected, cold AC, new tires. Very clean inside and out. Available for viewing anytime.',
                'price' => 52000,
                'currency' => 'SAR',
                'location' => 'جدة - حي الصفا',
                'city' => 'جدة',
                'country' => 'Saudi Arabia',
                'phone' => '+966-55-987-6543',
                'email' => '<EMAIL>',
                'whatsapp' => '+966-55-987-6543',
                'is_featured' => false,
                'is_urgent' => true,
                'category_name' => 'السيارات والمركبات',
            ],
            [
                'title_ar' => 'تويوتا هايلكس 2020 دبل كابينة - صنعاء',
                'title_en' => 'Toyota Hilux 2020 Double Cabin - Sanaa',
                'description_ar' => 'تويوتا هايلكس دبل كابينة 2020، دفع رباعي، ديزل، حالة ممتازة. مناسبة للعمل والرحلات الجبلية. صيانة دورية، جميع الأوراق سليمة. السعر قابل للتفاوض للجادين.',
                'description_en' => 'Toyota Hilux double cabin 2020, 4WD, diesel, excellent condition. Suitable for work and mountain trips. Regular maintenance, all papers valid. Price negotiable for serious buyers.',
                'price' => 28000000,
                'currency' => 'YER',
                'location' => 'صنعاء - شارع الزبيري',
                'city' => 'صنعاء',
                'country' => 'Yemen',
                'phone' => '+967-1-234-567',
                'email' => '<EMAIL>',
                'whatsapp' => '+967-777-123-456',
                'is_featured' => true,
                'is_urgent' => false,
                'category_name' => 'السيارات والمركبات',
            ],
            [
                'title_ar' => 'كيا سيراتو 2018 اقتصادية - عدن',
                'title_en' => 'Kia Cerato 2018 Economic - Aden',
                'description_ar' => 'كيا سيراتو 2018، أوتوماتيك، اقتصادية في الوقود، مناسبة للاستخدام اليومي. تكييف بارد، راديو، حالة جيدة جداً. سعر مناسب ومعقول.',
                'description_en' => 'Kia Cerato 2018, automatic, fuel efficient, suitable for daily use. Cold AC, radio, very good condition. Reasonable and affordable price.',
                'price' => 18000000,
                'currency' => 'YER',
                'location' => 'عدن - كريتر',
                'city' => 'عدن',
                'country' => 'Yemen',
                'phone' => '+967-2-345-678',
                'email' => '<EMAIL>',
                'whatsapp' => '+967-733-987-654',
                'is_featured' => false,
                'is_urgent' => true,
                'category_name' => 'السيارات والمركبات',
            ],
        ];

        // إعلانات العقارات - السوق اليمني والعربي
        $realEstateAds = [
            [
                'title_ar' => 'شقة 3 غرف للإيجار - موقع مميز',
                'title_en' => '3-Bedroom Apartment for Rent - Prime Location',
                'description_ar' => 'شقة واسعة 3 غرف نوم + صالة + مطبخ + 2 حمام، الدور الثالث، مساحة 120 متر مربع. الشقة مفروشة بالكامل، تكييف مركزي، موقف سيارة. قريبة من المدارس والمستشفيات والمولات. عقد سنوي.',
                'description_en' => 'Spacious 3-bedroom apartment + living room + kitchen + 2 bathrooms, 3rd floor, 120 sqm. Fully furnished, central AC, parking space. Close to schools, hospitals, and malls. Annual contract.',
                'price' => 2500,
                'currency' => 'SAR',
                'price_type' => 'fixed',
                'location' => 'الرياض - حي العليا',
                'city' => 'الرياض',
                'country' => 'Saudi Arabia',
                'phone' => '+966-50-555-1234',
                'email' => '<EMAIL>',
                'whatsapp' => '+966-50-555-1234',
                'is_featured' => true,
                'is_urgent' => false,
                'category_name' => 'العقارات',
            ],
            [
                'title_ar' => 'فيلا للبيع - تشطيب فاخر',
                'title_en' => 'Villa for Sale - Luxury Finishing',
                'description_ar' => 'فيلا دورين + ملحق، 5 غرف نوم، 4 حمامات، مجلس، صالة كبيرة، مطبخ مجهز، حديقة واسعة، مسبح، موقف 4 سيارات. تشطيب فاخر، تصميم عصري. المساحة 400 متر مربع.',
                'description_en' => 'Two-story villa + annex, 5 bedrooms, 4 bathrooms, majlis, large living room, equipped kitchen, spacious garden, swimming pool, 4-car parking. Luxury finishing, modern design. 400 sqm area.',
                'price' => 1250000,
                'currency' => 'SAR',
                'price_type' => 'negotiable',
                'location' => 'الدمام - حي الشاطئ',
                'city' => 'الدمام',
                'country' => 'Saudi Arabia',
                'phone' => '+966-53-777-8888',
                'email' => '<EMAIL>',
                'whatsapp' => '+966-53-777-8888',
                'is_featured' => true,
                'is_urgent' => false,
                'category_name' => 'العقارات',
            ],
            [
                'title_ar' => 'شقة للبيع في صنعاء - حي حدة',
                'title_en' => 'Apartment for Sale in Sanaa - Hadda District',
                'description_ar' => 'شقة للبيع في حي حدة الراقي، 4 غرف + صالتين + مطبخ + 3 حمامات، الدور الثاني، مساحة 150 متر. تشطيب جيد، إطلالة جميلة، قريبة من الخدمات. سند ملكية جاهز.',
                'description_en' => 'Apartment for sale in upscale Hadda district, 4 rooms + 2 living rooms + kitchen + 3 bathrooms, 2nd floor, 150 sqm. Good finishing, beautiful view, close to services. Ownership deed ready.',
                'price' => 45000000,
                'currency' => 'YER',
                'price_type' => 'negotiable',
                'location' => 'صنعاء - حي حدة',
                'city' => 'صنعاء',
                'country' => 'Yemen',
                'phone' => '+967-1-456-789',
                'email' => '<EMAIL>',
                'whatsapp' => '+967-777-654-321',
                'is_featured' => true,
                'is_urgent' => false,
                'category_name' => 'العقارات',
            ],
            [
                'title_ar' => 'أرض للبيع في تعز - موقع استثماري',
                'title_en' => 'Land for Sale in Taiz - Investment Location',
                'description_ar' => 'أرض للبيع في تعز، مساحة 500 متر مربع، موقع تجاري ممتاز على شارع رئيسي. مناسبة للاستثمار التجاري أو السكني. صك ملكية واضح، جميع الأوراق جاهزة.',
                'description_en' => 'Land for sale in Taiz, 500 sqm area, excellent commercial location on main street. Suitable for commercial or residential investment. Clear ownership deed, all papers ready.',
                'price' => 25000000,
                'currency' => 'YER',
                'price_type' => 'fixed',
                'location' => 'تعز - شارع جمال',
                'city' => 'تعز',
                'country' => 'Yemen',
                'phone' => '+967-4-123-456',
                'email' => '<EMAIL>',
                'whatsapp' => '+967-733-111-222',
                'is_featured' => false,
                'is_urgent' => true,
                'category_name' => 'العقارات',
            ],
        ];

        // إعلانات الإلكترونيات
        $electronicsAds = [
            [
                'title_ar' => 'آيفون 14 برو ماكس - جديد بالكرتون',
                'title_en' => 'iPhone 14 Pro Max - Brand New in Box',
                'description_ar' => 'آيفون 14 برو ماكس، 256 جيجا، لون أزرق، جديد لم يستخدم، بالكرتون الأصلي مع جميع الملحقات. ضمان أبل لمدة سنة. السعر نهائي غير قابل للتفاوض.',
                'description_en' => 'iPhone 14 Pro Max, 256GB, blue color, brand new unused, original box with all accessories. Apple warranty for one year. Final price, non-negotiable.',
                'price' => 4500,
                'currency' => 'SAR',
                'price_type' => 'fixed',
                'location' => 'الرياض - حي الورود',
                'city' => 'الرياض',
                'country' => 'Saudi Arabia',
                'phone' => '+966-56-123-9999',
                'email' => '<EMAIL>',
                'whatsapp' => '+966-56-123-9999',
                'is_featured' => true,
                'is_urgent' => true,
                'category_name' => 'الإلكترونيات والأجهزة',
            ],
            [
                'title_ar' => 'لابتوب ديل XPS 13 - للمبرمجين',
                'title_en' => 'Dell XPS 13 Laptop - For Programmers',
                'description_ar' => 'لابتوب ديل XPS 13، معالج Intel i7 الجيل 11، رام 16 جيجا، SSD 512 جيجا، شاشة 4K، كرت شاشة مدمج. مثالي للبرمجة والتصميم. استخدام خفيف لمدة 6 أشهر فقط.',
                'description_en' => 'Dell XPS 13 laptop, Intel i7 11th gen processor, 16GB RAM, 512GB SSD, 4K display, integrated graphics. Perfect for programming and design. Light usage for only 6 months.',
                'price' => 3200,
                'currency' => 'SAR',
                'price_type' => 'negotiable',
                'location' => 'جدة - حي الزهراء',
                'city' => 'جدة',
                'country' => 'Saudi Arabia',
                'phone' => '+966-54-456-7890',
                'email' => '<EMAIL>',
                'whatsapp' => '+966-54-456-7890',
                'is_featured' => false,
                'is_urgent' => false,
                'category_name' => 'الإلكترونيات والأجهزة',
            ],
            [
                'title_ar' => 'سامسونج جالاكسي S23 - صنعاء',
                'title_en' => 'Samsung Galaxy S23 - Sanaa',
                'description_ar' => 'سامسونج جالاكسي S23، 128 جيجا، لون أسود، حالة ممتازة، استعمال 3 أشهر فقط. مع الشاحن الأصلي وجراب حماية. بطارية ممتازة، كاميرا عالية الجودة.',
                'description_en' => 'Samsung Galaxy S23, 128GB, black color, excellent condition, only 3 months usage. With original charger and protective case. Excellent battery, high-quality camera.',
                'price' => 1800000,
                'currency' => 'YER',
                'price_type' => 'negotiable',
                'location' => 'صنعاء - شارع الستين',
                'city' => 'صنعاء',
                'country' => 'Yemen',
                'phone' => '+967-1-789-012',
                'email' => '<EMAIL>',
                'whatsapp' => '+967-777-888-999',
                'is_featured' => true,
                'is_urgent' => false,
                'category_name' => 'الإلكترونيات والأجهزة',
            ],
            [
                'title_ar' => 'لابتوب HP للطلاب - عدن',
                'title_en' => 'HP Laptop for Students - Aden',
                'description_ar' => 'لابتوب HP مناسب للطلاب والاستخدام العادي، معالج i5، رام 8 جيجا، هارد 1 تيرا، شاشة 15.6 بوصة. حالة جيدة، سعر مناسب للطلاب.',
                'description_en' => 'HP laptop suitable for students and general use, i5 processor, 8GB RAM, 1TB HDD, 15.6 inch screen. Good condition, student-friendly price.',
                'price' => 800000,
                'currency' => 'YER',
                'price_type' => 'fixed',
                'location' => 'عدن - المعلا',
                'city' => 'عدن',
                'country' => 'Yemen',
                'phone' => '+967-2-567-890',
                'email' => '<EMAIL>',
                'whatsapp' => '+967-733-444-555',
                'is_featured' => false,
                'is_urgent' => true,
                'category_name' => 'الإلكترونيات والأجهزة',
            ],
        ];

        // إعلانات الوظائف - السوق اليمني والعربي
        $jobAds = [
            [
                'title_ar' => 'مطلوب مبرمج ويب - صنعاء',
                'title_en' => 'Web Developer Needed - Sanaa',
                'description_ar' => 'مطلوب مبرمج ويب خبرة في PHP وLaravel وMySQL. العمل في شركة تقنية ناشئة في صنعاء. راتب مجزي، بيئة عمل مريحة، فرص تطوير مهني. يفضل خريجي كليات الحاسوب.',
                'description_en' => 'Web developer needed with experience in PHP, Laravel, and MySQL. Work at a startup tech company in Sanaa. Good salary, comfortable work environment, professional development opportunities. Computer science graduates preferred.',
                'price' => 0,
                'currency' => 'YER',
                'price_type' => 'on_request',
                'location' => 'صنعاء - شارع الزبيري',
                'city' => 'صنعاء',
                'country' => 'Yemen',
                'phone' => '+967-1-111-222',
                'email' => '<EMAIL>',
                'whatsapp' => '+***********-444',
                'is_featured' => true,
                'is_urgent' => true,
                'category_name' => 'الوظائف والتوظيف',
            ],
            [
                'title_ar' => 'مدرس رياضيات خصوصي - عدن',
                'title_en' => 'Private Math Tutor - Aden',
                'description_ar' => 'مدرس رياضيات خبرة 10 سنوات، تدريس جميع المراحل الدراسية، نتائج ممتازة مع الطلاب. دروس خصوصية في المنزل أو عبر الإنترنت. أسعار مناسبة، مواعيد مرنة.',
                'description_en' => 'Math teacher with 10 years experience, teaching all academic levels, excellent results with students. Private lessons at home or online. Reasonable prices, flexible schedules.',
                'price' => 15000,
                'currency' => 'YER',
                'price_type' => 'fixed',
                'location' => 'عدن - كريتر',
                'city' => 'عدن',
                'country' => 'Yemen',
                'phone' => '+967-2-333-444',
                'email' => '<EMAIL>',
                'whatsapp' => '+967-733-666-777',
                'is_featured' => false,
                'is_urgent' => false,
                'category_name' => 'الوظائف والتوظيف',
            ],
        ];

        // إعلانات الخدمات - السوق اليمني والعربي
        $serviceAds = [
            [
                'title_ar' => 'خدمات صيانة السيارات - صنعاء',
                'title_en' => 'Car Maintenance Services - Sanaa',
                'description_ar' => 'ورشة صيانة سيارات متخصصة في جميع أنواع السيارات. خدمات: تغيير زيت، فحص شامل، إصلاح المحرك، صيانة التكييف. فنيين مهرة، قطع غيار أصلية، أسعار منافسة.',
                'description_en' => 'Car maintenance workshop specialized in all car types. Services: oil change, comprehensive inspection, engine repair, AC maintenance. Skilled technicians, original spare parts, competitive prices.',
                'price' => 0,
                'currency' => 'YER',
                'price_type' => 'on_request',
                'location' => 'صنعاء - شارع الثورة',
                'city' => 'صنعاء',
                'country' => 'Yemen',
                'phone' => '+967-1-555-666',
                'email' => '<EMAIL>',
                'whatsapp' => '+967-777-999-000',
                'is_featured' => true,
                'is_urgent' => false,
                'category_name' => 'الخدمات',
            ],
            [
                'title_ar' => 'خدمات تنظيف المنازل - عدن',
                'title_en' => 'House Cleaning Services - Aden',
                'description_ar' => 'شركة تنظيف منازل محترفة، فريق مدرب ومجهز بأحدث المعدات. خدمات: تنظيف شامل، تنظيف السجاد، تنظيف النوافذ، تعقيم. أسعار مناسبة، خدمة سريعة وموثوقة.',
                'description_en' => 'Professional house cleaning company, trained team equipped with latest equipment. Services: comprehensive cleaning, carpet cleaning, window cleaning, sanitization. Reasonable prices, fast and reliable service.',
                'price' => 50000,
                'currency' => 'YER',
                'price_type' => 'fixed',
                'location' => 'عدن - المعلا',
                'city' => 'عدن',
                'country' => 'Yemen',
                'phone' => '+967-2-777-888',
                'email' => '<EMAIL>',
                'whatsapp' => '+***********-333',
                'is_featured' => false,
                'is_urgent' => false,
                'category_name' => 'الخدمات',
            ],
        ];

        $allAds = array_merge($carAds, $realEstateAds, $electronicsAds, $jobAds, $serviceAds);
        $createdCount = 0;

        foreach ($allAds as $adData) {
            $user = $users->random();
            $category = $categories->where('name_ar', $adData['category_name'])->first();

            if (!$category) {
                continue;
            }

            $sanitizedData = [
                'user_id' => $user->id,
                'category_id' => $category->id,
                'title_ar' => InputSanitizationService::sanitizeText($adData['title_ar']),
                'title_en' => InputSanitizationService::sanitizeText($adData['title_en']),
                'description_ar' => InputSanitizationService::sanitizeRichText($adData['description_ar']),
                'description_en' => InputSanitizationService::sanitizeRichText($adData['description_en']),
                'slug' => Str::slug($adData['title_en']) . '-' . time() . '-' . rand(1000, 9999),

                // الصور (JSON array) - null لاستخدام أيقونات التصنيف الافتراضية
                'images' => null,
                'main_image' => null,

                // معلومات السعر
                'price' => $adData['price'],
                'currency' => $adData['currency'],
                'price_type' => $adData['price_type'] ?? 'fixed',
                'is_negotiable' => ($adData['price_type'] ?? 'fixed') === 'negotiable',

                // معلومات التواصل (مشفرة)
                'contact_phone' => EncryptionService::encryptText($adData['phone']),
                'contact_email' => EncryptionService::encryptText($adData['email']),
                'contact_whatsapp' => EncryptionService::encryptText($adData['whatsapp']),

                // الموقع
                'location' => InputSanitizationService::sanitizeText($adData['location']),
                'city' => $adData['city'],
                'country' => $adData['country'],

                // الحالة والإعدادات
                'status' => 'active',
                'is_featured' => $adData['is_featured'],
                'is_urgent' => $adData['is_urgent'],
                'expires_at' => now()->addDays(30),

                // الإحصائيات (محسوبة)
                'views_count' => rand(50, 300),
                'contact_reveals_count' => rand(5, 25),
                'favorites_count' => rand(2, 15),
                'rating_average' => rand(35, 50) / 10, // 3.5 to 5.0
                'rating_count' => rand(3, 12),

                'created_at' => now()->subDays(rand(1, 15)),
                'updated_at' => now(),
            ];

            Ad::create($sanitizedData);
            $createdCount++;
        }

        $this->command->info('✅ تم إنشاء ' . $createdCount . ' إعلان تجريبي واقعي');
    }
    /**
     * إنشاء إعلانات بأسعار متقدمة ومتنوعة
     */
    private function createAdvancedPricingTestAds(): void
    {
        $this->command->info('💰 إنشاء إعلانات الأسعار المتقدمة...');

        $users = User::where('is_admin', false)->get();
        $categories = Category::all();

        if ($users->isEmpty() || $categories->isEmpty()) {
            $this->command->warn('⚠️ لا توجد مستخدمين أو تصنيفات لإنشاء الإعلانات');
            return;
        }

        // إعلانات مجانية
        $freeAds = [
            [
                'title_ar' => 'دروس تعليم القرآن الكريم - مجاناً',
                'title_en' => 'Free Quran Teaching Lessons',
                'description_ar' => 'أقدم دروس تعليم القرآن الكريم والتجويد مجاناً للأطفال والكبار. خبرة 10 سنوات في التدريس. الدروس عبر الإنترنت أو في المنزل. أوقات مرنة تناسب الجميع.',
                'description_en' => 'I offer free Quran and Tajweed lessons for children and adults. 10 years teaching experience. Online or home lessons. Flexible times for everyone.',
                'price' => 0,
                'currency' => 'SAR',
                'price_type' => 'free',
                'category_name' => 'الخدمات',
            ],
            [
                'title_ar' => 'كتب جامعية مستعملة - مجاناً',
                'title_en' => 'Used University Books - Free',
                'description_ar' => 'مجموعة كتب جامعية مستعملة لتخصص إدارة الأعمال، حالة جيدة. أريد التخلص منها لإفساح المجال. مناسبة للطلاب الجدد. يمكن الاستلام من المنزل.',
                'description_en' => 'Collection of used university books for business administration major, good condition. Want to get rid of them to make space. Suitable for new students. Can be picked up from home.',
                'price' => 0,
                'currency' => 'YER',
                'price_type' => 'free',
                'category_name' => 'الخدمات',
            ],
            [
                'title_ar' => 'مساعدة في الأعمال المنزلية - مجاناً',
                'title_en' => 'Help with Household Chores - Free',
                'description_ar' => 'أقدم مساعدة مجانية للعائلات المحتاجة في الأعمال المنزلية والتنظيف. خدمة تطوعية من أجل المجتمع. متاحة في عطلة نهاية الأسبوع.',
                'description_en' => 'I offer free help to needy families with household chores and cleaning. Volunteer service for the community. Available on weekends.',
                'price' => 0,
                'currency' => 'YER',
                'price_type' => 'free',
                'category_name' => 'الخدمات',
            ],
        ];

        // إعلانات بعملات مختلفة
        $multiCurrencyAds = [
            [
                'title_ar' => 'ساعة رولكس أصلية - للبيع',
                'title_en' => 'Original Rolex Watch - For Sale',
                'description_ar' => 'ساعة رولكس سابمارينر أصلية، موديل 2018، حالة ممتازة، مع الضمان والأوراق الأصلية. استخدام شخصي خفيف. السعر بالدولار الأمريكي نهائي.',
                'description_en' => 'Original Rolex Submariner, 2018 model, excellent condition, with warranty and original papers. Light personal use. Price in USD is final.',
                'price' => 8500,
                'currency' => 'USD',
                'price_type' => 'fixed',
                'category_name' => 'الأزياء والموضة',
            ],
            [
                'title_ar' => 'خدمات ترجمة احترافية',
                'title_en' => 'Professional Translation Services',
                'description_ar' => 'أقدم خدمات ترجمة احترافية من العربية إلى الإنجليزية والعكس. متخصص في الترجمة القانونية والطبية والتقنية. خبرة 8 سنوات. تسليم سريع وجودة عالية.',
                'description_en' => 'I provide professional translation services from Arabic to English and vice versa. Specialized in legal, medical, and technical translation. 8 years experience. Fast delivery and high quality.',
                'price' => 25,
                'currency' => 'AED',
                'price_type' => 'fixed',
                'category_name' => 'الخدمات',
            ],
            [
                'title_ar' => 'دورة تعليم البرمجة - أونلاين',
                'title_en' => 'Programming Course - Online',
                'description_ar' => 'دورة شاملة لتعليم البرمجة للمبتدئين، تشمل HTML, CSS, JavaScript, PHP. مدة الدورة 3 أشهر، شهادة معتمدة، دعم فني مستمر. خصم 20% للتسجيل المبكر.',
                'description_en' => 'Comprehensive programming course for beginners, includes HTML, CSS, JavaScript, PHP. 3-month duration, certified, continuous technical support. 20% discount for early registration.',
                'price' => 450,
                'currency' => 'YER',
                'price_type' => 'negotiable',
                'category_name' => 'الخدمات',
            ],
        ];

        // إعلانات قابلة للتفاوض
        $negotiableAds = [
            [
                'title_ar' => 'طقم أثاث صالة كامل - حالة ممتازة',
                'title_en' => 'Complete Living Room Furniture Set - Excellent Condition',
                'description_ar' => 'طقم أثاث صالة كامل يشمل: كنبة 7 مقاعد، طاولة وسط، طاولات جانبية، مكتبة، تلفزيون 55 بوصة. حالة ممتازة، استخدام خفيف لمدة سنتين. السعر قابل للتفاوض للجادين.',
                'description_en' => 'Complete living room furniture set includes: 7-seat sofa, center table, side tables, bookshelf, 55-inch TV. Excellent condition, light use for two years. Price negotiable for serious buyers.',
                'price' => 12000,
                'currency' => 'SAR',
                'price_type' => 'negotiable',
                'category_name' => 'الأثاث والمنزل',
            ],
            [
                'title_ar' => 'دراجة هوائية جبلية - للبيع',
                'title_en' => 'Mountain Bike - For Sale',
                'description_ar' => 'دراجة هوائية جبلية ماركة Trek، 21 سرعة، إطارات جديدة، فرامل ديسك، حالة جيدة جداً. مناسبة للرياضة والتنزه. السعر قابل للتفاوض البسيط.',
                'description_en' => 'Trek mountain bike, 21 speeds, new tires, disc brakes, very good condition. Suitable for sports and recreation. Price slightly negotiable.',
                'price' => 800,
                'currency' => 'SAR',
                'price_type' => 'negotiable',
                'category_name' => 'الرياضة واللياقة',
            ],
            [
                'title_ar' => 'ثوب يمني تقليدي أصيل - صنعاء',
                'title_en' => 'Authentic Traditional Yemeni Thobe - Sanaa',
                'description_ar' => 'ثوب يمني تقليدي أصيل، تطريز يدوي فاخر، قماش عالي الجودة، مناسب للمناسبات والأعراس. صناعة محلية يمنية أصيلة. متوفر بألوان مختلفة.',
                'description_en' => 'Authentic traditional Yemeni thobe, luxury hand embroidery, high-quality fabric, suitable for occasions and weddings. Authentic local Yemeni craftsmanship. Available in different colors.',
                'price' => 350000,
                'currency' => 'YER',
                'price_type' => 'negotiable',
                'category_name' => 'الأزياء والموضة',
            ],
            [
                'title_ar' => 'طقم أثاث غرفة نوم - تعز',
                'title_en' => 'Bedroom Furniture Set - Taiz',
                'description_ar' => 'طقم أثاث غرفة نوم كامل: سرير مزدوج، دولاب 6 أبواب، تسريحة مع مرآة، كومودينو. خشب طبيعي، تصميم كلاسيكي، حالة ممتازة. السعر شامل التوصيل.',
                'description_en' => 'Complete bedroom furniture set: double bed, 6-door wardrobe, dresser with mirror, nightstand. Natural wood, classic design, excellent condition. Price includes delivery.',
                'price' => 8500000,
                'currency' => 'YER',
                'price_type' => 'negotiable',
                'category_name' => 'الأثاث والمنزل',
            ],
        ];

        // إعلانات بناءً على الطلب
        $onRequestAds = [
            [
                'title_ar' => 'تصميم مواقع إلكترونية احترافية',
                'title_en' => 'Professional Website Design',
                'description_ar' => 'أقدم خدمات تصميم وتطوير المواقع الإلكترونية الاحترافية. تصميم متجاوب، سرعة عالية، SEO محسن. أسعار تنافسية حسب المشروع. استشارة مجانية.',
                'description_en' => 'I provide professional website design and development services. Responsive design, high speed, SEO optimized. Competitive prices per project. Free consultation.',
                'price' => null,
                'currency' => 'SAR',
                'price_type' => 'on_request',
                'category_name' => 'الخدمات',
            ],
            [
                'title_ar' => 'خدمات التصوير الفوتوغرافي',
                'title_en' => 'Photography Services',
                'description_ar' => 'مصور فوتوغرافي محترف، متخصص في تصوير الأفراح والمناسبات والبورتريه. معدات احترافية، جودة عالية، تسليم سريع. الأسعار تختلف حسب نوع المناسبة ومدة التصوير.',
                'description_en' => 'Professional photographer, specialized in weddings, events, and portraits. Professional equipment, high quality, fast delivery. Prices vary according to event type and shooting duration.',
                'price' => null,
                'currency' => 'SAR',
                'price_type' => 'on_request',
                'category_name' => 'الخدمات',
            ],
        ];

        $allAdvancedAds = array_merge($freeAds, $multiCurrencyAds, $negotiableAds, $onRequestAds);
        $createdCount = 0;

        foreach ($allAdvancedAds as $adData) {
            $user = $users->random();
            $category = $categories->where('name_ar', $adData['category_name'])->first();

            if (!$category) {
                continue;
            }

            $sanitizedData = [
                'user_id' => $user->id,
                'category_id' => $category->id,
                'title_ar' => InputSanitizationService::sanitizeText($adData['title_ar']),
                'title_en' => InputSanitizationService::sanitizeText($adData['title_en']),
                'description_ar' => InputSanitizationService::sanitizeRichText($adData['description_ar']),
                'description_en' => InputSanitizationService::sanitizeRichText($adData['description_en']),
                'slug' => Str::slug($adData['title_en']) . '-' . time() . '-' . rand(1000, 9999),

                // الصور - null لاستخدام أيقونات التصنيف الافتراضية
                'images' => null,
                'main_image' => null,

                // معلومات السعر المتقدمة
                'price' => $adData['price'],
                'currency' => $adData['currency'],
                'price_type' => $adData['price_type'],
                'is_negotiable' => $adData['price_type'] === 'negotiable',
                'price_notes' => $adData['price_type'] === 'free' ? 'مجاني تماماً' :
                               ($adData['price_type'] === 'on_request' ? 'يرجى الاتصال للاستفسار عن السعر' : null),

                // معلومات التواصل
                'contact_phone' => EncryptionService::encryptText('+966-5' . rand(0, 9) . '-' . rand(100, 999) . '-' . rand(1000, 9999)),
                'contact_email' => EncryptionService::encryptText('user' . rand(1, 100) . '@example.com'),
                'contact_whatsapp' => EncryptionService::encryptText('+966-5' . rand(0, 9) . '-' . rand(100, 999) . '-' . rand(1000, 9999)),

                // الموقع
                'location' => ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة'][rand(0, 4)],
                'city' => ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة'][rand(0, 4)],
                'country' => 'Saudi Arabia',

                // الحالة والإعدادات
                'status' => 'active',
                'is_featured' => rand(0, 3) == 0, // 25% مميز
                'is_urgent' => rand(0, 4) == 0,   // 20% عاجل
                'expires_at' => now()->addDays(rand(15, 45)),

                // الإحصائيات
                'views_count' => rand(20, 150),
                'contact_reveals_count' => rand(2, 20),
                'favorites_count' => rand(1, 10),
                'rating_average' => rand(30, 50) / 10,
                'rating_count' => rand(1, 8),

                'created_at' => now()->subDays(rand(1, 20)),
                'updated_at' => now(),
            ];

            Ad::create($sanitizedData);
            $createdCount++;
        }

        $this->command->info('✅ تم إنشاء ' . $createdCount . ' إعلان بأسعار متقدمة');
    }
    /**
     * إنشاء التفاعلات التجريبية الواقعية
     */
    private function createTestInteractions(): void
    {
        $this->command->info('❤️ إنشاء التفاعلات التجريبية الواقعية...');

        $users = User::where('is_admin', false)->get();
        $ads = Ad::all();

        if ($users->isEmpty() || $ads->isEmpty()) {
            $this->command->warn('⚠️ لا توجد مستخدمين أو إعلانات لإنشاء التفاعلات');
            return;
        }

        $createdCount = 0;

        // إنشاء مشاهدات واقعية (مسموحة بالتكرار)
        foreach ($ads as $ad) {
            $viewsCount = rand(50, 200);
            for ($i = 0; $i < $viewsCount; $i++) {
                $user = $users->random();

                // المشاهدات مسموحة بالتكرار (نفس المستخدم يمكن أن يشاهد الإعلان عدة مرات)
                Interaction::create([
                    'user_id' => $user->id,
                    'ad_id' => $ad->id,
                    'type' => 'view',
                    'data' => json_encode(['timestamp' => now()->timestamp]),
                    'ip_address' => $this->generateRandomIP(),
                    'created_at' => now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59)),
                    'updated_at' => now(),
                ]);
                $createdCount++;
            }
        }

        // إنشاء مفضلة واقعية
        foreach ($users as $user) {
            $favoriteAds = $ads->random(rand(3, 12));
            foreach ($favoriteAds as $ad) {
                $existingFavorite = Interaction::where([
                    'user_id' => $user->id,
                    'ad_id' => $ad->id,
                    'type' => 'favorite'
                ])->first();

                if (!$existingFavorite) {
                    Interaction::create([
                        'user_id' => $user->id,
                        'ad_id' => $ad->id,
                        'type' => 'favorite',
                        'data' => json_encode(['added_at' => now()->timestamp]),
                        'created_at' => now()->subDays(rand(0, 20)),
                        'updated_at' => now(),
                    ]);
                    $createdCount++;
                }
            }
        }

        // إنشاء تقييمات واقعية مع تعليقات
        $ratingComments = [
            5 => [
                'ممتاز جداً، أنصح بالتعامل معه',
                'خدمة رائعة وسرعة في التجاوب',
                'منتج كما هو موصوف تماماً',
                'تعامل راقي ومهني',
                'جودة عالية وسعر مناسب'
            ],
            4 => [
                'جيد جداً، تجربة إيجابية',
                'منتج جيد مع بعض الملاحظات البسيطة',
                'خدمة جيدة وتسليم في الوقت',
                'راضي عن التعامل بشكل عام',
                'جودة مقبولة للسعر'
            ],
            3 => [
                'تجربة عادية، لا بأس بها',
                'منتج مقبول لكن يحتاج تحسين',
                'خدمة متوسطة',
                'السعر مناسب للجودة',
                'تعامل عادي'
            ],
            2 => [
                'تجربة أقل من المتوقع',
                'المنتج لا يطابق الوصف تماماً',
                'تأخير في التسليم',
                'يحتاج تحسين في الخدمة',
                'جودة أقل من المتوقع'
            ],
            1 => [
                'تجربة سيئة، لا أنصح',
                'منتج مختلف عن الوصف',
                'خدمة عملاء ضعيفة',
                'تأخير كبير في التسليم',
                'جودة رديئة'
            ]
        ];

        foreach ($ads as $ad) {
            $ratingsCount = rand(2, 15);
            for ($i = 0; $i < $ratingsCount; $i++) {
                $user = $users->random();
                $rating = rand(1, 5);

                // تجنب التقييمات المكررة
                $existingRating = Interaction::where([
                    'user_id' => $user->id,
                    'ad_id' => $ad->id,
                    'type' => 'rating'
                ])->first();

                if (!$existingRating) {
                    $comments = $ratingComments[$rating];
                    $comment = $comments[array_rand($comments)];

                    Interaction::create([
                        'user_id' => $user->id,
                        'ad_id' => $ad->id,
                        'type' => 'rating',
                        'data' => json_encode([
                            'rating' => $rating,
                            'comment' => $comment
                        ]),
                        'created_at' => now()->subDays(rand(0, 25)),
                        'updated_at' => now(),
                    ]);
                    $createdCount++;
                }
            }
        }

        // إنشاء تعليقات واقعية (مسموح بالتكرار)
        $generalComments = [
            'هل المنتج لا يزال متاحاً؟',
            'ما هي طريقة الدفع المتاحة؟',
            'هل يمكن التفاوض في السعر؟',
            'متى يمكن المعاينة؟',
            'هل التوصيل متاح؟',
            'ما هي حالة المنتج بالضبط؟',
            'هل يوجد ضمان؟',
            'كم المدة المطلوبة للتسليم؟',
            'هل يمكن الدفع بالتقسيط؟',
            'أريد المزيد من التفاصيل',
            'هل السعر نهائي؟',
            'متى تم الشراء؟',
            'هل يوجد عيوب؟',
            'أين يمكن المعاينة؟',
            'هل يمكن الاستبدال؟'
        ];

        foreach ($ads as $ad) {
            $commentsCount = rand(1, 8);
            for ($i = 0; $i < $commentsCount; $i++) {
                $user = $users->random();
                $comment = $generalComments[array_rand($generalComments)];

                // التعليقات مسموحة بالتكرار، لذا لا نحتاج للتحقق من التكرار
                Interaction::create([
                    'user_id' => $user->id,
                    'ad_id' => $ad->id,
                    'type' => 'comment',
                    'data' => json_encode(['comment' => $comment]),
                    'created_at' => now()->subDays(rand(0, 15))->subMinutes(rand(0, 1440)),
                    'updated_at' => now(),
                ]);
                $createdCount++;
            }
        }

        // إنشاء كشف معلومات التواصل
        foreach ($ads as $ad) {
            $revealsCount = rand(5, 30);
            for ($i = 0; $i < $revealsCount; $i++) {
                $user = $users->random();

                // تجنب الكشف المكرر من نفس المستخدم
                $existingReveal = Interaction::where([
                    'user_id' => $user->id,
                    'ad_id' => $ad->id,
                    'type' => 'contact_reveal'
                ])->first();

                if (!$existingReveal) {
                    Interaction::create([
                        'user_id' => $user->id,
                        'ad_id' => $ad->id,
                        'type' => 'contact_reveal',
                        'data' => json_encode(['revealed_at' => now()->timestamp]),
                        'ip_address' => $this->generateRandomIP(),
                        'created_at' => now()->subDays(rand(0, 20)),
                        'updated_at' => now(),
                    ]);
                    $createdCount++;
                }
            }
        }

        $this->command->info('✅ تم إنشاء ' . $createdCount . ' تفاعل تجريبي واقعي');
    }
    /**
     * إنشاء سجلات النظام التجريبية الواقعية
     */
    private function createTestSystemLogs(): void
    {
        $this->command->info('📋 إنشاء سجلات النظام التجريبية...');

        $users = User::all();
        $createdCount = 0;

        // سجلات أمنية واقعية
        foreach ($users as $user) {
            // سجلات تسجيل الدخول
            for ($i = 0; $i < rand(5, 20); $i++) {
                SystemLog::create([
                    'type' => 'security',
                    'action' => 'login',
                    'description' => 'تسجيل دخول ناجح من IP: ' . $this->generateRandomIP(),
                    'user_id' => $user->id,
                    'severity' => 'low',
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => $this->generateRandomUserAgent(),
                    'created_at' => now()->subDays(rand(0, 30))->subHours(rand(0, 23)),
                    'updated_at' => now(),
                ]);
                $createdCount++;
            }

            // سجلات تسجيل الخروج
            for ($i = 0; $i < rand(3, 15); $i++) {
                SystemLog::create([
                    'type' => 'security',
                    'action' => 'logout',
                    'description' => 'تسجيل خروج من النظام',
                    'user_id' => $user->id,
                    'severity' => 'low',
                    'ip_address' => $this->generateRandomIP(),
                    'created_at' => now()->subDays(rand(0, 25))->subHours(rand(0, 23)),
                    'updated_at' => now(),
                ]);
                $createdCount++;
            }

            // محاولات تسجيل دخول فاشلة (أحياناً)
            if (rand(0, 3) == 0) {
                SystemLog::create([
                    'type' => 'security',
                    'action' => 'failed_login',
                    'description' => 'محاولة تسجيل دخول فاشلة - كلمة مرور خاطئة',
                    'user_id' => $user->id,
                    'severity' => 'medium',
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => $this->generateRandomUserAgent(),
                    'created_at' => now()->subDays(rand(0, 20)),
                    'updated_at' => now(),
                ]);
                $createdCount++;
            }

            // تغيير كلمة المرور
            if (rand(0, 5) == 0) {
                SystemLog::create([
                    'type' => 'security',
                    'action' => 'password_change',
                    'description' => 'تم تغيير كلمة المرور بنجاح',
                    'user_id' => $user->id,
                    'severity' => 'medium',
                    'ip_address' => $this->generateRandomIP(),
                    'created_at' => now()->subDays(rand(0, 15)),
                    'updated_at' => now(),
                ]);
                $createdCount++;
            }
        }

        // سجلات البحث الواقعية
        $searchTerms = [
            'سيارات للبيع', 'شقق للإيجار', 'لابتوب مستعمل', 'وظائف الرياض', 'خدمات تنظيف',
            'أثاث منزلي', 'هواتف ذكية', 'دورات تدريبية', 'مطاعم جدة', 'عقارات الدمام',
            'سيارات تويوتا', 'شقق صغيرة', 'آيفون 14', 'وظائف محاسبة', 'صيانة مكيفات',
            'كنب مستعمل', 'سامسونج جالاكسي', 'تعليم قيادة', 'مقاهي الرياض', 'فيلات للبيع',
            'دراجات هوائية', 'شقق مفروشة', 'لابتوب ديل', 'وظائف تقنية', 'خدمات توصيل',
            'طاولات طعام', 'هواوي', 'دورات إنجليزي', 'استراحات', 'أراضي للبيع'
        ];

        foreach ($users as $user) {
            for ($i = 0; $i < rand(10, 50); $i++) {
                $term = $searchTerms[array_rand($searchTerms)];
                $resultsCount = rand(0, 100);

                SystemLog::create([
                    'type' => 'search',
                    'action' => 'search_query',
                    'description' => "بحث عن: {$term} - النتائج: {$resultsCount}",
                    'user_id' => $user->id,
                    'severity' => 'low',
                    'data' => json_encode([
                        'search_term' => $term,
                        'results_count' => $resultsCount,
                        'search_filters' => rand(0, 1) ? ['category' => 'السيارات'] : null,
                    ]),
                    'ip_address' => $this->generateRandomIP(),
                    'created_at' => now()->subDays(rand(0, 30))->subHours(rand(0, 23)),
                    'updated_at' => now(),
                ]);
                $createdCount++;
            }
        }

        // سجلات إدارية
        $adminUser = User::where('is_admin', true)->first();
        if ($adminUser) {
            $adminActions = [
                ['action' => 'user_management', 'description' => 'تم تفعيل حساب مستخدم جديد'],
                ['action' => 'content_moderation', 'description' => 'تم مراجعة وقبول إعلان جديد'],
                ['action' => 'system_settings', 'description' => 'تم تحديث إعدادات النظام'],
                ['action' => 'user_management', 'description' => 'تم حظر مستخدم مخالف'],
                ['action' => 'content_moderation', 'description' => 'تم حذف إعلان مخالف'],
                ['action' => 'system_backup', 'description' => 'تم إنشاء نسخة احتياطية من قاعدة البيانات'],
                ['action' => 'security_review', 'description' => 'تم مراجعة سجلات الأمان'],
                ['action' => 'performance_check', 'description' => 'تم فحص أداء النظام'],
            ];

            foreach ($adminActions as $actionData) {
                for ($i = 0; $i < rand(1, 5); $i++) {
                    SystemLog::create([
                        'type' => 'admin_action',
                        'action' => $actionData['action'],
                        'description' => $actionData['description'],
                        'user_id' => $adminUser->id,
                        'severity' => ['low', 'medium', 'high'][rand(0, 2)],
                        'ip_address' => $this->generateRandomIP(),
                        'created_at' => now()->subDays(rand(0, 20)),
                        'updated_at' => now(),
                    ]);
                    $createdCount++;
                }
            }
        }

        // سجلات أخطاء واقعية
        $errorTypes = [
            ['action' => 'database_error', 'description' => 'خطأ مؤقت في الاتصال بقاعدة البيانات', 'severity' => 'high'],
            ['action' => 'file_upload_error', 'description' => 'فشل في رفع ملف صورة - حجم كبير', 'severity' => 'medium'],
            ['action' => 'email_error', 'description' => 'فشل في إرسال بريد إلكتروني', 'severity' => 'medium'],
            ['action' => 'payment_error', 'description' => 'خطأ في معالجة الدفع', 'severity' => 'high'],
            ['action' => 'validation_error', 'description' => 'خطأ في التحقق من البيانات', 'severity' => 'low'],
            ['action' => 'api_error', 'description' => 'خطأ في استدعاء API خارجي', 'severity' => 'medium'],
        ];

        foreach ($errorTypes as $errorData) {
            for ($i = 0; $i < rand(1, 3); $i++) {
                SystemLog::create([
                    'type' => 'error',
                    'action' => $errorData['action'],
                    'description' => $errorData['description'],
                    'severity' => $errorData['severity'],
                    'ip_address' => $this->generateRandomIP(),
                    'created_at' => now()->subDays(rand(0, 15)),
                    'updated_at' => now(),
                ]);
                $createdCount++;
            }
        }

        $this->command->info('✅ تم إنشاء ' . $createdCount . ' سجل نظام تجريبي');
    }

    /**
     * توليد IP عشوائي
     */
    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    /**
     * توليد User Agent عشوائي
     */
    private function generateRandomUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0',
        ];

        return $userAgents[array_rand($userAgents)];
    }

    /**
     * عرض إحصائيات البيانات المنشأة
     */
    private function displayDataStatistics(): void
    {
        $this->command->newLine();
        $this->command->info('📊 إحصائيات البيانات المنشأة:');

        $stats = [
            'المستخدمين' => User::count(),
            'التصنيفات' => Category::count(),
            'الإعلانات' => Ad::count(),
            'التفاعلات' => Interaction::count(),
            'السجلات' => SystemLog::count(),
            'الإعدادات' => Setting::count(),
        ];

        foreach ($stats as $type => $count) {
            $this->command->line("  📈 {$type}: {$count}");
        }

        $this->command->newLine();
        $this->command->info('🎯 النظام جاهز للاستخدام مع بيانات تجريبية شاملة!');
    }
}