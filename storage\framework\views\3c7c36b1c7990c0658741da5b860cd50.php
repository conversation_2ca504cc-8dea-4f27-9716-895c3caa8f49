<?php $__env->startSection('title', __('Home Page') . ' - ' . __('site_name')); ?>
<?php $__env->startSection('description', __('Comprehensive advertising platform for educational, healthcare and commercial institutions')); ?>

<?php $__env->startSection('content'); ?>
<!-- القسم الرئيسي -->
<section class="hero-section py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 70vh;">
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-6">
                <div class="hero-content text-white">
                    <h1 class="display-4 fw-bold arabic-text mb-4">
                        <?php echo e(__('welcome_to')); ?>

                        <span class="text-warning"><?php echo e(__('site_name')); ?></span>
                    </h1>
                    <p class="lead arabic-text mb-4">
                        <?php echo e(__('platform_description_full')); ?>

                    </p>

                    <!-- الإحصائيات السريعة -->
                    <div class="stats-row row g-3 mb-4">
                        <div class="col-4">
                            <div class="stat-item text-center">
                                <h3 class="fw-bold text-warning"><?php echo e($stats['total_categories'] ?? 6); ?></h3>
                                <small class="arabic-text"><?php echo e(__('categories_text')); ?></small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item text-center">
                                <h3 class="fw-bold text-warning"><?php echo e($stats['total_ads'] ?? 0); ?>+</h3>
                                <small class="arabic-text"><?php echo e(__('ads_text')); ?></small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item text-center">
                                <h3 class="fw-bold text-warning"><?php echo e($stats['total_feedbacks'] ?? 0); ?>+</h3>
                                <small class="arabic-text"><?php echo e(__('rating_text')); ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- الزر الرئيسي -->
                    <div class="hero-actions">
                        <a href="<?php echo e(route('action.choice')); ?>" class="btn btn-warning btn-lg px-5 py-3 arabic-text fw-bold">
                            <i class="fas fa-rocket me-2"></i>
                            <?php echo e(__('start_now')); ?>

                        </a>
                        <a href="<?php echo e(route('categories.index')); ?>" class="btn btn-outline-light btn-lg px-4 py-3 arabic-text ms-3">
                            <i class="fas fa-th-large me-2"></i>
                            <?php echo e(__('browse_categories')); ?>

                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="hero-image text-center">
                    <!-- صورة أو رسم توضيحي -->
                    <div class="illustration-placeholder">
                        <i class="fas fa-bullhorn fa-10x text-warning opacity-75"></i>
                        <div class="floating-elements">
                            <i class="fas fa-school floating-icon text-info"></i>
                            <i class="fas fa-hospital floating-icon text-danger"></i>
                            <i class="fas fa-university floating-icon text-success"></i>
                            <i class="fas fa-building floating-icon text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم التصنيفات السريعة -->
<section class="categories-preview py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="arabic-text fw-bold text-primary"><?php echo e(__('main_categories')); ?></h2>
            <p class="text-muted arabic-text"><?php echo e(__('choose_category')); ?></p>
        </div>

        <div class="row g-4">
            <!-- بطاقات التصنيفات -->
            <div class="col-lg-2 col-md-4 col-6">
                <a href="<?php echo e(route('ads.category', 'schools')); ?>" class="text-decoration-none">
                    <div class="category-card card-custom text-center p-4 h-100">
                        <i class="fas fa-school fa-3x text-info mb-3"></i>
                        <h6 class="arabic-text fw-bold"><?php echo e(__('schools')); ?></h6>
                        <small class="text-secondary-custom"><?php echo e(__('Government and private schools')); ?></small>
                    </div>
                </a>
            </div>

            <div class="col-lg-2 col-md-4 col-6">
                <a href="<?php echo e(route('ads.category', 'universities')); ?>" class="text-decoration-none">
                    <div class="category-card card-custom text-center p-4 h-100">
                        <i class="fas fa-university fa-3x text-success mb-3"></i>
                        <h6 class="arabic-text fw-bold"><?php echo e(__('universities')); ?></h6>
                        <small class="text-secondary-custom"><?php echo e(__('Universities and colleges')); ?></small>
                    </div>
                </a>
            </div>

            <div class="col-lg-2 col-md-4 col-6">
                <a href="<?php echo e(route('ads.category', 'institutes')); ?>" class="text-decoration-none">
                    <div class="category-card card-custom text-center p-4 h-100">
                        <i class="fas fa-graduation-cap fa-3x text-warning mb-3"></i>
                        <h6 class="arabic-text fw-bold"><?php echo e(__('institutes')); ?></h6>
                        <small class="text-secondary-custom"><?php echo e(__('Training institutes')); ?></small>
                    </div>
                </a>
            </div>

            <div class="col-lg-2 col-md-4 col-6">
                <a href="<?php echo e(route('ads.category', 'hospitals')); ?>" class="text-decoration-none">
                    <div class="category-card card-custom text-center p-4 h-100">
                        <i class="fas fa-hospital fa-3x text-danger mb-3"></i>
                        <h6 class="arabic-text fw-bold"><?php echo e(__('hospitals')); ?></h6>
                        <small class="text-secondary-custom"><?php echo e(__('Medical centers')); ?></small>
                    </div>
                </a>
            </div>

            <div class="col-lg-2 col-md-4 col-6">
                <a href="<?php echo e(route('ads.category', 'shops')); ?>" class="text-decoration-none">
                    <div class="category-card card-custom text-center p-4 h-100">
                        <i class="fas fa-store fa-3x text-purple mb-3"></i>
                        <h6 class="arabic-text fw-bold"><?php echo e(__('shops')); ?></h6>
                        <small class="text-secondary-custom"><?php echo e(__('Stores and shops')); ?></small>
                    </div>
                </a>
            </div>

            <div class="col-lg-2 col-md-4 col-6">
                <a href="<?php echo e(route('ads.category', 'companies')); ?>" class="text-decoration-none">
                    <div class="category-card card-custom text-center p-4 h-100">
                        <i class="fas fa-building fa-3x text-primary mb-3"></i>
                        <h6 class="arabic-text fw-bold"><?php echo e(__('companies')); ?></h6>
                        <small class="text-secondary-custom"><?php echo e(__('Companies and institutions')); ?></small>
                    </div>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- قسم المميزات -->
<section class="features-section py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="arabic-text fw-bold text-primary"><?php echo e(__('why_our_site')); ?></h2>
            <p class="text-muted arabic-text"><?php echo e(__('We provide the best experience')); ?></p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card card-custom p-4 text-center h-100">
                    <i class="fas fa-search fa-3x text-primary mb-3"></i>
                    <h5 class="arabic-text fw-bold"><?php echo e(__('easy_fast_search')); ?></h5>
                    <p class="text-muted arabic-text"><?php echo e(__('Search thousands of ads easily')); ?></p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card card-custom p-4 text-center h-100">
                    <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                    <h5 class="arabic-text fw-bold"><?php echo e(__('trusted_secure')); ?></h5>
                    <p class="text-muted arabic-text"><?php echo e(__('All ads are reviewed')); ?></p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card card-custom p-4 text-center h-100">
                    <i class="fas fa-mobile-alt fa-3x text-warning mb-3"></i>
                    <h5 class="arabic-text fw-bold"><?php echo e(__('mobile_compatible')); ?></h5>
                    <p class="text-muted arabic-text"><?php echo e(__('Use the site from any device')); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم الإعلانات المميزة -->
<?php if(isset($featuredAds) && $featuredAds->count() > 0): ?>
<section class="featured-ads-section py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="arabic-text fw-bold text-primary"><?php echo e(__('Featured Ads')); ?></h2>
            <p class="text-muted arabic-text"><?php echo e(__('Discover the best featured ads')); ?></p>
        </div>

        <div class="row">
            <?php $__currentLoopData = $featuredAds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('components.ad-card.index', [
                    'ad' => $ad,
                    'variant' => 'compact',
                    'showFavorites' => true,
                    'descriptionLength' => 80,
                    'showPricing' => true,
                    'showMeta' => false,
                    'showActions' => true,
                    'showExpiry' => false,
                    'customClass' => 'featured-ad-card'
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-4">
            <a href="<?php echo e(route('ads.index')); ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-eye me-2"></i>
                <?php echo e(__('View All Ads')); ?>

            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- قسم الإعلانات الشائعة -->
<?php if(isset($popularAds) && $popularAds->count() > 0): ?>
<section class="popular-ads-section py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="arabic-text fw-bold text-primary"><?php echo e(__('Popular Ads')); ?></h2>
            <p class="text-muted arabic-text"><?php echo e(__('Most viewed ads this week')); ?></p>
        </div>

        <div class="row">
            <?php $__currentLoopData = $popularAds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('components.ad-card.index', [
                    'ad' => $ad,
                    'variant' => 'compact',
                    'showFavorites' => true,
                    'descriptionLength' => 80,
                    'showPricing' => true,
                    'showMeta' => false,
                    'showActions' => true,
                    'showExpiry' => false,
                    'customClass' => 'popular-ad-card'
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-4">
            <a href="<?php echo e(route('ads.index', ['sort' => 'popular'])); ?>" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-fire me-2"></i>
                <?php echo e(__('View Popular Ads')); ?>

            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- قسم الدعوة للعمل -->
<section class="cta-section py-5 bg-primary text-white">
    <div class="container text-center">
        <h2 class="arabic-text fw-bold mb-3"><?php echo e(__('business_owner')); ?></h2>
        <p class="lead arabic-text mb-4"><?php echo e(__('join_us_publish')); ?></p>
        <a href="<?php echo e(route('auth.register')); ?>" class="btn btn-warning btn-lg px-5 py-3 arabic-text fw-bold">
            <i class="fas fa-plus-circle me-2"></i>
            <?php echo e(__('register_free')); ?>

        </a>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* تحسينات القسم الرئيسي */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* الإحصائيات */
.stat-item {
    padding: 1rem;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* العناصر المتحركة */
.floating-elements {
    position: relative;
}

.floating-icon {
    position: absolute;
    animation: float 3s ease-in-out infinite;
}

.floating-icon:nth-child(1) {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.floating-icon:nth-child(2) {
    top: 60%;
    right: 80%;
    animation-delay: 1s;
}

.floating-icon:nth-child(3) {
    top: 80%;
    right: 20%;
    animation-delay: 2s;
}

.floating-icon:nth-child(4) {
    top: 40%;
    right: 60%;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* بطاقات التصنيفات */
.category-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* بطاقات المميزات */
.feature-card {
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

/* ألوان إضافية */
.text-purple {
    color: #8b5cf6 !important;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
    }

    .display-4 {
        font-size: 2rem;
    }

    .hero-actions .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
    }

    .stats-row {
        margin-bottom: 2rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/home/<USER>/ ?>