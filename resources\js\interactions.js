/**
 * تفاعلات واجهة المستخدم المحسنة
 * ملف مبسط ومحسن للأداء - تم حل مشاكل التعقيد والإفراط في الاستخدام
 */

class UIInteractions {
    constructor() {
        this.observers = new Map();
        this.init();
    }

    /**
     * تهيئة جميع التفاعلات
     */
    init() {
        this.initScrollAnimations();
        this.initHoverEffects();
        this.initClickEffects();
        this.initFormEnhancements();
        this.initImageLazyLoading();
        this.initSmoothScrolling();
        this.initTooltips();
        this.initModals();
        this.initProgressBars();
    }

    /**
     * الرسوم المتحركة عند التمرير - محسنة
     */
    initScrollAnimations() {
        if (!('IntersectionObserver' in window)) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const animationType = element.dataset.animation || 'fadeInUp';

                    element.classList.add(`animate-${animationType}`);
                    observer.unobserve(element);
                }
            });
        }, { threshold: 0.1, rootMargin: '0px 0px -50px 0px' });

        document.querySelectorAll('[data-animation]').forEach(el => {
            observer.observe(el);
        });

        this.observers.set('scroll', observer);
    }

    /**
     * تأثيرات التحويم - محسنة بـ event delegation
     */
    initHoverEffects() {
        document.addEventListener('mouseenter', (e) => {
            if (e.target.matches('.card, .hover-lift')) {
                this.applyLiftEffect(e.target, true);
            }
            if (e.target.matches('.hover-zoom img')) {
                this.applyZoomEffect(e.target, true);
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.matches('.card, .hover-lift')) {
                this.applyLiftEffect(e.target, false);
            }
            if (e.target.matches('.hover-zoom img')) {
                this.applyZoomEffect(e.target, false);
            }
        }, true);
    }

    /**
     * تطبيق تأثير الرفع
     */
    applyLiftEffect(element, isHover) {
        Object.assign(element.style, {
            transform: isHover ? 'translateY(-5px)' : 'translateY(0)',
            boxShadow: isHover ? '0 10px 25px rgba(0, 0, 0, 0.15)' : '',
            transition: 'all 0.3s ease'
        });
    }

    /**
     * تطبيق تأثير التكبير
     */
    applyZoomEffect(element, isHover) {
        Object.assign(element.style, {
            transform: isHover ? 'scale(1.05)' : 'scale(1)',
            transition: 'transform 0.3s ease'
        });
    }

    /**
     * تأثيرات النقر - محسنة
     */
    initClickEffects() {
        document.addEventListener('click', (e) => {
            const button = e.target.closest('.btn, .btn-ripple');
            if (button && !this.shouldSkipRipple(button)) {
                this.createRippleEffect(e, button);
            }
        });
    }

    /**
     * فحص تخطي تأثير الموجة
     */
    shouldSkipRipple(element) {
        return element.closest('.nav-link') ||
               element.id === 'languageToggleBtn' ||
               element.id === 'themeToggleBtn' ||
               element.classList.contains('language-toggle-btn');
    }

    /**
     * إنشاء تأثير الموجة - محسن
     */
    createRippleEffect(event, element) {
        if (element.querySelector('.ripple-effect')) return;

        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        Object.assign(ripple.style, {
            width: `${size}px`,
            height: `${size}px`,
            left: `${x}px`,
            top: `${y}px`,
            position: 'absolute',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.6)',
            transform: 'scale(0)',
            animation: 'ripple 0.6s linear',
            pointerEvents: 'none'
        });

        ripple.classList.add('ripple-effect');

        Object.assign(element.style, {
            position: 'relative',
            overflow: 'hidden'
        });

        element.appendChild(ripple);
        setTimeout(() => ripple.remove(), 600);
    }

    /**
     * تحسينات النماذج - مبسطة
     */
    initFormEnhancements() {
        document.addEventListener('focus', (e) => {
            if (e.target.matches('.form-control')) {
                e.target.parentElement?.classList.add('focused');
            }
        }, true);

        document.addEventListener('blur', (e) => {
            if (e.target.matches('.form-control') && !e.target.value.trim()) {
                e.target.parentElement?.classList.remove('focused');
            }
        }, true);

        // تطبيق الحالة المبدئية
        document.querySelectorAll('.form-control').forEach(input => {
            if (input.value.trim()) {
                input.parentElement?.classList.add('focused');
            }
        });
    }

    /**
     * تحميل الصور الكسول - محسن
     */
    initImageLazyLoading() {
        if (!('IntersectionObserver' in window)) return;

        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    const src = img.dataset.src;

                    if (src) {
                        img.src = src;
                        img.classList.add('loaded');
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });

        this.observers.set('images', imageObserver);
    }

    /**
     * التمرير السلس - محسن
     */
    initSmoothScrolling() {
        document.addEventListener('click', (e) => {
            const anchor = e.target.closest('a[href^="#"]');

            if (!anchor || this.shouldSkipSmoothScroll(anchor)) return;

            e.preventDefault();
            e.stopPropagation();

            const target = document.querySelector(anchor.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }

    /**
     * فحص تخطي التمرير السلس
     */
    shouldSkipSmoothScroll(anchor) {
        return anchor.closest('.nav-link') ||
               anchor.id === 'languageToggleBtn' ||
               anchor.id === 'themeToggleBtn' ||
               anchor.closest('.navbar');
    }

    /**
     * التلميحات - مبسطة
     */
    initTooltips() {
        document.addEventListener('mouseenter', (e) => {
            if (e.target.matches('[data-tooltip]')) {
                this.showTooltip(e.target);
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.matches('[data-tooltip]')) {
                this.hideTooltip(e.target);
            }
        }, true);
    }

    /**
     * عرض التلميح
     */
    showTooltip(element) {
        if (element._tooltip) return;

        const tooltip = document.createElement('div');
        Object.assign(tooltip.style, {
            position: 'absolute',
            background: '#333',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: '1000',
            pointerEvents: 'none',
            opacity: '0',
            transition: 'opacity 0.2s ease'
        });

        tooltip.textContent = element.dataset.tooltip;
        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`;

        requestAnimationFrame(() => {
            tooltip.style.opacity = '1';
        });

        element._tooltip = tooltip;
    }

    /**
     * إخفاء التلميح
     */
    hideTooltip(element) {
        if (element._tooltip) {
            element._tooltip.remove();
            delete element._tooltip;
        }
    }

    /**
     * النوافذ المنبثقة - مبسطة
     */
    initModals() {
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-modal]');
            if (trigger) {
                e.preventDefault();
                const modalId = trigger.dataset.modal;
                const modal = document.getElementById(modalId);
                if (modal) this.showModal(modal);
            }

            if (e.target.matches('.modal')) {
                this.hideModal(e.target);
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) this.hideModal(openModal);
            }
        });
    }

    /**
     * عرض النافذة المنبثقة
     */
    showModal(modal) {
        modal.style.display = 'block';
        requestAnimationFrame(() => {
            modal.classList.add('show');
        });
        document.body.style.overflow = 'hidden';
    }

    /**
     * إخفاء النافذة المنبثقة
     */
    hideModal(modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
        document.body.style.overflow = '';
    }

    /**
     * أشرطة التقدم - محسنة
     */
    initProgressBars() {
        if (!('IntersectionObserver' in window)) return;

        const progressObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const progressBar = entry.target;
                    const progress = progressBar.dataset.progress || 0;

                    progressBar.style.width = `${progress}%`;
                    progressObserver.unobserve(progressBar);
                }
            });
        });

        document.querySelectorAll('.progress-bar[data-progress]').forEach(bar => {
            progressObserver.observe(bar);
        });

        this.observers.set('progress', progressObserver);
    }

    /**
     * تدمير جميع المراقبين
     */
    destroy() {
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers.clear();
    }
}

/**
 * وظائف مساعدة مبسطة
 */
const InteractionUtils = {
    /**
     * إنشاء إشعار منبثق مبسط
     */
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        const colors = {
            info: '#17a2b8',
            success: '#28a745',
            warning: '#ffc107',
            error: '#dc3545'
        };

        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '5px',
            background: colors[type] || colors.info,
            color: type === 'warning' ? '#212529' : '#fff',
            zIndex: '9999',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
        });

        notification.textContent = message;
        document.body.appendChild(notification);

        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
        });

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
};

// إضافة CSS للتأثيرات - مبسط
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    img[data-src] {
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    img.loaded {
        opacity: 1;
    }
`;
document.head.appendChild(style);

// تهيئة التفاعلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.uiInteractions = new UIInteractions();
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UIInteractions, InteractionUtils };
}
