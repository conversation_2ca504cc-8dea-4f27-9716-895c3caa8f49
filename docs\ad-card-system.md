# 📋 نظام البطاقات الموحد - دليل التخصيص

## 🎯 نظرة عامة

نظام البطاقات الموحد يوفر مرونة كاملة في تخصيص عرض بطاقات الإعلانات حسب احتياجات كل صفحة.

## 🔧 المعاملات المتاحة

### **المعاملات الأساسية:**
```php
@include('components.ad-card.index', [
    'ad' => $ad,                           // الإعلان (مطلوب)
    'variant' => 'default',               // نوع البطاقة
    'columns' => 3,                       // عدد الأعمدة (جديد!)
    'gridClass' => '',                    // فئات Grid مخصصة
    'customClass' => '',                  // فئات CSS إضافية
])
```

### **معاملات المحتوى:**
```php
'showFavorites' => true,       // إظهار أيقونة المفضلة
'descriptionLength' => 100,    // طول الوصف
'showPricing' => true,         // إظهار الأسعار
'showMeta' => true,            // إظهار المعلومات الإضافية
'showActions' => true,         // إظهار أزرار الإجراءات
'showExpiry' => true,          // إظهار انتهاء الصلاحية
```

### **معاملات التصميم:**
```php
'imageHeight' => '280px',      // ارتفاع الصورة
'cardHeight' => '550px',       // ارتفاع البطاقة
'noWrapper' => false,          // عدم إضافة wrapper
```

## 🎨 أنواع البطاقات (Variants)

### **1. default** - البطاقة الافتراضية
- **الاستخدام**: الصفحة الرئيسية، صفحات التصنيفات
- **الأعمدة الافتراضية**: 3 أعمدة
- **الخصائص**: عرض كامل للمعلومات

### **2. compact** - البطاقة المدمجة
- **الاستخدام**: الشريط الجانبي، الإعلانات المشابهة
- **الأعمدة الافتراضية**: 6 أعمدة
- **الخصائص**: حجم أصغر، معلومات مختصرة

### **3. detailed** - البطاقة المفصلة
- **الاستخدام**: صفحات خاصة تحتاج تفاصيل أكثر
- **الأعمدة الافتراضية**: 2 أعمدة
- **الخصائص**: حجم كبير، معلومات مفصلة

### **4. list** - عرض القائمة
- **الاستخدام**: العرض الأفقي
- **الأعمدة الافتراضية**: عمود واحد
- **الخصائص**: عرض أفقي كامل

### **5. favorites** - بطاقة المفضلة
- **الاستخدام**: صفحة المفضلة
- **الأعمدة الافتراضية**: 4 أعمدة
- **الخصائص**: زر إزالة من المفضلة

## 📐 نظام الأعمدة

### **تخصيص عدد الأعمدة:**
```php
'columns' => 2,  // عمودان
'columns' => 3,  // ثلاثة أعمدة (افتراضي)
'columns' => 4,  // أربعة أعمدة
'columns' => 5,  // خمسة أعمدة
'columns' => 6,  // ستة أعمدة
```

### **فئات Bootstrap المولدة تلقائياً:**
- **2 أعمدة**: `col-xl-6 col-lg-6 col-md-6 col-sm-12`
- **3 أعمدة**: `col-xl-4 col-lg-6 col-md-6 col-sm-12`
- **4 أعمدة**: `col-xl-3 col-lg-4 col-md-6 col-sm-12`
- **5 أعمدة**: `col-xl-2 col-lg-3 col-md-4 col-sm-6`
- **6 أعمدة**: `col-xl-2 col-lg-2 col-md-3 col-sm-6`

### **فئات Grid مخصصة:**
```php
'gridClass' => 'col-lg-3 col-md-4 col-sm-6',  // تجاوز النظام التلقائي
```

## 🌟 أمثلة عملية

### **صفحة الإعلانات الرئيسية (3 أعمدة):**
```php
@include('components.ad-card.index', [
    'ad' => $ad,
    'variant' => 'default',
    'columns' => 3,
    'showFavorites' => true,
    'descriptionLength' => 100,
])
```

### **صفحة المفضلة (4 أعمدة):**
```php
@include('components.ad-card.index', [
    'ad' => $favorite->ad,
    'variant' => 'favorites',
    'columns' => 4,
    'showFavorites' => false,
    'descriptionLength' => 80,
])
```

### **الشريط الجانبي (6 أعمدة مدمجة):**
```php
@include('components.ad-card.index', [
    'ad' => $ad,
    'variant' => 'compact',
    'columns' => 6,
    'showPricing' => false,
    'descriptionLength' => 50,
])
```

### **صفحة خاصة (2 أعمدة مفصلة):**
```php
@include('components.ad-card.index', [
    'ad' => $ad,
    'variant' => 'detailed',
    'columns' => 2,
    'showMeta' => true,
    'descriptionLength' => 200,
])
```

## 📱 التصميم المتجاوب

النظام يدعم التصميم المتجاوب تلقائياً:
- **XL Screens (≥1200px)**: العدد المحدد من الأعمدة
- **Large Screens (≥992px)**: تقليل تدريجي
- **Medium Screens (≥768px)**: 2-3 أعمدة عادة
- **Small Screens (<768px)**: عمود واحد أو اثنان

## 🔄 التوافق مع النظام القديم

النظام الجديد متوافق تماماً مع الكود الموجود:
- إذا لم تحدد `columns`، سيستخدم الافتراضي حسب `variant`
- جميع المعاملات القديمة تعمل بنفس الطريقة
- لا حاجة لتعديل الكود الموجود

## 🎯 نصائح للاستخدام

1. **استخدم `columns`** للتحكم السريع في العدد
2. **استخدم `gridClass`** للتحكم الدقيق في Bootstrap
3. **اختبر على شاشات مختلفة** للتأكد من التجاوب
4. **استخدم variants مناسبة** لكل سياق
5. **اضبط `descriptionLength`** حسب المساحة المتاحة
