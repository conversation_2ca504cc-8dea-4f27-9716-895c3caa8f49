<?php if($paginator->hasPages()): ?>
    <nav aria-label="<?php echo e(__('Pagination Navigation')); ?>" class="d-flex justify-content-center">
        <ul class="pagination pagination-sm mb-0">
            
            <?php if($paginator->onFirstPage()): ?>
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="fas fa-chevron-right" style="font-size: 12px;"></i>
                    </span>
                </li>
            <?php else: ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo e($paginator->previousPageUrl()); ?>" rel="prev">
                        <i class="fas fa-chevron-right" style="font-size: 12px;"></i>
                    </a>
                </li>
            <?php endif; ?>

            
            <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                
                <?php if(is_string($element)): ?>
                    <li class="page-item disabled" aria-disabled="true">
                        <span class="page-link"><?php echo e($element); ?></span>
                    </li>
                <?php endif; ?>

                
                <?php if(is_array($element)): ?>
                    <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($page == $paginator->currentPage()): ?>
                            <li class="page-item active" aria-current="page">
                                <span class="page-link"><?php echo e($page); ?></span>
                            </li>
                        <?php else: ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            
            <?php if($paginator->hasMorePages()): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo e($paginator->nextPageUrl()); ?>" rel="next">
                        <i class="fas fa-chevron-left" style="font-size: 12px;"></i>
                    </a>
                </li>
            <?php else: ?>
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="fas fa-chevron-left" style="font-size: 12px;"></i>
                    </span>
                </li>
            <?php endif; ?>
        </ul>
    </nav>

    
    <div class="text-center mt-2">
        <small class="text-muted arabic-text">
            <?php echo e(__('Showing')); ?> <?php echo e($paginator->firstItem()); ?> <?php echo e(__('to')); ?> <?php echo e($paginator->lastItem()); ?> <?php echo e(__('of')); ?> <?php echo e($paginator->total()); ?> <?php echo e(__('results')); ?>

        </small>
    </div>
<?php endif; ?>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/pagination/custom.blade.php ENDPATH**/ ?>