/**
 * ملف JavaScript متقدم لمكونات الأسعار
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على وظائف متقدمة للتفاعل مع عرض الأسعار
 */

// متغيرات عامة
const PricingAdvanced = {
    // إعدادات التطبيق
    config: {
        animationDuration: 300,
        toastDuration: 3000,
        logLevel: 'info'
    },

    // حالة التطبيق
    state: {
        isInitialized: false,
        activeTooltips: new Set(),
        priceInteractions: 0
    },

    /**
     * تهيئة مكونات الأسعار المتقدمة
     */
    init() {
        if (this.state.isInitialized) {
            console.warn('PricingAdvanced already initialized');
            return;
        }

        try {
            this.log('info', 'تهيئة مكونات الأسعار المتقدمة');
            
            this.initializePriceInteractions();
            this.initializeTooltips();
            this.initializeCopyFeatures();
            this.initializeAnimations();
            this.setupEventListeners();
            
            this.state.isInitialized = true;
            this.log('info', 'تم تهيئة مكونات الأسعار المتقدمة بنجاح');
            
        } catch (error) {
            this.log('error', 'خطأ في تهيئة مكونات الأسعار المتقدمة', error);
        }
    },

    /**
     * تهيئة تفاعلات الأسعار
     */
    initializePriceInteractions() {
        const priceElements = document.querySelectorAll('.price-value-overlay, .price-amount');
        
        priceElements.forEach(element => {
            // تأثير التركيز
            element.addEventListener('mouseenter', (e) => {
                this.handlePriceHover(e.target, true);
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.handlePriceHover(e.target, false);
            });
            
            // نسخ السعر عند النقر
            element.addEventListener('click', (e) => {
                this.handlePriceClick(e.target);
            });
        });
        
        this.log('info', `تم تهيئة ${priceElements.length} عنصر سعر للتفاعل`);
    },

    /**
     * تهيئة التلميحات
     */
    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip(e.target);
            });
        });
        
        this.log('info', `تم تهيئة ${tooltipElements.length} تلميح`);
    },

    /**
     * تهيئة ميزات النسخ
     */
    initializeCopyFeatures() {
        const copyButtons = document.querySelectorAll('[data-copy]');
        
        copyButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const textToCopy = button.getAttribute('data-copy');
                this.copyToClipboard(textToCopy, button);
            });
        });
        
        this.log('info', `تم تهيئة ${copyButtons.length} زر نسخ`);
    },

    /**
     * تهيئة الحركات والتأثيرات
     */
    initializeAnimations() {
        // تأثير الظهور التدريجي للبطاقات
        const pricingCards = document.querySelectorAll('.pricing-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
        
        pricingCards.forEach(card => {
            observer.observe(card);
        });
        
        this.log('info', `تم تهيئة الحركات لـ ${pricingCards.length} بطاقة أسعار`);
    },

    /**
     * إعداد مستمعي الأحداث العامة
     */
    setupEventListeners() {
        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // مراقبة تغيير الوضع المظلم
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addEventListener('change', (e) => {
                this.handleDarkModeChange(e.matches);
            });
        }
    },

    /**
     * معالجة تفاعل الماوس مع الأسعار
     */
    handlePriceHover(element, isHovering) {
        if (isHovering) {
            element.style.transform = 'scale(1.05)';
            element.style.textShadow = '0 4px 8px rgba(16, 185, 129, 0.3)';
            element.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        } else {
            element.style.transform = 'scale(1)';
            element.style.textShadow = '0 2px 4px rgba(16, 185, 129, 0.2)';
        }
    },

    /**
     * معالجة النقر على السعر
     */
    handlePriceClick(element) {
        const priceText = element.textContent.trim();
        this.copyToClipboard(priceText, element);
        
        // تسجيل التفاعل
        this.state.priceInteractions++;
        this.log('info', 'تفاعل مع السعر', {
            price: priceText,
            interactions: this.state.priceInteractions
        });
        
        // تأثير بصري للنقر
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = 'scale(1.05)';
        }, 150);
    },

    /**
     * نسخ النص إلى الحافظة
     */
    async copyToClipboard(text, sourceElement = null) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('تم نسخ السعر: ' + text, 'success');
            
            if (sourceElement) {
                this.showCopyFeedback(sourceElement);
            }
            
            this.log('info', 'تم نسخ النص بنجاح', { text });
            
        } catch (error) {
            // fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                this.showToast('تم نسخ السعر: ' + text, 'success');
                this.log('info', 'تم نسخ النص باستخدام fallback', { text });
            } catch (fallbackError) {
                this.showToast('فشل في نسخ النص', 'error');
                this.log('error', 'فشل في نسخ النص', fallbackError);
            }
            
            document.body.removeChild(textArea);
        }
    },

    /**
     * إظهار تأثير بصري للنسخ
     */
    showCopyFeedback(element) {
        const feedback = document.createElement('div');
        feedback.className = 'copy-feedback';
        feedback.textContent = '✓ تم النسخ';
        feedback.style.cssText = `
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            z-index: 1000;
            animation: fadeInOut 2s ease-in-out;
        `;
        
        element.style.position = 'relative';
        element.appendChild(feedback);
        
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 2000);
    },

    /**
     * إظهار رسالة Toast
     */
    showToast(message, type = 'info') {
        // استخدام نظام Toast الموحد إذا كان متوفراً
        if (typeof showToast === 'function') {
            showToast(message, type);
            return;
        }
        
        // إنشاء Toast مخصص
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, this.config.toastDuration);
    },

    /**
     * معالجة تغيير حجم النافذة
     */
    handleResize() {
        this.log('info', 'تغيير حجم النافذة');
        // إعادة حساب مواضع التلميحات إذا لزم الأمر
        this.state.activeTooltips.forEach(tooltip => {
            this.repositionTooltip(tooltip);
        });
    },

    /**
     * معالجة تغيير الوضع المظلم
     */
    handleDarkModeChange(isDark) {
        this.log('info', 'تغيير الوضع المظلم', { isDark });
        document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
    },

    /**
     * دالة تأخير التنفيذ
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * تسجيل الأحداث
     */
    log(level, message, data = null) {
        if (this.config.logLevel === 'none') return;
        
        const logData = {
            timestamp: new Date().toISOString(),
            component: 'PricingAdvanced',
            message,
            data
        };
        
        switch (level) {
            case 'error':
                console.error('[PricingAdvanced]', message, data);
                break;
            case 'warn':
                console.warn('[PricingAdvanced]', message, data);
                break;
            case 'info':
            default:
                console.log('[PricingAdvanced]', message, data);
                break;
        }
    }
};

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    PricingAdvanced.init();
});

// تصدير للاستخدام العام
window.PricingAdvanced = PricingAdvanced;
