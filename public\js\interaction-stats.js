/**
 * ملف JavaScript متقدم لإحصائيات التفاعل والمفضلة
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على وظائف متقدمة للتفاعل مع الإحصائيات والرسوم البيانية
 */

class InteractionStats {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.startRealTimeUpdates();
    }

    /**
     * تهيئة الكلاس
     */
    init() {
        this.updateInterval = null;
        this.animationDuration = 1000;
        this.countUpDuration = 2000;
        
        // تحديد عناصر DOM
        this.statsSection = document.querySelector('.interaction-stats-section');
        this.statNumbers = document.querySelectorAll('.stat-number');
        this.chartBars = document.querySelectorAll('.bar-fill');
        
        // تشغيل الرسوم المتحركة عند التحميل
        this.animateOnLoad();
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تحديث الإحصائيات عند النقر
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-refresh-stats]')) {
                this.refreshStats();
            }
        });

        // تصدير الإحصائيات
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-export-stats]')) {
                const adId = e.target.dataset.adId;
                this.exportStats(adId);
            }
        });

        // عرض التحليلات المفصلة
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-detailed-analytics]')) {
                const adId = e.target.dataset.adId;
                this.viewDetailedAnalytics(adId);
            }
        });

        // تحديث الإحصائيات عند تغيير حالة المفضلة
        document.addEventListener('favoriteToggled', (e) => {
            this.updateFavoriteStats(e.detail);
        });

        // تحديث الإحصائيات عند إضافة تقييم
        document.addEventListener('ratingAdded', (e) => {
            this.updateRatingStats(e.detail);
        });

        // تحديث الإحصائيات عند إضافة تعليق
        document.addEventListener('commentAdded', (e) => {
            this.updateCommentStats(e.detail);
        });
    }

    /**
     * تشغيل الرسوم المتحركة عند التحميل
     */
    animateOnLoad() {
        // تحريك الأرقام
        this.animateNumbers();
        
        // تحريك الرسم البياني
        setTimeout(() => {
            this.animateChart();
        }, 500);

        // تحريك البطاقات
        this.animateCards();
    }

    /**
     * تحريك الأرقام (Count Up Effect)
     */
    animateNumbers() {
        this.statNumbers.forEach(numberElement => {
            const finalValue = parseInt(numberElement.textContent.replace(/,/g, ''));
            const duration = this.countUpDuration;
            const startTime = performance.now();

            const updateNumber = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // استخدام easing function للحصول على تأثير طبيعي
                const easedProgress = this.easeOutCubic(progress);
                const currentValue = Math.floor(finalValue * easedProgress);
                
                numberElement.textContent = this.formatNumber(currentValue);
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            };

            requestAnimationFrame(updateNumber);
        });
    }

    /**
     * تحريك الرسم البياني
     */
    animateChart() {
        this.chartBars.forEach((bar, index) => {
            const height = bar.style.height;
            bar.style.height = '0%';
            
            setTimeout(() => {
                bar.style.transition = 'height 0.8s ease-out';
                bar.style.height = height;
            }, index * 200);
        });
    }

    /**
     * تحريك البطاقات
     */
    animateCards() {
        const cards = document.querySelectorAll('.stat-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 150);
        });
    }

    /**
     * تحديث الإحصائيات
     */
    async refreshStats() {
        try {
            const adId = this.getAdId();
            const response = await fetch(`/api/ads/${adId}/stats`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.updateStatsDisplay(data.stats);
                this.showNotification('تم تحديث الإحصائيات بنجاح', 'success');
            } else {
                throw new Error('فشل في تحديث الإحصائيات');
            }
        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
            this.showNotification('حدث خطأ في تحديث الإحصائيات', 'error');
        }
    }

    /**
     * تحديث عرض الإحصائيات
     */
    updateStatsDisplay(stats) {
        // تحديث الأرقام الرئيسية
        const elements = {
            views: document.querySelector('.views-card .stat-number'),
            favorites: document.querySelector('.favorites-card .stat-number'),
            ratings: document.querySelector('.ratings-card .stat-number'),
            comments: document.querySelector('.comments-card .stat-number')
        };

        Object.keys(elements).forEach(key => {
            if (elements[key] && stats[key] !== undefined) {
                this.animateNumberUpdate(elements[key], stats[key]);
            }
        });

        // تحديث معدل التفاعل
        const engagementElement = document.querySelector('.engagement-rate');
        if (engagementElement && stats.engagement_rate !== undefined) {
            engagementElement.textContent = `${stats.engagement_rate}% معدل التفاعل`;
        }

        // تحديث الرسم البياني
        this.updateChart(stats);
    }

    /**
     * تحديث رقم بتأثير متحرك
     */
    animateNumberUpdate(element, newValue) {
        const currentValue = parseInt(element.textContent.replace(/,/g, ''));
        const difference = newValue - currentValue;
        const duration = 1000;
        const startTime = performance.now();

        const updateNumber = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = this.easeOutCubic(progress);
            const value = Math.floor(currentValue + (difference * easedProgress));
            
            element.textContent = this.formatNumber(value);
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        };

        requestAnimationFrame(updateNumber);
    }

    /**
     * تحديث الرسم البياني
     */
    updateChart(stats) {
        const maxValue = Math.max(stats.views, stats.favorites, stats.ratings, stats.comments);
        
        const chartData = [
            { class: 'views-bar', value: stats.views },
            { class: 'favorites-bar', value: stats.favorites },
            { class: 'ratings-bar', value: stats.ratings },
            { class: 'comments-bar', value: stats.comments }
        ];

        chartData.forEach(item => {
            const bar = document.querySelector(`.${item.class}`);
            if (bar) {
                const height = maxValue > 0 ? (item.value / maxValue) * 100 : 0;
                bar.style.height = `${height}%`;
                
                // تحديث القيمة
                const valueElement = bar.parentElement.querySelector('.bar-value');
                if (valueElement) {
                    valueElement.textContent = this.formatNumber(item.value);
                }
            }
        });
    }

    /**
     * تحديث إحصائيات المفضلة
     */
    updateFavoriteStats(data) {
        const favoritesCard = document.querySelector('.favorites-card .stat-number');
        if (favoritesCard) {
            const currentValue = parseInt(favoritesCard.textContent.replace(/,/g, ''));
            const newValue = data.action === 'added' ? currentValue + 1 : currentValue - 1;
            this.animateNumberUpdate(favoritesCard, Math.max(0, newValue));
        }
    }

    /**
     * تحديث إحصائيات التقييمات
     */
    updateRatingStats(data) {
        const ratingsCard = document.querySelector('.ratings-card .stat-number');
        if (ratingsCard) {
            const currentValue = parseInt(ratingsCard.textContent.replace(/,/g, ''));
            this.animateNumberUpdate(ratingsCard, currentValue + 1);
        }

        // تحديث متوسط التقييم
        const ratingDisplay = document.querySelector('.rating-display');
        if (ratingDisplay && data.new_average) {
            this.updateRatingDisplay(ratingDisplay, data.new_average);
        }
    }

    /**
     * تحديث إحصائيات التعليقات
     */
    updateCommentStats(data) {
        const commentsCard = document.querySelector('.comments-card .stat-number');
        if (commentsCard) {
            const currentValue = parseInt(commentsCard.textContent.replace(/,/g, ''));
            this.animateNumberUpdate(commentsCard, currentValue + 1);
        }
    }

    /**
     * تصدير الإحصائيات
     */
    async exportStats(adId) {
        try {
            const response = await fetch(`/api/ads/${adId}/stats/export`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `ad-${adId}-stats.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showNotification('تم تصدير الإحصائيات بنجاح', 'success');
            } else {
                throw new Error('فشل في تصدير الإحصائيات');
            }
        } catch (error) {
            console.error('خطأ في تصدير الإحصائيات:', error);
            this.showNotification('حدث خطأ في تصدير الإحصائيات', 'error');
        }
    }

    /**
     * عرض التحليلات المفصلة
     */
    viewDetailedAnalytics(adId) {
        // فتح نافذة منبثقة أو توجيه لصفحة التحليلات المفصلة
        window.open(`/dashboard/ads/${adId}/analytics`, '_blank');
    }

    /**
     * بدء التحديثات في الوقت الفعلي
     */
    startRealTimeUpdates() {
        // تحديث الإحصائيات كل 30 ثانية
        this.updateInterval = setInterval(() => {
            this.refreshStats();
        }, 30000);
    }

    /**
     * إيقاف التحديثات في الوقت الفعلي
     */
    stopRealTimeUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * دوال مساعدة
     */
    
    getAdId() {
        return document.querySelector('[data-ad-id]')?.dataset.adId || 
               window.location.pathname.match(/\/ads\/(\d+)/)?.[1];
    }

    formatNumber(num) {
        return new Intl.NumberFormat('ar-SA').format(num);
    }

    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    updateRatingDisplay(element, average) {
        const stars = element.querySelectorAll('.fas.fa-star');
        stars.forEach((star, index) => {
            star.className = `fas fa-star ${index < average ? 'text-warning' : 'text-muted'}`;
        });
        
        const averageSpan = element.querySelector('span');
        if (averageSpan) {
            averageSpan.textContent = average.toFixed(1);
        }
    }

    showNotification(message, type = 'info') {
        // استخدام نظام الإشعارات الموجود في الموقع
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * تنظيف الموارد عند إلغاء تحميل الصفحة
     */
    destroy() {
        this.stopRealTimeUpdates();
        // إزالة مستمعي الأحداث إذا لزم الأمر
    }
}

// تهيئة الكلاس عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.interaction-stats-section')) {
        window.InteractionStats = new InteractionStats();
    }
});

// تنظيف الموارد عند إلغاء تحميل الصفحة
window.addEventListener('beforeunload', () => {
    if (window.InteractionStats) {
        window.InteractionStats.destroy();
    }
});

// تصدير الكلاس للاستخدام العام
window.InteractionStats = InteractionStats;
