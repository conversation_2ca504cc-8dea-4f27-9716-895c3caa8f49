{{--
    مكون التواصل الموحد والشامل - محدث للهيكل المحسن الجديد
    يستخدم الحقول المشفرة الجديدة: contact_phone, contact_email, contact_whatsapp
    يدعم: جميع أنواع التواصل مع إعدادات الخصوصية المتقدمة
    الحل الوحيد لجميع احتياجات التواصل في البطاقات
--}}
@props([
    'ad',
    'config' => [],
    'showIcons' => true,        // إظهار الأيقونات
    'showDetails' => false,     // إظهار التفاصيل النصية
    'showReveal' => false,      // إظهار نظام الكشف المتدرج
    'iconStyle' => 'circular'   // نمط الأيقونات: circular, square, minimal
])

@php
    use App\Services\EncryptionService;
    
    // الحصول على معلومات التواصل من الحقول المشفرة الجديدة
    $currentUser = auth()->user();
    $adOwner = $ad->user;

    // فك تشفير معلومات التواصل من الإعلان
    $contactPhone = null;
    $contactEmail = null;
    $contactWhatsapp = null;
    
    try {
        $contactPhone = $ad->contact_phone ? EncryptionService::decryptText($ad->contact_phone) : null;
        $contactEmail = $ad->contact_email ? EncryptionService::decryptText($ad->contact_email) : null;
        $contactWhatsapp = $ad->contact_whatsapp ? EncryptionService::decryptText($ad->contact_whatsapp) : null;
    } catch (\Exception $e) {
        // في حالة فشل فك التشفير، استخدم البيانات من المستخدم
        try {
            $contactPhone = $adOwner->phone ? EncryptionService::decryptText($adOwner->phone) : null;
            $contactWhatsapp = $adOwner->whatsapp ? EncryptionService::decryptText($adOwner->whatsapp) : null;
        } catch (\Exception $e2) {
            // في حالة فشل فك التشفير تماماً، لا تعرض معلومات التواصل
            $contactPhone = null;
            $contactWhatsapp = null;
        }
        $contactEmail = $adOwner->email; // البريد الإلكتروني غير مشفر في جدول المستخدمين
    }

    // التحقق من إعدادات الخصوصية - استخدام الـ accessor الجديد
    $privacySettings = $adOwner->getPrivacySettingsAttribute();
    $showPhone = $privacySettings['show_phone'] ?? true;
    $showEmail = $privacySettings['show_email'] ?? true;
    $showWhatsapp = $privacySettings['show_whatsapp'] ?? true;
    $allowContactReveal = $privacySettings['allow_contact_reveal'] ?? true;

    // تطبيق إعدادات الخصوصية
    $hasPhone = $contactPhone && $showPhone;
    $hasWhatsApp = $contactWhatsapp && $showWhatsapp;
    $hasEmail = $contactEmail && $showEmail;

    // تحضير أرقام الهاتف والواتساب
    $phoneNumber = $hasPhone ? $contactPhone : '';
    $whatsappNumber = $hasWhatsApp ? $contactWhatsapp : ($hasPhone ? $phoneNumber : '');
    $emailAddress = $hasEmail ? $contactEmail : '';

    // تنظيف رقم الواتساب (إزالة المسافات والرموز)
    if ($whatsappNumber) {
        $whatsappNumber = preg_replace('/[^0-9+]/', '', $whatsappNumber);
        
        // إضافة رمز الدولة إذا لم يكن موجود (افتراضي: اليمن +967)
        if (!str_starts_with($whatsappNumber, '+')) {
            if (str_starts_with($whatsappNumber, '967')) {
                $whatsappNumber = '+' . $whatsappNumber;
            } elseif (str_starts_with($whatsappNumber, '0')) {
                $whatsappNumber = '+967' . substr($whatsappNumber, 1);
            } else {
                $whatsappNumber = '+967' . $whatsappNumber;
            }
        }
    }

    // التحقق من وجود أي معلومات تواصل
    $hasAnyContact = $hasPhone || $hasWhatsApp || $hasEmail;

    // إعدادات العرض
    $defaultConfig = [
        'showPhone' => true,
        'showWhatsApp' => true,
        'showEmail' => true,
        'showCopyButtons' => true,
        'showLabels' => false,
        'compactMode' => false,
        'iconSize' => 'md',
        'buttonStyle' => 'outline',
        'alignment' => 'start'
    ];
    
    $config = array_merge($defaultConfig, $config);
@endphp

@if($hasAnyContact)
    <div class="contact-section {{ $config['compactMode'] ? 'compact' : '' }}" 
         data-ad-id="{{ $ad->id }}"
         data-contact-reveal="{{ $allowContactReveal ? 'true' : 'false' }}">
        
        @if($showIcons)
            <div class="contact-icons d-flex align-items-center justify-content-{{ $config['alignment'] }} gap-2">
                
                {{-- أيقونة الهاتف --}}
                @if($hasPhone && $config['showPhone'])
                    <div class="contact-item phone-contact" data-contact-type="phone">
                        @if($showReveal && !$currentUser)
                            {{-- نظام الكشف للزوار --}}
                            <button class="btn btn-{{ $config['buttonStyle'] }}-primary btn-sm contact-reveal-btn"
                                    data-contact-type="phone"
                                    data-ad-id="{{ $ad->id }}"
                                    title="{{ __('Click to reveal phone number') }}">
                                <i class="fas fa-phone"></i>
                                @if($config['showLabels'])
                                    <span class="ms-1">{{ __('Phone') }}</span>
                                @endif
                            </button>
                        @else
                            {{-- عرض مباشر للمستخدمين المسجلين --}}
                            <a href="tel:{{ $phoneNumber }}" 
                               class="btn btn-{{ $config['buttonStyle'] }}-success btn-sm"
                               title="{{ __('Call') }}: {{ EncryptionService::maskPhone($phoneNumber) }}">
                                <i class="fas fa-phone"></i>
                                @if($config['showLabels'])
                                    <span class="ms-1">{{ __('Call') }}</span>
                                @endif
                            </a>
                            
                            @if($config['showCopyButtons'])
                                <button class="btn btn-outline-secondary btn-sm ms-1 copy-contact-btn"
                                        data-contact="{{ $phoneNumber }}"
                                        data-contact-type="phone"
                                        title="{{ __('Copy phone number') }}">
                                    <i class="fas fa-copy"></i>
                                </button>
                            @endif
                        @endif
                    </div>
                @endif

                {{-- أيقونة الواتساب --}}
                @if($hasWhatsApp && $config['showWhatsApp'])
                    <div class="contact-item whatsapp-contact" data-contact-type="whatsapp">
                        @if($showReveal && !$currentUser)
                            {{-- نظام الكشف للزوار --}}
                            <button class="btn btn-{{ $config['buttonStyle'] }}-success btn-sm contact-reveal-btn"
                                    data-contact-type="whatsapp"
                                    data-ad-id="{{ $ad->id }}"
                                    title="{{ __('Click to reveal WhatsApp number') }}">
                                <i class="fab fa-whatsapp"></i>
                                @if($config['showLabels'])
                                    <span class="ms-1">{{ __('WhatsApp') }}</span>
                                @endif
                            </button>
                        @else
                            {{-- رابط واتساب مباشر --}}
                            <a href="https://wa.me/{{ $whatsappNumber }}?text={{ urlencode(__('Hello, I am interested in your ad: ') . $ad->title) }}" 
                               target="_blank"
                               class="btn btn-{{ $config['buttonStyle'] }}-success btn-sm"
                               title="{{ __('WhatsApp') }}: {{ EncryptionService::maskPhone($whatsappNumber) }}">
                                <i class="fab fa-whatsapp"></i>
                                @if($config['showLabels'])
                                    <span class="ms-1">{{ __('WhatsApp') }}</span>
                                @endif
                            </a>
                            
                            @if($config['showCopyButtons'])
                                <button class="btn btn-outline-secondary btn-sm ms-1 copy-contact-btn"
                                        data-contact="{{ $whatsappNumber }}"
                                        data-contact-type="whatsapp"
                                        title="{{ __('Copy WhatsApp number') }}">
                                    <i class="fas fa-copy"></i>
                                </button>
                            @endif
                        @endif
                    </div>
                @endif

                {{-- أيقونة البريد الإلكتروني --}}
                @if($hasEmail && $config['showEmail'])
                    <div class="contact-item email-contact" data-contact-type="email">
                        @if($showReveal && !$currentUser)
                            {{-- نظام الكشف للزوار --}}
                            <button class="btn btn-{{ $config['buttonStyle'] }}-info btn-sm contact-reveal-btn"
                                    data-contact-type="email"
                                    data-ad-id="{{ $ad->id }}"
                                    title="{{ __('Click to reveal email address') }}">
                                <i class="fas fa-envelope"></i>
                                @if($config['showLabels'])
                                    <span class="ms-1">{{ __('Email') }}</span>
                                @endif
                            </button>
                        @else
                            {{-- رابط بريد إلكتروني مباشر --}}
                            <a href="mailto:{{ $emailAddress }}?subject={{ urlencode(__('Inquiry about: ') . $ad->title) }}" 
                               class="btn btn-{{ $config['buttonStyle'] }}-info btn-sm"
                               title="{{ __('Email') }}: {{ EncryptionService::maskEmail($emailAddress) }}">
                                <i class="fas fa-envelope"></i>
                                @if($config['showLabels'])
                                    <span class="ms-1">{{ __('Email') }}</span>
                                @endif
                            </a>
                            
                            @if($config['showCopyButtons'])
                                <button class="btn btn-outline-secondary btn-sm ms-1 copy-contact-btn"
                                        data-contact="{{ $emailAddress }}"
                                        data-contact-type="email"
                                        title="{{ __('Copy email address') }}">
                                    <i class="fas fa-copy"></i>
                                </button>
                            @endif
                        @endif
                    </div>
                @endif
            </div>
        @endif

        @if($showDetails)
            <div class="contact-details mt-2">
                <div class="contact-details-content">
                    @if($hasPhone && $config['showPhone'])
                        <div class="contact-detail-item">
                            <strong>{{ __('Phone') }}:</strong>
                            <span class="contact-value">{{ EncryptionService::maskPhone($phoneNumber) }}</span>
                        </div>
                    @endif
                    
                    @if($hasWhatsApp && $config['showWhatsApp'])
                        <div class="contact-detail-item">
                            <strong>{{ __('WhatsApp') }}:</strong>
                            <span class="contact-value">{{ EncryptionService::maskPhone($whatsappNumber) }}</span>
                        </div>
                    @endif
                    
                    @if($hasEmail && $config['showEmail'])
                        <div class="contact-detail-item">
                            <strong>{{ __('Email') }}:</strong>
                            <span class="contact-value">{{ EncryptionService::maskEmail($emailAddress) }}</span>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
@else
    {{-- رسالة عدم توفر معلومات التواصل --}}
    <div class="no-contact-info text-muted">
        <small>
            <i class="fas fa-info-circle me-1"></i>
            {{ __('Contact information not available') }}
        </small>
    </div>
@endif

{{-- JavaScript للتفاعل مع أزرار النسخ والكشف --}}
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // نسخ معلومات التواصل
    document.querySelectorAll('.copy-contact-btn').forEach(button => {
        button.addEventListener('click', function() {
            const contact = this.dataset.contact;
            const type = this.dataset.contactType;
            
            navigator.clipboard.writeText(contact).then(() => {
                // إظهار رسالة نجاح
                this.innerHTML = '<i class="fas fa-check"></i>';
                this.classList.add('btn-success');
                
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-copy"></i>';
                    this.classList.remove('btn-success');
                }, 2000);
            });
        });
    });

    // كشف معلومات التواصل للزوار
    document.querySelectorAll('.contact-reveal-btn').forEach(button => {
        button.addEventListener('click', function() {
            const adId = this.dataset.adId;
            const contactType = this.dataset.contactType;
            
            // تسجيل كشف التواصل
            fetch(`/ads/${adId}/reveal-contact`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    contact_type: contactType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // استبدال الزر بمعلومات التواصل الفعلية
                    this.outerHTML = data.contact_html;
                }
            });
        });
    });
});
</script>
@endpush
