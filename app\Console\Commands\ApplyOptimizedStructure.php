<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

class ApplyOptimizedStructure extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'db:apply-optimized-structure {--force : تطبيق التغييرات بدون تأكيد}';

    /**
     * The console command description.
     */
    protected $description = 'تطبيق الهيكل المحسن لقاعدة البيانات';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 بدء تطبيق الهيكل المحسن لقاعدة البيانات...');

        if (!$this->option('force')) {
            if (!$this->confirm('⚠️ هذا سيحذف جميع البيانات الحالية ويطبق الهيكل الجديد. هل أنت متأكد؟')) {
                $this->info('❌ تم إلغاء العملية');
                return 1;
            }
        }

        try {
            // 1. إعادة تعيين قاعدة البيانات
            $this->info('🗑️ مسح قاعدة البيانات الحالية...');
            $this->call('migrate:fresh', ['--force' => true]);

            // 2. تطبيق الهيكل الجديد
            $this->info('🏗️ تطبيق الهيكل المحسن...');
            $this->call('migrate', ['--force' => true]);

            // 3. إنشاء الإعدادات الافتراضية
            $this->info('⚙️ إنشاء الإعدادات الافتراضية...');
            Setting::createDefaults();

            // 4. فحص الهيكل الجديد
            $this->info('🔍 فحص الهيكل الجديد...');
            $this->call('db:health-check');

            // 5. تسخين الكاش
            $this->info('🔥 تسخين الكاش...');
            $this->call('cache:warm-up', ['--type' => 'all']);

            $this->info('🎉 تم تطبيق الهيكل المحسن بنجاح!');
            
            $this->displaySummary();

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ فشل في تطبيق الهيكل المحسن: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * عرض ملخص التغييرات
     */
    private function displaySummary()
    {
        $this->newLine();
        $this->info('📊 ملخص الهيكل الجديد:');
        
        $tables = [
            'users' => 'جدول المستخدمين الموحد',
            'categories' => 'جدول التصنيفات',
            'ads' => 'جدول الإعلانات المحسن',
            'interactions' => 'جدول التفاعلات الموحد (مفضلة، تقييمات، تعليقات)',
            'notifications' => 'جدول الإشعارات',
            'system_logs' => 'جدول السجلات الموحد',
            'settings' => 'جدول الإعدادات الموحد',
        ];

        foreach ($tables as $table => $description) {
            if (Schema::hasTable($table)) {
                $count = DB::table($table)->count();
                $this->line("✅ {$description}: {$table} ({$count} سجل)");
            }
        }

        $this->newLine();
        $this->info('🎯 الفوائد المحققة:');
        $this->line('• تقليل عدد الجداول من 15+ إلى 7 جداول أساسية');
        $this->line('• توحيد التفاعلات في جدول واحد مرن');
        $this->line('• تحسين الأداء بفهارس محسنة');
        $this->line('• سهولة الصيانة والتطوير');
        $this->line('• تقليل التعقيد والتكرار');

        $this->newLine();
        $this->info('📝 الخطوات التالية:');
        $this->line('1. اختبار الوظائف الأساسية');
        $this->line('2. إضافة بيانات تجريبية إذا لزم الأمر');
        $this->line('3. مراقبة الأداء');
        $this->line('4. تحديث الواجهات حسب الحاجة');
    }
}
