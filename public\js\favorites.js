/**
 * نظام المفضلة المحسن - الإصدار 2.0
 * حل شامل لجميع مشاكل إدارة الحالة والتزامن
 * تم إعادة كتابته بالكامل لضمان الاستقرار والأداء
 */

// ===== متغيرات النظام العامة =====
let isProcessing = false;
let lastRequestTime = 0;
let favoritesCache = new Map();
let systemInitialized = false;
let retryCount = 0;
let currentFavoritesCount = 0;

// ===== إعدادات النظام =====
const CONFIG = {
    REQUEST_DEBOUNCE_TIME: 300,
    MAX_RETRY_ATTEMPTS: 3,
    CACHE_DURATION: 5 * 60 * 1000,
    INIT_DELAY: 100,
    FALLBACK_DELAY: 200,
    STORAGE_KEY: 'favorites_cache'
};

// ===== دوال مساعدة أساسية =====
function getCSRFToken() {
    return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
           document.querySelector('input[name="_token"]')?.value;
}

function isUserAuthenticated() {
    return document.querySelector('meta[name="user-authenticated"]') !== null ||
           document.body.classList.contains('authenticated') ||
           document.querySelector('.navbar .dropdown-toggle') !== null;
}

function logDebug(message, data = null) {
    if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
        console.log(`🔄 [Favorites] ${message}`, data || '');
    }
}

function showToast(message, type = 'info') {
    if (window.showToast && typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        console.log(`Toast [${type}]: ${message}`);
    }
}

// ===== إدارة الكاش المحلي =====
function saveFavoritesToCache() {
    try {
        const cacheData = {
            favorites: Array.from(favoritesCache.entries()),
            count: currentFavoritesCount,
            timestamp: Date.now()
        };
        localStorage.setItem(CONFIG.STORAGE_KEY, JSON.stringify(cacheData));
        logDebug('تم حفظ الكاش المحلي');
    } catch (error) {
        console.warn('فشل في حفظ الكاش:', error);
    }
}

function loadFavoritesFromCache() {
    try {
        const cached = localStorage.getItem(CONFIG.STORAGE_KEY);
        if (cached) {
            const cacheData = JSON.parse(cached);
            const age = Date.now() - cacheData.timestamp;

            if (age < CONFIG.CACHE_DURATION) {
                favoritesCache = new Map(cacheData.favorites);
                currentFavoritesCount = cacheData.count || 0;
                logDebug('تم تحميل الكاش المحلي', { count: currentFavoritesCount });
                updateAllCounters();
                return true;
            }
        }

        // إذا لم يكن هناك كاش صالح، تأكد من تحديث العداد
        logDebug('لا يوجد كاش صالح، تحديث العداد بالقيمة الحالية');
        updateAllCounters();
    } catch (error) {
        console.warn('فشل في تحميل الكاش:', error);
        // تحديث العداد حتى في حالة الخطأ
        updateAllCounters();
    }
    return false;
}

// ===== تهيئة النظام =====
document.addEventListener('DOMContentLoaded', function() {
    logDebug('بدء تهيئة نظام المفضلة');
    setTimeout(() => initializeFavoritesSystem(), CONFIG.INIT_DELAY);
});

window.addEventListener('load', function() {
    logDebug('اكتمال تحميل الصفحة');
    if (!systemInitialized) {
        setTimeout(() => initializeFavoritesSystem(), CONFIG.FALLBACK_DELAY);
    }
});

// ===== الدالة الرئيسية لتهيئة النظام =====
function initializeFavoritesSystem() {
    if (systemInitialized) {
        logDebug('النظام مهيأ مسبقاً');
        return;
    }

    logDebug('بدء تهيئة النظام الشامل');

    try {
        // 1. تحميل الكاش المحلي
        loadFavoritesFromCache();

        // 2. تهيئة أزرار المفضلة
        initializeFavoriteButtons();

        // 3. تحديث العدادات فوراً
        updateAllCounters();

        // 4. تحديث حالة المفضلة للمستخدمين المسجلين
        if (isUserAuthenticated()) {
            updateFavoritesStatus();
        }

        // 5. تحديث نهائي للعدادات للتأكد من الظهور الصحيح
        setTimeout(() => {
            updateAllCounters();
            logDebug('تحديث نهائي للعدادات بعد التهيئة');
        }, 100);

        systemInitialized = true;
        logDebug('تم تهيئة النظام بنجاح');

    } catch (error) {
        console.error('خطأ في تهيئة النظام:', error);
        retryInitialization();
    }
}

function retryInitialization() {
    if (retryCount < CONFIG.MAX_RETRY_ATTEMPTS) {
        retryCount++;
        logDebug(`إعادة المحاولة ${retryCount}/${CONFIG.MAX_RETRY_ATTEMPTS}`);
        setTimeout(() => {
            systemInitialized = false;
            initializeFavoritesSystem();
        }, CONFIG.FALLBACK_DELAY * retryCount);
    } else {
        console.error('فشل في تهيئة النظام بعد عدة محاولات');
    }
}

function initializeFavoriteButtons() {
    const buttons = document.querySelectorAll('.btn-favorite');
    logDebug(`تهيئة ${buttons.length} زر مفضلة`);

    buttons.forEach(function(button) {
        if (button.hasAttribute('data-favorites-initialized')) {
            return;
        }

        button.addEventListener('click', handleFavoriteClick);
        button.setAttribute('data-favorites-initialized', 'true');
    });
}

function handleFavoriteClick(e) {
    e.preventDefault();
    e.stopPropagation();

    if (!isUserAuthenticated()) {
        showToast(window.translations?.must_login_to_add_favorites || 'يجب تسجيل الدخول أولاً', 'warning');
        return;
    }

    const favoriteIcon = this.closest('.favorite-icon');
    if (!favoriteIcon) {
        console.error('لم يتم العثور على .favorite-icon');
        return;
    }

    const adId = favoriteIcon.dataset.adId;
    if (!adId) {
        console.error('معرف الإعلان مفقود');
        return;
    }

    // تحديد نوع العملية حسب الصفحة
    if (window.location.pathname.includes('/favorites')) {
        handleFavoritesPageClick(adId, favoriteIcon, this);
    } else {
        toggleFavorite(adId, this);
    }
}

function handleFavoritesPageClick(adId, favoriteIcon, button) {
    const favoriteId = favoriteIcon.dataset.favoriteId || 
                      button.closest('[data-favorite-id]')?.dataset?.favoriteId;
    
    if (favoriteId) {
        removeFavoriteFromPage(adId, favoriteId, button);
    } else {
        toggleFavorite(adId, button);
    }
}

// ===== دوال العمليات الأساسية =====
function toggleFavorite(adId, button) {
    if (isProcessing || (Date.now() - lastRequestTime) < CONFIG.REQUEST_DEBOUNCE_TIME) {
        return;
    }

    isProcessing = true;
    lastRequestTime = Date.now();
    
    setButtonLoading(button, true);
    
    fetch(`/interactions/favorite/${adId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': getCSRFToken(),
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        return response.json();
    })
    .then(data => {
        if (data.success) {
            handleToggleSuccess(data, button, adId);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في تبديل المفضلة:', error);
        showToast(window.translations?.unexpected_error_occurred || 'حدث خطأ غير متوقع', 'error');
    })
    .finally(() => {
        setButtonLoading(button, false);
        isProcessing = false;
    });
}

function handleToggleSuccess(data, button, adId) {
    logDebug('تم تبديل المفضلة بنجاح:', data);
    
    // تحديث الكاش المحلي
    favoritesCache.set(adId, data.is_favorited);
    currentFavoritesCount = data.favorites_count || 0;
    saveFavoritesToCache();
    
    // تحديث حالة الزر
    updateButtonState(button, data.is_favorited);
    
    // تحديث العدادات
    updateAllCounters();
    
    // إظهار رسالة نجاح
    showToast(data.message, 'success');
}

// ===== دوال تحديث الواجهة المحسنة =====
function updateButtonState(button, isFavorited) {
    // إضافة تأثير النقر
    addClickAnimation(button);

    if (isFavorited) {
        // تأثير إضافة المفضلة
        setTimeout(() => {
            button.classList.add('favorited');
            button.title = window.translations?.remove_from_favorites || 'إزالة من المفضلة';
            addFavoriteAddedEffect(button);
        }, 100);
    } else {
        // تأثير إزالة المفضلة
        addFavoriteRemovedEffect(button);
        setTimeout(() => {
            button.classList.remove('favorited');
            button.title = window.translations?.add_to_favorites || 'إضافة إلى المفضلة';
        }, 200);
    }
}

function addClickAnimation(button) {
    button.classList.add('clicked');
    setTimeout(() => {
        button.classList.remove('clicked');
    }, 600);
}

function addFavoriteAddedEffect(button) {
    // تأثير القلب المتوهج
    button.style.animation = 'favoriteAdded 0.6s ease-out';

    // إضافة جسيمات متحركة
    createHeartParticles(button);

    setTimeout(() => {
        button.style.animation = '';
    }, 600);
}

function addFavoriteRemovedEffect(button) {
    // تأثير الاهتزاز اللطيف
    button.classList.add('shake-gentle');
    setTimeout(() => {
        button.classList.remove('shake-gentle');
    }, 500);
}

function createHeartParticles(button) {
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    for (let i = 0; i < 6; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            particle.innerHTML = '♥';
            particle.style.cssText = `
                position: fixed;
                left: ${centerX}px;
                top: ${centerY}px;
                color: #ff6b6b;
                font-size: 12px;
                pointer-events: none;
                z-index: 9999;
                animation: heartFloat 1.5s ease-out forwards;
                transform: translate(-50%, -50%);
            `;

            document.body.appendChild(particle);

            // حركة عشوائية للجسيمات
            const angle = (i * 60) * Math.PI / 180;
            const distance = 50 + Math.random() * 30;
            const endX = centerX + Math.cos(angle) * distance;
            const endY = centerY + Math.sin(angle) * distance - 30;

            particle.style.setProperty('--end-x', `${endX - centerX}px`);
            particle.style.setProperty('--end-y', `${endY - centerY}px`);

            setTimeout(() => {
                particle.remove();
            }, 1500);
        }, i * 100);
    }
}

// إضافة CSS للجسيمات المتحركة
if (!document.getElementById('heart-particles-style')) {
    const style = document.createElement('style');
    style.id = 'heart-particles-style';
    style.textContent = `
        @keyframes heartFloat {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 1;
            }
            50% {
                transform: translate(calc(-50% + var(--end-x, 0px)), calc(-50% + var(--end-y, 0px))) scale(1.2);
                opacity: 0.8;
            }
            100% {
                transform: translate(calc(-50% + var(--end-x, 0px)), calc(-50% + var(--end-y, 0px))) scale(0);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

function setButtonLoading(button, isLoading) {
    if (isLoading) {
        // تأثير التحميل المحسن
        button.classList.add('loading');
        button.style.pointerEvents = 'none';

        // إضافة تأثير النبض أثناء التحميل
        button.style.animation = 'heartPulse 1.2s ease-in-out infinite';

        // تغيير النص مؤقتاً
        const icon = button.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-spinner';
        }

        // إضافة تأثير الوهج
        button.classList.add('backlight-effect');

    } else {
        // إزالة تأثيرات التحميل
        button.classList.remove('loading', 'backlight-effect');
        button.style.pointerEvents = 'auto';
        button.style.animation = '';

        // استعادة أيقونة القلب
        const icon = button.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-heart';
        }

        // تأثير انتهاء التحميل
        button.style.transform = 'scale(1.05)';
        setTimeout(() => {
            button.style.transform = '';
        }, 200);
    }
}

function updateAllCounters() {
    // البحث عن جميع عدادات المفضلة
    const counters = document.querySelectorAll('.favorites-count');
    logDebug(`تحديث ${counters.length} عداد مفضلة إلى: ${currentFavoritesCount}`);

    counters.forEach((counter, index) => {
        // إضافة تأثير التحديث
        addUpdateAnimation(counter);

        // تحديث النص مع تأثير انتقالي
        setTimeout(() => {
            counter.textContent = currentFavoritesCount;
        }, 150);

        const badge = counter.closest('.badge');
        const navLink = counter.closest('.nav-link');
        const dropdownItem = counter.closest('.dropdown-item');

        // إظهار العداد دائماً إذا كان هناك مفضلة
        if (currentFavoritesCount > 0) {
            // تأثير الظهور التدريجي
            showCounterWithAnimation(counter, badge);

            logDebug(`عداد ${index + 1}: تم إظهار العداد والشارة - العدد: ${currentFavoritesCount}`);

            // تحديث خاص لعدادات شريط التنقل
            if (navLink || dropdownItem) {
                addNavbarGlowEffect(badge || counter);
                logDebug(`عداد شريط التنقل ${index + 1}: تم إظهاره - العدد: ${currentFavoritesCount}`);
            }
        } else {
            // إخفاء العداد مع تأثير انتقالي
            hideCounterWithAnimation(counter, badge);

            logDebug(`عداد ${index + 1}: تم إخفاء العداد - العدد: ${currentFavoritesCount}`);

            // تحديث خاص لعدادات شريط التنقل
            if (navLink || dropdownItem) {
                logDebug(`عداد شريط التنقل ${index + 1}: تم إخفاؤه - العدد: ${currentFavoritesCount}`);
            }
        }
    });
}

// ===== دوال التأثيرات البصرية المحسنة =====

function addUpdateAnimation(element) {
    element.classList.add('updating');
    setTimeout(() => {
        element.classList.remove('updating');
    }, 800);
}

function showCounterWithAnimation(counter, badge) {
    const targetElement = badge || counter;

    // إزالة أي تأثيرات سابقة
    targetElement.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    targetElement.style.transform = 'scale(0.8)';
    targetElement.style.opacity = '0';
    targetElement.style.display = 'inline';

    // تطبيق التأثير
    setTimeout(() => {
        targetElement.style.transform = 'scale(1.1)';
        targetElement.style.opacity = '1';

        setTimeout(() => {
            targetElement.style.transform = 'scale(1)';
        }, 200);
    }, 50);
}

function hideCounterWithAnimation(counter, badge) {
    const targetElement = badge || counter;

    targetElement.style.transition = 'all 0.3s ease';
    targetElement.style.transform = 'scale(0.8)';
    targetElement.style.opacity = '0';

    setTimeout(() => {
        targetElement.style.display = 'none';
        targetElement.style.transform = 'scale(1)';
        targetElement.style.opacity = '1';
    }, 300);
}

function addNavbarGlowEffect(element) {
    element.classList.add('favorites-glow');

    // إضافة تأثير خاص لرابط المفضلة في شريط التنقل
    const navLink = element.closest('.favorites-nav-link');
    if (navLink) {
        navLink.style.background = 'rgba(255, 107, 107, 0.2)';
        navLink.style.borderRadius = '8px';

        setTimeout(() => {
            navLink.style.background = '';
        }, 1000);
    }

    setTimeout(() => {
        element.classList.remove('favorites-glow');
    }, 1000);
}

// ===== تحسينات إضافية للأداء والتوافق =====

// تحسين الأداء بتأخير التأثيرات غير الضرورية
function optimizeAnimations() {
    // تقليل التأثيرات على الأجهزة الضعيفة
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
                          navigator.deviceMemory <= 2 ||
                          /Android.*Chrome\/[0-5]/.test(navigator.userAgent);

    if (isLowEndDevice) {
        document.documentElement.style.setProperty('--animation-duration', '0.2s');
        document.documentElement.style.setProperty('--transition-duration', '0.1s');
    }
}

// تحسين التأثيرات للمس
function optimizeForTouch() {
    if ('ontouchstart' in window) {
        // تقليل تأخير hover على الأجهزة اللمسية
        const style = document.createElement('style');
        style.textContent = `
            .btn-favorite:hover {
                transition-delay: 0s !important;
            }
            .favorites-nav-link:hover {
                transition-delay: 0s !important;
            }
        `;
        document.head.appendChild(style);
    }
}

// تطبيق التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    optimizeAnimations();
    optimizeForTouch();
    addFallbackClasses();
});

// إضافة classes fallback للمتصفحات القديمة
function addFallbackClasses() {
    // التحقق من دعم :has() selector
    const supportsHas = CSS.supports('selector(:has(*))');

    if (!supportsHas) {
        // إضافة class للمتصفحات التي لا تدعم :has()
        const updateNavLinkClass = () => {
            const navLinks = document.querySelectorAll('.favorites-nav-link');
            navLinks.forEach(link => {
                const counter = link.querySelector('.favorites-count');
                if (counter && counter.style.display !== 'none' && counter.textContent !== '0') {
                    link.classList.add('has-favorites');
                } else {
                    link.classList.remove('has-favorites');
                }
            });
        };

        // تشغيل فوري
        updateNavLinkClass();

        // مراقبة التغييرات
        const observer = new MutationObserver(updateNavLinkClass);
        document.querySelectorAll('.favorites-nav-link').forEach(link => {
            observer.observe(link, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['style']
            });
        });
    }
}

// ===== تحديث حالة المفضلة =====
function updateFavoritesStatus() {
    if (!isUserAuthenticated()) {
        return;
    }

    const favoriteButtons = document.querySelectorAll('.btn-favorite');
    const adIds = [];

    favoriteButtons.forEach(button => {
        const favoriteIcon = button.closest('.favorite-icon');
        if (favoriteIcon && favoriteIcon.dataset.adId) {
            const adId = favoriteIcon.dataset.adId;
            if (!adIds.includes(adId)) {
                adIds.push(adId);
            }
        }
    });

    if (adIds.length === 0) {
        logDebug('لا توجد أزرار مفضلة للتحديث');
        return;
    }

    logDebug(`تحديث حالة المفضلة لـ ${adIds.length} إعلان`);

    // استخدام الكاش أولاً
    let hasUpdatesFromCache = false;
    favoriteButtons.forEach(button => {
        const favoriteIcon = button.closest('.favorite-icon');
        if (favoriteIcon && favoriteIcon.dataset.adId) {
            const adId = favoriteIcon.dataset.adId;
            if (favoritesCache.has(adId)) {
                updateButtonState(button, favoritesCache.get(adId));
                hasUpdatesFromCache = true;
            }
        }
    });

    if (hasUpdatesFromCache) {
        logDebug('تم تحديث بعض الأزرار من الكاش');
    }

    // طلب التحديث من الخادم
    fetch('/dashboard/api/favorites/check-multiple', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': getCSRFToken(),
            'Accept': 'application/json'
        },
        body: JSON.stringify({ ad_ids: adIds })
    })
    .then(response => {
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        return response.json();
    })
    .then(data => {
        if (data.success) {
            logDebug('تم تحديث حالة المفضلة من الخادم:', data.favorites);

            // تحديث الكاش والأزرار
            favoriteButtons.forEach(button => {
                const favoriteIcon = button.closest('.favorite-icon');
                if (favoriteIcon && favoriteIcon.dataset.adId) {
                    const adId = favoriteIcon.dataset.adId;
                    if (data.favorites[adId] !== undefined) {
                        favoritesCache.set(adId, data.favorites[adId]);
                        updateButtonState(button, data.favorites[adId]);
                    }
                }
            });

            // تحديث العداد
            if (data.favorites_count !== undefined) {
                currentFavoritesCount = data.favorites_count;
                updateAllCounters();
            }

            // حفظ الكاش
            saveFavoritesToCache();
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث حالة المفضلة:', error);
    });
}

// ===== إزالة من صفحة المفضلة =====
function removeFavoriteFromPage(adId, favoriteId, button) {
    if (!confirm(window.translations?.confirm_remove_favorite || 'هل أنت متأكد من إزالة هذا الإعلان من المفضلة؟')) {
        return;
    }

    logDebug('إزالة المفضلة:', { adId, favoriteId });

    // البحث عن البطاقة
    let card = document.querySelector(`[data-favorite-id="${favoriteId}"]`);
    if (!card) {
        card = document.querySelector(`[data-ad-id="${adId}"]`);
    }
    if (!card && button) {
        card = button.closest('.favorite-card-wrapper, .card, .col');
    }

    if (card) {
        card.style.opacity = '0.6';
        card.style.pointerEvents = 'none';
    }

    fetch(`/interactions/favorite/${adId}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': getCSRFToken(),
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            logDebug('تم حذف المفضلة بنجاح');

            // تحديث الكاش
            favoritesCache.set(adId, false);
            currentFavoritesCount = data.favorites_count || 0;
            saveFavoritesToCache();

            // إزالة البطاقة بتأثير سلس
            if (card) {
                card.style.transition = 'all 0.4s ease';
                card.style.opacity = '0';
                card.style.transform = 'scale(0.8) translateY(-20px)';

                setTimeout(() => {
                    card.remove();
                    updateAllCounters();

                    // فحص إذا كانت هذه آخر بطاقة
                    const remainingCards = document.querySelectorAll('.favorite-card-wrapper, [data-favorite-id], .card[data-ad-id]');
                    if (remainingCards.length === 0 || currentFavoritesCount === 0) {
                        showEmptyFavoritesMessage();
                    }
                }, 400);
            } else {
                updateAllCounters();
                if (currentFavoritesCount === 0) {
                    showEmptyFavoritesMessage();
                }
            }

            showToast(data.message, 'success');
        } else {
            console.error('فشل في حذف المفضلة:', data.message);
            if (card) {
                card.style.opacity = '1';
                card.style.pointerEvents = 'auto';
            }
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في حذف المفضلة:', error);
        if (card) {
            card.style.opacity = '1';
            card.style.pointerEvents = 'auto';
        }
        showToast(window.translations?.unexpected_error || 'حدث خطأ غير متوقع', 'error');
    });
}

// ===== مسح جميع المفضلة =====
function clearAllFavorites() {
    if (!confirm(window.translations?.confirm_clear_all_favorites || 'هل أنت متأكد من مسح جميع الإعلانات المفضلة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    logDebug('بدء مسح جميع المفضلة');

    const allCards = document.querySelectorAll('.favorite-card-wrapper, [data-favorite-id], .card[data-ad-id]');

    allCards.forEach(card => {
        card.style.opacity = '0.6';
        card.style.pointerEvents = 'none';
    });

    fetch('/favorites/clear', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': getCSRFToken(),
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            logDebug('تم مسح جميع المفضلة بنجاح');

            // تحديث الكاش
            favoritesCache.clear();
            currentFavoritesCount = 0;
            saveFavoritesToCache();

            // تحديث العدادات
            updateAllCounters();

            // إزالة جميع البطاقات بتأثير سلس
            if (allCards.length > 0) {
                allCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.transition = 'all 0.4s ease';
                        card.style.opacity = '0';
                        card.style.transform = 'scale(0.8) translateY(-20px)';

                        setTimeout(() => {
                            card.remove();

                            // إظهار رسالة فارغة إذا كانت هذه آخر بطاقة
                            if (index === allCards.length - 1) {
                                setTimeout(() => showEmptyFavoritesMessage(), 200);
                            }
                        }, 400);
                    }, index * 100);
                });
            } else {
                showEmptyFavoritesMessage();
            }

            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
            allCards.forEach(card => {
                card.style.opacity = '1';
                card.style.pointerEvents = 'auto';
            });
        }
    })
    .catch(error => {
        console.error('خطأ في مسح جميع المفضلة:', error);
        showToast(window.translations?.unexpected_error || 'حدث خطأ غير متوقع', 'error');

        allCards.forEach(card => {
            card.style.opacity = '1';
            card.style.pointerEvents = 'auto';
        });
    });
}

// ===== إظهار رسالة المفضلة الفارغة =====
function showEmptyFavoritesMessage() {
    logDebug('إظهار رسالة المفضلة الفارغة');

    let targetElement = document.querySelector('#favorites-row') ||
                       document.querySelector('#favorites-container .row') ||
                       document.querySelector('.ads-grid .row');

    if (targetElement) {
        const noFavoritesText = window.translations?.no_favorites_yet || 'لا توجد إعلانات مفضلة';
        const noFavoritesAddedText = window.translations?.no_favorites_added_yet || 'لم تقم بإضافة أي إعلانات إلى المفضلة بعد';
        const browseAdsText = window.translations?.browse_ads || 'تصفح الإعلانات';

        targetElement.innerHTML = `
            <div class="col-12">
                <div class="empty-favorites text-center py-5">
                    <div class="empty-icon mb-4">
                        <i class="fas fa-heart text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                    </div>
                    <h4 class="text-muted mb-3">${noFavoritesText}</h4>
                    <p class="text-muted mb-4">${noFavoritesAddedText}</p>
                    <a href="/ads" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        ${browseAdsText}
                    </a>
                </div>
            </div>
        `;

        logDebug('تم إظهار رسالة المفضلة الفارغة بنجاح');
        updateAllCounters();
    } else {
        console.error('لم يتم العثور على عنصر المفضلة المناسب');
    }
}

// ===== إعادة تهيئة النظام =====
function reinitializeFavorites() {
    logDebug('إعادة تهيئة النظام');
    systemInitialized = false;
    retryCount = 0;
    initializeFavoritesSystem();
}

// ===== تصدير النظام للاستخدام العام =====
window.FavoritesSystem = {
    // العمليات الأساسية
    toggle: toggleFavorite,
    removeFromPage: removeFavoriteFromPage,
    clearAll: clearAllFavorites,

    // إدارة الحالة
    updateStatus: updateFavoritesStatus,
    updateCounters: updateAllCounters,
    reinitialize: reinitializeFavorites,

    // الرسائل والواجهة
    showEmptyMessage: showEmptyFavoritesMessage,

    // معلومات النظام
    isInitialized: () => systemInitialized,
    getCount: () => currentFavoritesCount,
    getCache: () => favoritesCache
};

logDebug('تم تصدير نظام المفضلة للاستخدام العام');
