@php
    try {
        // الحصول على إعدادات الإعلانات من النظام الموحد
        $announcementSettings = (object) [
            'is_enabled' => \App\Models\Setting::get('announcements_enabled', true),
            'animation_type' => \App\Models\Setting::get('announcements_animation_type', 'rotation'),
            'transition_duration' => \App\Models\Setting::get('announcements_transition_duration', 5),
            'scroll_speed' => \App\Models\Setting::get('announcements_scroll_speed', 30)
        ];
        $announcements = \App\Models\Announcement::active()->ordered()->get();
    } catch (\Exception $e) {
        $announcementSettings = null;
        $announcements = collect();
    }
@endphp

@if($announcementSettings && $announcementSettings->is_enabled && $announcements->count() > 0)
<!-- شريط الإعلانات المتحرك البسيط -->
<div class="announcement-bar" id="announcementBar"
     data-animation-type="{{ $announcementSettings->animation_type }}"
     data-transition-duration="{{ $announcementSettings->transition_duration }}"
     data-scroll-speed="{{ $announcementSettings->scroll_speed }}">

    <!-- محتوى الإعلانات -->
    <div class="announcement-content-wrapper py-3" id="announcementContent">
        <div class="container-fluid px-0">
            <div class="row align-items-center mx-0">
                <div class="col-12 px-0">
                    <!-- وضع التبديل التلقائي -->
                    <div class="announcement-slider" id="rotationMode">
                        <div class="announcement-content arabic-text">
                            @foreach($announcements as $index => $announcement)
                                <div class="announcement-item {{ $index === 0 ? 'active' : '' }}">
                                    <i class="{{ $announcement->icon }} {{ $announcement->color }} me-2"></i>
                                    <strong>{{ $announcement->title }}</strong>
                                    @if($announcement->content)
                                        <span class="mx-2">-</span>
                                        {{ $announcement->content }}
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- وضع التمرير الأفقي -->
                    <div class="announcement-marquee arabic-text" id="marqueeMode" style="display: none;">
                        <div class="marquee-content" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
                            <span class="marquee-text">
                                @foreach($announcements as $announcement)
                                    <i class="{{ $announcement->icon }} {{ $announcement->color }} me-2"></i>
                                    <strong>{{ $announcement->title }}</strong>
                                    @if($announcement->content)
                                        <span class="mx-2">-</span>
                                        {{ $announcement->content }}
                                    @endif
                                    @if(!$loop->last)
                                        <i class="fas fa-circle text-warning mx-3" style="font-size: 0.5rem;"></i>
                                    @endif
                                @endforeach
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* شريط الإعلانات المتحرك البسيط */
.announcement-bar {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e40af 100%);
    color: white;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 2px 10px rgba(37, 99, 235, 0.2);
    width: 100%;
    margin: 0;
    padding: 0;
}

.announcement-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shimmer 4s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.announcement-slider {
    position: relative;
    height: 40px;
    overflow: hidden;
}

.announcement-content {
    position: relative;
    height: 100%;
}

.announcement-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
    font-size: 1rem;
    font-weight: 500;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.announcement-item.active {
    opacity: 1;
    transform: translateY(0);
}

.announcement-item i {
    margin-left: 8px;
    font-size: 1.1rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* وضع التمرير الأفقي */
.announcement-marquee {
    height: 40px;
    overflow: hidden;
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
}

.marquee-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.marquee-text {
    display: inline-block;
    white-space: nowrap;
    animation: marqueeScroll var(--scroll-speed, 20s) linear infinite;
    font-size: 1rem;
    font-weight: 500;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.marquee-text i {
    margin-left: 8px;
    font-size: 1.1rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* تمرير من اليمين لليسار للعربية */
[dir="rtl"] .marquee-text {
    animation: marqueeScrollRTL var(--scroll-speed, 20s) linear infinite;
}

@keyframes marqueeScroll {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

@keyframes marqueeScrollRTL {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* تحسين الاستجابة للشريط */
@media (max-width: 768px) {
    .announcement-item {
        font-size: 0.9rem;
        padding: 0 1rem;
        text-align: center;
    }

    .marquee-text {
        font-size: 0.9rem;
    }

    .announcement-slider,
    .announcement-marquee {
        height: 35px;
    }
}

@media (max-width: 480px) {
    .announcement-item {
        font-size: 0.85rem;
    }

    .marquee-text {
        font-size: 0.85rem;
    }

    .announcement-content-wrapper {
        padding: 0.75rem 0 !important;
    }
}

/* ضمان العرض الكامل للشريط */
.announcement-content-wrapper {
    width: 100% !important;
    margin: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.announcement-content-wrapper .container-fluid {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
}

.announcement-content-wrapper .row {
    margin: 0 !important;
    width: 100% !important;
}

.announcement-content-wrapper .col-12 {
    padding: 0 !important;
    width: 100% !important;
}
</style>

<script>
// شريط الإعلانات المتحرك المحسن
let announcementInterval;
let currentAnnouncementIndex = 0;
let isMarqueeMode = false;
let isCollapsed = false;

document.addEventListener('DOMContentLoaded', function() {
    // مسح حالة الإغلاق إذا تم تفعيل الشريط من الإدارة
    const announcementBar = document.getElementById('announcementBar');
    if (announcementBar) {
        localStorage.removeItem('announcementBarClosed');
    }

    initializeAnnouncementBar();
    loadUserPreferences();

    // مراقبة تحديثات الإعدادات
    window.addEventListener('storage', function(e) {
        if (e.key === 'announcement_settings_updated') {
            // إعادة تحميل الصفحة عند تحديث الإعدادات
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    });
});

function initializeAnnouncementBar() {
    const announcementBar = document.getElementById('announcementBar');
    if (!announcementBar) return;

    const announcementItems = document.querySelectorAll('.announcement-item');
    const transitionDuration = parseInt(announcementBar.dataset.transitionDuration) * 1000 || 4000;
    const animationType = announcementBar.dataset.animationType || 'rotation';

    // تطبيق الإعدادات الافتراضية
    const scrollSpeed = parseInt(announcementBar.dataset.scrollSpeed) || 20;
    document.documentElement.style.setProperty('--scroll-speed', scrollSpeed + 's');

    const rotationMode = document.getElementById('rotationMode');
    const marqueeMode = document.getElementById('marqueeMode');

    // تطبيق نوع الحركة من الإعدادات
    if (animationType === 'marquee') {
        // وضع التمرير الأفقي
        if (rotationMode) rotationMode.style.display = 'none';
        if (marqueeMode) marqueeMode.style.display = 'block';
        isMarqueeMode = true;
    } else {
        // وضع التبديل التلقائي
        if (rotationMode) rotationMode.style.display = 'block';
        if (marqueeMode) marqueeMode.style.display = 'none';
        isMarqueeMode = false;

        function showNextAnnouncement() {
            if (isMarqueeMode || announcementItems.length === 0) return;

            // إخفاء العنصر الحالي
            announcementItems[currentAnnouncementIndex].classList.remove('active');

            // الانتقال للعنصر التالي
            currentAnnouncementIndex = (currentAnnouncementIndex + 1) % announcementItems.length;

            // إظهار العنصر الجديد
            announcementItems[currentAnnouncementIndex].classList.add('active');
        }

        // تشغيل التبديل التلقائي
        if (announcementItems.length > 0) {
            announcementInterval = setInterval(showNextAnnouncement, transitionDuration);
        }
    }
}

// تحميل تفضيلات المستخدم (مبسط)
function loadUserPreferences() {
    // لا حاجة لتحميل تفضيلات معقدة - الشريط بسيط الآن
}
</script>
@endif
