<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Category;
use App\Models\Ad;
use App\Models\Interaction;
use App\Models\SystemLog;
use App\Models\Setting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class TestOptimizedStructure extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:optimized-structure {--create-sample-data : إنشاء بيانات تجريبية}';

    /**
     * The console command description.
     */
    protected $description = 'اختبار الهيكل المحسن للتأكد من عمل جميع الوظائف';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 بدء اختبار الهيكل المحسن...');

        $errors = [];
        $warnings = [];

        try {
            // 1. اختبار إنشاء المستخدمين
            $this->info('👤 اختبار إنشاء المستخدمين...');
            $user = $this->testUserCreation();
            if ($user) {
                $this->line('✅ تم إنشاء مستخدم تجريبي بنجاح');
            } else {
                $errors[] = 'فشل في إنشاء مستخدم تجريبي';
            }

            // 2. اختبار إنشاء التصنيفات
            $this->info('📂 اختبار إنشاء التصنيفات...');
            $category = $this->testCategoryCreation();
            if ($category) {
                $this->line('✅ تم إنشاء تصنيف تجريبي بنجاح');
            } else {
                $errors[] = 'فشل في إنشاء تصنيف تجريبي';
            }

            // 3. اختبار إنشاء الإعلانات
            $this->info('📢 اختبار إنشاء الإعلانات...');
            $ad = $this->testAdCreation($user, $category);
            if ($ad) {
                $this->line('✅ تم إنشاء إعلان تجريبي بنجاح');
            } else {
                $errors[] = 'فشل في إنشاء إعلان تجريبي';
            }

            // 4. اختبار التفاعلات
            $this->info('❤️ اختبار التفاعلات...');
            $this->testInteractions($user, $ad);

            // 5. اختبار السجلات
            $this->info('📋 اختبار السجلات...');
            $this->testSystemLogs($user);

            // 6. اختبار الإعدادات
            $this->info('⚙️ اختبار الإعدادات...');
            $this->testSettings();

            // 7. اختبار العلاقات
            $this->info('🔗 اختبار العلاقات...');
            $this->testRelationships($user, $ad);

            // 8. إنشاء بيانات تجريبية إضافية
            if ($this->option('create-sample-data')) {
                $this->info('📊 إنشاء بيانات تجريبية إضافية...');
                $this->createSampleData();
            }

        } catch (\Exception $e) {
            $errors[] = 'خطأ عام: ' . $e->getMessage();
        }

        // عرض النتائج
        $this->displayResults($errors, $warnings);

        return empty($errors) ? 0 : 1;
    }

    private function testUserCreation()
    {
        try {
            return User::create([
                'name' => 'مستخدم تجريبي',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '123456789',
                'whatsapp' => '123456789',
                'preferred_currency' => 'YER',
                'is_active' => true,
            ]);
        } catch (\Exception $e) {
            $this->error('خطأ في إنشاء المستخدم: ' . $e->getMessage());
            return null;
        }
    }

    private function testCategoryCreation()
    {
        try {
            return Category::create([
                'name_ar' => 'تصنيف تجريبي',
                'name_en' => 'Test Category',
                'slug' => 'test-category',
                'description_ar' => 'وصف التصنيف التجريبي',
                'description_en' => 'Test category description',
                'is_active' => true,
                'sort_order' => 1,
            ]);
        } catch (\Exception $e) {
            $this->error('خطأ في إنشاء التصنيف: ' . $e->getMessage());
            return null;
        }
    }

    private function testAdCreation($user, $category)
    {
        try {
            return Ad::create([
                'user_id' => $user->id,
                'category_id' => $category->id,
                'title_ar' => 'إعلان تجريبي',
                'title_en' => 'Test Ad',
                'description_ar' => 'وصف الإعلان التجريبي',
                'description_en' => 'Test ad description',
                'slug' => 'test-ad-' . time(),
                'price' => 100.00,
                'currency' => 'YER',
                'price_type' => 'fixed',
                'location' => 'صنعاء',
                'city' => 'صنعاء',
                'country' => 'Yemen',
                'status' => 'active',
                'expires_at' => now()->addDays(30),
            ]);
        } catch (\Exception $e) {
            $this->error('خطأ في إنشاء الإعلان: ' . $e->getMessage());
            return null;
        }
    }

    private function testInteractions($user, $ad)
    {
        try {
            // اختبار المفضلة
            $favorite = Interaction::addFavorite($user->id, $ad->id);
            $this->line('✅ تم اختبار المفضلة بنجاح');

            // اختبار التقييم
            $rating = Interaction::addRating($user->id, $ad->id, 5, 'تقييم ممتاز');
            $this->line('✅ تم اختبار التقييم بنجاح');

            // اختبار التعليق
            $comment = Interaction::addComment($user->id, $ad->id, 'تعليق تجريبي');
            $this->line('✅ تم اختبار التعليق بنجاح');

            // اختبار المشاهدة
            $view = Interaction::recordView($user->id, $ad->id);
            $this->line('✅ تم اختبار المشاهدة بنجاح');

        } catch (\Exception $e) {
            $this->error('خطأ في اختبار التفاعلات: ' . $e->getMessage());
        }
    }

    private function testSystemLogs($user)
    {
        try {
            // اختبار سجل الأمان
            SystemLog::logSecurity('test_login', 'اختبار تسجيل الدخول', null, $user->id);
            $this->line('✅ تم اختبار سجل الأمان بنجاح');

            // اختبار سجل البحث
            SystemLog::logSearch('كلمة بحث تجريبية', 5, $user->id);
            $this->line('✅ تم اختبار سجل البحث بنجاح');

            // اختبار سجل إداري
            SystemLog::logAdminAction('test_action', 'إجراء إداري تجريبي');
            $this->line('✅ تم اختبار السجل الإداري بنجاح');

        } catch (\Exception $e) {
            $this->error('خطأ في اختبار السجلات: ' . $e->getMessage());
        }
    }

    private function testSettings()
    {
        try {
            // اختبار إنشاء إعداد
            Setting::set('test_setting', 'قيمة تجريبية', 'string', 'general', 'إعداد تجريبي');
            $this->line('✅ تم إنشاء إعداد تجريبي بنجاح');

            // اختبار قراءة الإعداد
            $value = Setting::get('test_setting');
            if ($value === 'قيمة تجريبية') {
                $this->line('✅ تم قراءة الإعداد بنجاح');
            } else {
                $this->warn('⚠️ قيمة الإعداد غير صحيحة');
            }

        } catch (\Exception $e) {
            $this->error('خطأ في اختبار الإعدادات: ' . $e->getMessage());
        }
    }

    private function testRelationships($user, $ad)
    {
        try {
            // اختبار علاقة المستخدم مع الإعلانات
            $userAds = $user->ads()->count();
            $this->line("✅ المستخدم لديه {$userAds} إعلان");

            // اختبار علاقة الإعلان مع التفاعلات
            $adInteractions = $ad->interactions()->count();
            $this->line("✅ الإعلان لديه {$adInteractions} تفاعل");

            // اختبار علاقة المستخدم مع التفاعلات
            $userInteractions = $user->interactions()->count();
            $this->line("✅ المستخدم لديه {$userInteractions} تفاعل");

        } catch (\Exception $e) {
            $this->error('خطأ في اختبار العلاقات: ' . $e->getMessage());
        }
    }

    private function createSampleData()
    {
        try {
            // إنشاء المزيد من البيانات التجريبية
            $this->line('📊 إنشاء بيانات تجريبية إضافية...');
            
            // يمكن إضافة المزيد من البيانات التجريبية هنا
            
        } catch (\Exception $e) {
            $this->error('خطأ في إنشاء البيانات التجريبية: ' . $e->getMessage());
        }
    }

    private function displayResults($errors, $warnings)
    {
        $this->newLine();
        $this->info('📊 نتائج الاختبار:');

        if (empty($errors) && empty($warnings)) {
            $this->info('🎉 جميع الاختبارات نجحت! الهيكل المحسن يعمل بشكل مثالي.');
        } else {
            if (!empty($errors)) {
                $this->error('❌ الأخطاء المكتشفة:');
                foreach ($errors as $error) {
                    $this->line("  - {$error}");
                }
            }

            if (!empty($warnings)) {
                $this->warn('⚠️ التحذيرات:');
                foreach ($warnings as $warning) {
                    $this->line("  - {$warning}");
                }
            }
        }
    }
}
