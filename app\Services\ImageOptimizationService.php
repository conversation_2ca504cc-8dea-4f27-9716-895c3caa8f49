<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
// use Intervention\Image\ImageManager;
// use Intervention\Image\Drivers\Gd\Driver;

/**
 * خدمة تحسين الصور
 * تدير ضغط وتحسين الصور لتحسين الأداء
 */
class ImageOptimizationService
{
    protected $imageManager;

    // أحجام الصور المختلفة - مقاسات احترافية
    const SIZES = [
        'thumbnail' => ['width' => 200, 'height' => 150],   // 4:3 للبطاقات الصغيرة
        'small' => ['width' => 400, 'height' => 300],       // 4:3 للعرض السريع
        'medium' => ['width' => 800, 'height' => 600],      // 4:3 للعرض الرئيسي
        'large' => ['width' => 1200, 'height' => 900],     // 4:3 للعرض الكامل
        'hero' => ['width' => 1920, 'height' => 1080],     // 16:9 للصور البطولية
    ];

    // جودة الضغط - محسنة للأحجام الجديدة
    const QUALITY = [
        'thumbnail' => 75,  // جودة جيدة للصور الصغيرة
        'small' => 80,      // جودة عالية للعرض السريع
        'medium' => 85,     // جودة عالية للعرض الرئيسي
        'large' => 90,      // جودة ممتازة للعرض الكامل
        'hero' => 95,       // جودة فائقة للصور البطولية
    ];

    public function __construct()
    {
        // TODO: Install Intervention Image package
        // $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * تحسين وحفظ الصورة بأحجام مختلفة
     * TODO: Install Intervention Image package for full functionality
     */
    public static function optimizeAndSave($file, $directory = 'ads')
    {
        try {
            // إنشاء اسم فريد للملف
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $basePath = $directory . '/' . date('Y/m');

            // التأكد من وجود المجلد
            Storage::makeDirectory("public/{$basePath}");

            $results = [];

            // حفظ الصورة الأصلية (بدون تحسين حتى يتم تثبيت المكتبة)
            $originalPath = "{$basePath}/original_{$filename}";
            $file->storeAs("public/{$basePath}", "original_{$filename}");
            $results['original'] = $originalPath;

            // TODO: إضافة تحسين الصور عند تثبيت Intervention Image
            // إنشاء أحجام مختلفة (مؤقتاً نحفظ نفس الصورة)
            foreach (self::SIZES as $sizeName => $dimensions) {
                $sizePath = "{$basePath}/{$sizeName}_{$filename}";
                $file->storeAs("public/{$basePath}", "{$sizeName}_{$filename}");
                $results[$sizeName] = $sizePath;
            }

            Log::info('Image optimized successfully', ['filename' => $filename, 'sizes' => count($results)]);

            return [
                'success' => true,
                'filename' => $filename,
                'paths' => $results,
                'main_path' => $results['medium'], // الحجم الافتراضي للعرض
                'display_path' => $results['medium'] // المسار للعرض في الواجهة
            ];

        } catch (\Exception $e) {
            Log::error('Image optimization failed', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * حذف جميع أحجام الصورة
     */
    public static function deleteImageSizes($imagePath)
    {
        try {
            $pathInfo = pathinfo($imagePath);
            $directory = $pathInfo['dirname'];
            $filename = $pathInfo['filename'];
            $extension = $pathInfo['extension'];

            // حذف الصورة الأصلية
            if (Storage::exists('public/' . $imagePath)) {
                Storage::delete('public/' . $imagePath);
            }

            // حذف جميع الأحجام
            foreach (array_keys(self::SIZES) as $sizeName) {
                $sizePath = $directory . '/' . $sizeName . '_' . $filename . '.' . $extension;
                if (Storage::exists('public/' . $sizePath)) {
                    Storage::delete('public/' . $sizePath);
                }
            }

            // حذف الصورة الأصلية إذا كانت موجودة
            $originalPath = $directory . '/original_' . $filename . '.' . $extension;
            if (Storage::exists('public/' . $originalPath)) {
                Storage::delete('public/' . $originalPath);
            }

            Log::info('Image sizes deleted successfully', ['path' => $imagePath]);
            return true;

        } catch (\Exception $e) {
            Log::error('Failed to delete image sizes', [
                'error' => $e->getMessage(),
                'path' => $imagePath
            ]);
            return false;
        }
    }

    /**
     * الحصول على مسار الصورة بحجم محدد
     */
    public function getImagePath($originalPath, $size = 'medium')
    {
        if (!$originalPath) {
            return null;
        }

        $pathInfo = pathinfo($originalPath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];

        // إزالة البادئة إذا كانت موجودة
        $cleanFilename = preg_replace('/^(original|thumbnail|small|medium|large)_/', '', $filename);

        $sizePath = $directory . '/' . $size . '_' . $cleanFilename . '.' . $extension;

        // التحقق من وجود الملف
        if (Storage::exists('public/' . $sizePath)) {
            return $sizePath;
        }

        // إرجاع المسار الأصلي إذا لم يوجد الحجم المطلوب
        return $originalPath;
    }

    /**
     * ضغط صورة موجودة
     */
    public function compressExistingImage($imagePath, $quality = 80)
    {
        try {
            if (!Storage::exists('public/' . $imagePath)) {
                return false;
            }

            $fullPath = Storage::path('public/' . $imagePath);
            $image = $this->imageManager->read($fullPath);

            // ضغط الصورة
            $compressedImage = $image->toJpeg($quality);
            Storage::put('public/' . $imagePath, $compressedImage);

            Log::info('Image compressed successfully', ['path' => $imagePath, 'quality' => $quality]);
            return true;

        } catch (\Exception $e) {
            Log::error('Image compression failed', [
                'error' => $e->getMessage(),
                'path' => $imagePath
            ]);
            return false;
        }
    }

    /**
     * الحصول على معلومات الصورة
     */
    public function getImageInfo($imagePath)
    {
        try {
            if (!Storage::exists('public/' . $imagePath)) {
                return null;
            }

            $fullPath = Storage::path('public/' . $imagePath);
            $image = $this->imageManager->read($fullPath);

            return [
                'width' => $image->width(),
                'height' => $image->height(),
                'size' => Storage::size('public/' . $imagePath),
                'mime_type' => $image->origin()->mediaType(),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get image info', [
                'error' => $e->getMessage(),
                'path' => $imagePath
            ]);
            return null;
        }
    }

    /**
     * تحويل الصورة إلى WebP
     */
    public function convertToWebP($imagePath)
    {
        try {
            if (!Storage::exists('public/' . $imagePath)) {
                return false;
            }

            $pathInfo = pathinfo($imagePath);
            $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';

            $fullPath = Storage::path('public/' . $imagePath);
            $image = $this->imageManager->read($fullPath);

            // تحويل إلى WebP
            $webpImage = $image->toWebp(85);
            Storage::put('public/' . $webpPath, $webpImage);

            Log::info('Image converted to WebP', ['original' => $imagePath, 'webp' => $webpPath]);
            return $webpPath;

        } catch (\Exception $e) {
            Log::error('WebP conversion failed', [
                'error' => $e->getMessage(),
                'path' => $imagePath
            ]);
            return false;
        }
    }
}
