<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CleanupOldFiles extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cleanup:old-files {--force : حذف الملفات بدون تأكيد}';

    /**
     * The console command description.
     */
    protected $description = 'حذف الملفات القديمة والغير مستخدمة بعد إعادة الهيكلة';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 بدء تنظيف الملفات القديمة...');

        if (!$this->option('force')) {
            if (!$this->confirm('⚠️ هذا سيحذف الملفات القديمة نهائياً. هل أنت متأكد؟')) {
                $this->info('❌ تم إلغاء العملية');
                return 1;
            }
        }

        $deletedFiles = [];
        $errors = [];

        // قائمة الملفات القديمة المراد حذفها
        $oldFiles = [
            // Models القديمة
            'app/Models/Favorite.php',
            'app/Models/Rating.php',
            'app/Models/Comment.php',
            'app/Models/Review.php',
            'app/Models/SecurityLog.php',
            'app/Models/SearchLog.php',
            'app/Models/ContactAccessLog.php',
            'app/Models/AdSetting.php',
            'app/Models/AnnouncementSetting.php',
            'app/Models/UserContact.php',
            'app/Models/UserPrivacySetting.php',
            
            // Controllers القديمة (إذا وجدت)
            'app/Http/Controllers/FavoriteController.php',
            'app/Http/Controllers/RatingController.php',
            'app/Http/Controllers/CommentController.php',
            
            // Migrations القديمة (تم نقلها للـ backup)
            // سيتم التحقق من وجودها في مجلد migrations
        ];

        // حذف الملفات
        foreach ($oldFiles as $file) {
            $fullPath = base_path($file);
            
            if (File::exists($fullPath)) {
                try {
                    File::delete($fullPath);
                    $deletedFiles[] = $file;
                    $this->line("✅ تم حذف: {$file}");
                } catch (\Exception $e) {
                    $errors[] = "فشل حذف {$file}: " . $e->getMessage();
                    $this->error("❌ فشل حذف: {$file}");
                }
            } else {
                $this->line("ℹ️ الملف غير موجود: {$file}");
            }
        }

        // حذف الـ migrations القديمة
        $this->cleanupOldMigrations($deletedFiles, $errors);

        // حذف ملفات JavaScript/CSS القديمة
        $this->cleanupOldAssets($deletedFiles, $errors);

        // عرض النتائج
        $this->displayResults($deletedFiles, $errors);

        return empty($errors) ? 0 : 1;
    }

    private function cleanupOldMigrations(&$deletedFiles, &$errors)
    {
        $this->info('🗂️ تنظيف الـ migrations القديمة...');

        $migrationsPath = database_path('migrations');
        $oldMigrationPatterns = [
            '*_create_favorites_table.php',
            '*_create_ratings_table.php',
            '*_create_comments_table.php',
            '*_create_reviews_table.php',
            '*_create_security_logs_table.php',
            '*_create_search_logs_table.php',
            '*_create_contact_access_logs_table.php',
            '*_create_ad_settings_table.php',
            '*_create_announcement_settings_table.php',
            '*_create_user_contacts_table.php',
            '*_create_user_privacy_settings_table.php',
            '*_add_security_fields_to_users_table.php',
            '*_add_card_fields_to_users_table.php',
        ];

        foreach ($oldMigrationPatterns as $pattern) {
            $files = glob($migrationsPath . '/' . $pattern);
            
            foreach ($files as $file) {
                $relativePath = 'database/migrations/' . basename($file);
                
                try {
                    File::delete($file);
                    $deletedFiles[] = $relativePath;
                    $this->line("✅ تم حذف migration: " . basename($file));
                } catch (\Exception $e) {
                    $errors[] = "فشل حذف migration {$relativePath}: " . $e->getMessage();
                    $this->error("❌ فشل حذف migration: " . basename($file));
                }
            }
        }
    }

    private function cleanupOldAssets(&$deletedFiles, &$errors)
    {
        $this->info('🎨 تنظيف الملفات الثابتة القديمة...');

        $oldAssets = [
            'public/js/favorites-old.js',
            'public/js/ratings-old.js',
            'public/js/comments-old.js',
            'public/css/favorites-old.css',
            'public/css/ratings-old.css',
            'public/css/comments-old.css',
        ];

        foreach ($oldAssets as $asset) {
            $fullPath = base_path($asset);
            
            if (File::exists($fullPath)) {
                try {
                    File::delete($fullPath);
                    $deletedFiles[] = $asset;
                    $this->line("✅ تم حذف asset: {$asset}");
                } catch (\Exception $e) {
                    $errors[] = "فشل حذف asset {$asset}: " . $e->getMessage();
                    $this->error("❌ فشل حذف asset: {$asset}");
                }
            }
        }
    }

    private function displayResults($deletedFiles, $errors)
    {
        $this->newLine();
        $this->info('📊 نتائج التنظيف:');

        if (!empty($deletedFiles)) {
            $this->info('✅ الملفات المحذوفة (' . count($deletedFiles) . '):');
            foreach ($deletedFiles as $file) {
                $this->line("  - {$file}");
            }
        }

        if (!empty($errors)) {
            $this->error('❌ الأخطاء (' . count($errors) . '):');
            foreach ($errors as $error) {
                $this->line("  - {$error}");
            }
        }

        if (empty($deletedFiles) && empty($errors)) {
            $this->info('ℹ️ لم يتم العثور على ملفات قديمة للحذف');
        }

        $this->newLine();
        $this->info('🎯 التوصيات:');
        $this->line('1. تأكد من عمل النظام الجديد بشكل صحيح');
        $this->line('2. قم بتشغيل الاختبارات للتأكد من سلامة النظام');
        $this->line('3. راجع الـ logs للتأكد من عدم وجود أخطاء');
        $this->line('4. احتفظ بنسخة احتياطية قبل الحذف النهائي');
    }
}
