{{-- مكون معلومات صاحب الإعلان المتقدم --}}
@props(['ad', 'detailed' => false, 'showStats' => false])

{{-- إضافة Logging لعرض معلومات صاحب الإعلان --}}
@php
    if ($detailed) {
        Log::info('عرض معلومات صاحب الإعلان المتقدمة', [
            'ad_id' => $ad->id,
            'ad_title' => $ad->title,
            'advertiser_id' => $ad->user->id,
            'advertiser_name' => $ad->user->name,
            'viewer_id' => auth()->id(),
            'ip' => request()->ip(),
            'timestamp' => now()
        ]);
    }
@endphp

<div class="advertiser-section {{ $detailed ? 'advertiser-detailed' : 'advertiser-compact' }}">
    {{-- معلومات المستخدم الأساسية --}}
    <div class="advertiser-profile">
        {{-- صورة المستخدم --}}
        <div class="advertiser-avatar">
            @if($ad->user->avatar)
                <img src="{{ $ad->user->avatar_url }}" 
                     alt="{{ $ad->user->name }}" 
                     class="avatar-image">
            @else
                <div class="avatar-placeholder">
                    <i class="fas fa-user"></i>
                </div>
            @endif
            
            {{-- شارات المستخدم --}}
            <div class="user-badges">
                @if($ad->user->is_verified)
                    <span class="badge-verified" title="مستخدم موثق">
                        <i class="fas fa-check-circle"></i>
                    </span>
                @endif
                
                @if($ad->user->is_admin)
                    <span class="badge-admin" title="مدير">
                        <i class="fas fa-crown"></i>
                    </span>
                @endif
                
                @if($ad->user->is_premium)
                    <span class="badge-premium" title="عضوية مميزة">
                        <i class="fas fa-star"></i>
                    </span>
                @endif
            </div>
        </div>

        {{-- معلومات المستخدم --}}
        <div class="advertiser-details">
            <div class="advertiser-name">
                <h6 class="name-text">{{ $ad->user->name }}</h6>
                @if($detailed && $ad->user->company_name)
                    <p class="company-name">{{ $ad->user->company_name }}</p>
                @endif
            </div>

            {{-- تاريخ الانضمام --}}
            <div class="join-date">
                <i class="fas fa-calendar-alt text-muted me-1"></i>
                <small class="text-muted">
                    عضو منذ {{ $ad->user->created_at->format('Y') }}
                </small>
            </div>

            {{-- آخر نشاط --}}
            @if($detailed && $ad->user->last_seen_at)
                <div class="last-seen">
                    <i class="fas fa-clock text-muted me-1"></i>
                    <small class="text-muted">
                        آخر نشاط {{ $ad->user->last_seen_at->diffForHumans() }}
                    </small>
                </div>
            @endif
        </div>
    </div>

    {{-- إحصائيات المستخدم للعرض المفصل --}}
    @if($detailed && $showStats)
        <div class="advertiser-stats">
            <div class="stats-grid">
                {{-- عدد الإعلانات النشطة --}}
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-bullhorn text-primary"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number">{{ $ad->user->activeAds()->count() }}</span>
                        <span class="stat-label">إعلان نشط</span>
                    </div>
                </div>

                {{-- متوسط التقييم --}}
                @if($ad->user->average_rating > 0)
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <span class="stat-number">{{ number_format($ad->user->average_rating, 1) }}</span>
                            <span class="stat-label">متوسط التقييم</span>
                        </div>
                    </div>
                @endif

                {{-- عدد التقييمات --}}
                @if($ad->user->received_reviews_count > 0)
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-comments text-info"></i>
                        </div>
                        <div class="stat-content">
                            <span class="stat-number">{{ $ad->user->received_reviews_count }}</span>
                            <span class="stat-label">تقييم</span>
                        </div>
                    </div>
                @endif

                {{-- عدد المفضلة --}}
                @if($ad->user->favorites_count > 0)
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-heart text-danger"></i>
                        </div>
                        <div class="stat-content">
                            <span class="stat-number">{{ $ad->user->favorites_count }}</span>
                            <span class="stat-label">مفضلة</span>
                        </div>
                    </div>
                @endif
            </div>

            {{-- تقييم سريع للمستخدم --}}
            @if($detailed && $ad->user->average_rating > 0)
                <div class="quick-rating">
                    <div class="rating-stars">
                        @for($i = 1; $i <= 5; $i++)
                            @if($i <= floor($ad->user->average_rating))
                                <i class="fas fa-star text-warning"></i>
                            @elseif($i - 0.5 <= $ad->user->average_rating)
                                <i class="fas fa-star-half-alt text-warning"></i>
                            @else
                                <i class="far fa-star text-muted"></i>
                            @endif
                        @endfor
                        <span class="rating-text">
                            ({{ $ad->user->received_reviews_count }} تقييم)
                        </span>
                    </div>
                </div>
            @endif
        </div>
    @endif

    {{-- أزرار التفاعل للعرض المفصل --}}
    @if($detailed)
        <div class="advertiser-actions">
            {{-- زر عرض جميع إعلانات المستخدم --}}
            <a href="{{ route('ads.by-user', $ad->user->id) }}" 
               class="btn btn-outline-primary btn-sm">
                <i class="fas fa-list me-1"></i>
                جميع الإعلانات ({{ $ad->user->activeAds()->count() }})
            </a>

            {{-- زر التواصل المباشر --}}
            @auth
                @if(auth()->id() !== $ad->user->id)
                    <button class="btn btn-outline-success btn-sm" 
                            onclick="openDirectMessage({{ $ad->user->id }})">
                        <i class="fas fa-envelope me-1"></i>
                        رسالة مباشرة
                    </button>
                @endif
            @endauth

            {{-- زر الإبلاغ --}}
            @auth
                @if(auth()->id() !== $ad->user->id)
                    <button class="btn btn-outline-warning btn-sm" 
                            onclick="reportUser({{ $ad->user->id }})">
                        <i class="fas fa-flag me-1"></i>
                        إبلاغ
                    </button>
                @endif
            @endauth
        </div>
    @endif

    {{-- معلومات الموثوقية --}}
    @if($detailed)
        <div class="trust-indicators">
            <div class="trust-items">
                @if($ad->user->is_verified)
                    <div class="trust-item verified">
                        <i class="fas fa-shield-alt text-success me-1"></i>
                        <small>هوية موثقة</small>
                    </div>
                @endif

                @if($ad->user->phone_verified_at)
                    <div class="trust-item phone-verified">
                        <i class="fas fa-phone text-success me-1"></i>
                        <small>هاتف موثق</small>
                    </div>
                @endif

                @if($ad->user->email_verified_at)
                    <div class="trust-item email-verified">
                        <i class="fas fa-envelope text-success me-1"></i>
                        <small>إيميل موثق</small>
                    </div>
                @endif

                {{-- مؤشر الثقة العام --}}
                @php
                    $trustScore = 0;
                    if($ad->user->is_verified) $trustScore += 30;
                    if($ad->user->phone_verified_at) $trustScore += 25;
                    if($ad->user->email_verified_at) $trustScore += 20;
                    if($ad->user->average_rating >= 4) $trustScore += 25;
                    $trustScore = min($trustScore, 100);
                @endphp

                @if($trustScore > 0)
                    <div class="trust-score">
                        <div class="trust-score-bar">
                            <div class="trust-score-fill" style="width: {{ $trustScore }}%"></div>
                        </div>
                        <small class="trust-score-text">
                            مؤشر الثقة: {{ $trustScore }}%
                        </small>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>
