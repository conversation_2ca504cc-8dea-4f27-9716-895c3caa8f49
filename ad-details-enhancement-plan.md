# خطة تطوير وتحسين صفحة تفاصيل الإعلان
## Ad Details Page Enhancement Plan

---

## ⚠️ قوانين التطوير الصارمة والمبادئ التوجيهية

> **تحذير مهم:** يجب الالتزام الصارم بهذه القوانين أثناء تنفيذ خطة التطوير لضمان استقرار المشروع وتجنب المشاكل

### 🔒 1. إدارة الملفات والدوال

#### قبل إنشاء أي ملف جديد:
- ✅ **فحص شامل:** استخدام أدوات البحث للتحقق من عدم وجود الملف مسبقاً
- ✅ **البحث في المجلدات:** فحص `resources/views/components/` و `public/css/components/` و `public/js/`
- ✅ **مراجعة الملفات المشابهة:** البحث عن ملفات تؤدي نفس الوظيفة أو وظيفة مشابهة

#### قبل إنشاء أي دالة جديدة:
- ✅ **البحث في النماذج:** فحص جميع Models للبحث عن دوال مشابهة
- ✅ **البحث في Controllers:** مراجعة Controllers الموجودة
- ✅ **البحث في Helper Files:** فحص ملفات المساعدة الموجودة
- ✅ **الأولوية للتطوير:** تحديث الدوال الموجودة أولى من إنشاء دوال جديدة

#### حماية الوظائف الحالية:
- 🚫 **منع كسر الوظائف:** يُمنع منعاً باتاً تعطيل أي وظائف موجودة
- ✅ **اختبار شامل:** بعد كل تعديل، اختبار جميع الوظائف المرتبطة
- ✅ **النسخ الاحتياطي:** إنشاء نسخة احتياطية قبل أي تعديل مهم

### 🗄️ 2. إدارة قاعدة البيانات

#### قيود صارمة:
- 🚫 **لا جداول جديدة:** منع إنشاء جداول جديدة إلا في الضرورة القصوى
- 🚫 **لا أعمدة جديدة:** منع إضافة أعمدة جديدة إلا إذا كان ضرورياً لا يمكن تجنبه 
- 🚫 **لا migrations جديدة:** تجنب إنشاء migrations جديدة والاعتماد على البنية الحالية

#### استثناءات مسموحة:
- ✅ **تعديل الجداول الموجودة:** يُسمح بتعديل أو إعادة إنشاء الجداول الموجودة
- ✅ **حذف البيانات التجريبية:** يمكن حذف وإعادة إنشاء البيانات التجريبية
- ✅ **تحسين الفهارس:** إضافة أو تحسين الفهارس للجداول الموجودة

### 🛡️ 3. مبادئ التطوير الآمن

#### قبل البدء:
```bash
# إنشاء نسخة احتياطية من الملفات المهمة
cp resources/views/ads/show.blade.php resources/views/ads/show.blade.php.backup
cp app/Models/Ad.php app/Models/Ad.php.backup
```

#### أثناء التطوير:
- ✅ **التطوير التدريجي:** تنفيذ التغييرات على مراحل صغيرة
- ✅ **اختبار مستمر:** اختبار كل تغيير فور تنفيذه
- ✅ **التوثيق الفوري:** توثيق التغييرات أثناء التطوير

#### بعد كل تعديل:
```bash
# اختبار الوظائف الأساسية
php artisan route:list | grep ads
php artisan config:cache
php artisan view:cache
```

### 📝 4. استثناءات مسموحة

#### ملفات يُسمح بإنشاؤها:
- ✅ **مكونات Blade جديدة:** إذا لم توجد مكونات مشابهة
- ✅ **ملفات CSS للمكونات:** ملفات منفصلة للمكونات الجديدة فقط
- ✅ **ملفات JS للمكونات:** ملفات JavaScript منفصلة للوظائف الجديدة
- ✅ **دوال مساعدة:** إذا لم توجد دوال مشابهة في المشروع

#### شروط الاستثناءات:
- 📋 **توثيق السبب:** توضيح سبب عدم إمكانية استخدام الملفات الموجودة
- 🔍 **مراجعة شاملة:** التأكد من عدم وجود بديل في المشروع
- ✅ **اتباع معايير المشروع:** الالتزام بنفس أسلوب التسمية والتنظيم

### 📊 5. نظام التسجيل والمراقبة (Logging)

#### تسجيل العمليات المهمة:
```php
// في بداية كل دالة مهمة
Log::info('بدء عملية كشف معلومات التواصل', [
    'ad_id' => $adId,
    'user_id' => auth()->id(),
    'ip' => request()->ip()
]);

// عند حدوث خطأ
Log::error('خطأ في كشف رقم الهاتف', [
    'ad_id' => $adId,
    'error' => $exception->getMessage(),
    'trace' => $exception->getTraceAsString()
]);

// عند نجاح العملية
Log::info('تم كشف معلومات التواصل بنجاح', [
    'ad_id' => $adId,
    'contact_type' => 'phone',
    'user_id' => auth()->id()
]);
```

#### مراقبة الأداء:
```php
// قياس وقت تنفيذ العمليات المهمة
$startTime = microtime(true);

// تنفيذ العملية
$result = $this->performOperation();

$executionTime = microtime(true) - $startTime;
Log::info('إحصائيات الأداء', [
    'operation' => 'reveal_contact',
    'execution_time' => $executionTime,
    'memory_usage' => memory_get_usage(true)
]);
```

#### مراقبة الأخطاء:
```php
// في ملف app/Exceptions/Handler.php
public function report(Throwable $exception)
{
    if ($exception instanceof ContactRevealException) {
        Log::critical('خطأ حرج في كشف معلومات التواصل', [
            'exception' => $exception->getMessage(),
            'user_id' => auth()->id(),
            'url' => request()->url()
        ]);
    }

    parent::report($exception);
}
```

### 🔍 6. قائمة التحقق قبل التنفيذ

#### قبل بدء أي مهمة:
- [ ] البحث عن ملفات مشابهة في المشروع
- [ ] مراجعة الدوال الموجودة في النماذج
- [ ] فحص المكونات الحالية في `resources/views/components/`
- [ ] التحقق من ملفات CSS/JS الموجودة
- [ ] إنشاء نسخة احتياطية من الملفات المهمة

#### أثناء التطوير:
- [ ] اختبار كل تغيير فور تنفيذه
- [ ] التأكد من عدم كسر الوظائف الحالية
- [ ] إضافة تسجيل مناسب للعمليات الجديدة
- [ ] توثيق التغييرات في التعليقات

#### بعد إكمال المهمة:
- [ ] اختبار شامل لجميع الوظائف المرتبطة
- [ ] مراجعة سجلات الأخطاء
- [ ] التأكد من الأداء المقبول
- [ ] توثيق التغييرات في الخطة

---

### 📋 مراجعة شاملة للمحادثة والتحليل

بناءً على التحليل الشامل لملف `resources\views\ads\show.blade.php` ومقارنته مع نموذج البيانات `Ad model`، تم تحديد عدة مشاكل ونقاط تحسين:

#### المشاكل المحددة:
1. **نظام الأسعار المتقدم غير معروض** - يوجد 12 حقل سعر في قاعدة البيانات لكن لا يتم عرضها
2. **معلومات التواصل غير مفصلة** - يتم عرض `contact_info` كنص واحد بدلاً من الحقول المنفصلة
3. **نقص في معلومات المستخدم** - لا يتم عرض معلومات صاحب الإعلان
4. **غياب نظام التقييمات** - لا يتم عرض التقييمات والتعليقات
5. **عدم عرض حالة الإعلان** - لا توجد شارات للحالة والميزات
6. **نقص في الإحصائيات** - عدم عرض إحصائيات المفضلة والتفاعل

---

## 🎯 تصنيف المهام حسب الأولوية

### 🔴 أولوية عالية (Critical Priority)

#### المهمة #1: إنشاء مكون عرض الأسعار المتقدم
**الوصف:** إنشاء مكون Blade منفصل لعرض جميع معلومات الأسعار بشكل جذاب ومنظم

**🔍 تطبيق القوانين الصارمة:**
```bash
# 1. البحث عن مكونات أسعار موجودة
find resources/views -name "*price*" -type f
grep -r "price" resources/views/components/
grep -r "getFormattedPrice" app/Models/

# 2. فحص الدوال الموجودة في نموذج Ad
grep -r "price" app/Models/Ad.php
```

**الملفات المتأثرة:**
- `resources/views/components/price-display-detailed.blade.php` (جديد - بعد التأكد من عدم وجود مكون مشابه)
- `resources/views/ads/show.blade.php` (تحديث)
- `public/css/components/pricing.css` (جديد - إذا لم توجد ملفات CSS للأسعار)

**الخطوات التقنية المحدثة:**
1. **فحص شامل:** البحث عن مكونات أسعار موجودة في `resources/views/components/`
2. **مراجعة النموذج:** فحص دوال الأسعار الموجودة في `app/Models/Ad.php`
3. **تطوير أو إنشاء:** تحديث المكون الموجود أو إنشاء مكون جديد
4. **إضافة Logging:** تسجيل عمليات عرض الأسعار للمراقبة
5. **اختبار شامل:** التأكد من عدم كسر عرض الأسعار الحالي

**التقدير الزمني:** 4-6 ساعات
**المتطلبات:** فهم نظام العملات المستخدم في الموقع + مراجعة الكود الحالي

#### المهمة #2: تحسين عرض معلومات التواصل
**الوصف:** فصل وتحسين عرض معلومات التواصل مع إضافة ميزات الأمان

**🔍 تطبيق القوانين الصارمة:**
```bash
# 1. البحث عن مكونات تواصل موجودة
find resources/views -name "*contact*" -type f
grep -r "contact_info" resources/views/
grep -r "phone" app/Models/Ad.php
grep -r "email" app/Models/Ad.php

# 2. فحص خدمات التشفير الموجودة
find app -name "*Encryption*" -type f
grep -r "encrypt" app/Services/
```

**الملفات المتأثرة:**
- `resources/views/components/ad-card/partials/contact.blade.php` (تحديث الموجود)
- `resources/views/ads/show.blade.php` (تحديث)
- `app/Http/Controllers/AdController.php` (إضافة routes للكشف)
- `public/js/contact-reveal.js` (جديد - إذا لم توجد وظائف مشابهة)

**الخطوات التقنية المحدثة:**
1. **مراجعة الموجود:** فحص `resources/views/components/ad-card/partials/contact.blade.php`
2. **تطوير المكون:** تحسين المكون الموجود بدلاً من إنشاء جديد
3. **فحص خدمة التشفير:** استخدام `EncryptionService` الموجودة
4. **إضافة Routes:** إضافة routes للكشف عن معلومات التواصل مع Logging
5. **تحسين الأمان:** تطبيق CSRF protection وتسجيل العمليات

**التقدير الزمني:** 3-4 ساعات
**المتطلبات:** استخدام خدمة التشفير الموجودة + مراجعة المكونات الحالية

#### المهمة #3: إضافة معلومات صاحب الإعلان
**الوصف:** عرض معلومات المستخدم الذي نشر الإعلان مع إحصائياته
**الملفات المتأثرة:**
- `resources/views/components/advertiser-info.blade.php` (جديد)
- `resources/views/ads/show.blade.php` (تحديث)

**الخطوات التقنية:**
1. إنشاء مكون لعرض معلومات المستخدم
2. إضافة إحصائيات المستخدم (عدد الإعلانات، تاريخ الانضمام)
3. إضافة رابط لعرض جميع إعلانات المستخدم
4. تصميم بطاقة مستخدم جذابة

**التقدير الزمني:** 2-3 ساعات
**المتطلبات:** علاقة User في نموذج Ad

### 🟡 أولوية متوسطة (Medium Priority)

#### المهمة #4: إضافة نظام التقييمات والتعليقات
**الوصف:** عرض التقييمات والتعليقات مع إمكانية التفاعل
**الملفات المتأثرة:**
- `resources/views/components/ratings-section.blade.php` (جديد)
- `resources/views/components/comments-section.blade.php` (جديد)
- `resources/views/ads/show.blade.php` (تحديث)
- `public/js/ratings-interaction.js` (جديد)

**الخطوات التقنية:**
1. إنشاء مكون عرض التقييمات مع النجوم
2. إنشاء مكون عرض التعليقات مع الردود
3. إضافة نماذج إضافة تقييم وتعليق جديد
4. تحسين تجربة المستخدم للتفاعل

**التقدير الزمني:** 6-8 ساعات
**المتطلبات:** نماذج Rating و Comment موجودة

#### المهمة #5: إضافة شارات الحالة والميزات
**الوصف:** عرض شارات توضح حالة الإعلان وميزاته الخاصة
**الملفات المتأثرة:**
- `resources/views/components/status-badges.blade.php` (جديد)
- `resources/views/ads/show.blade.php` (تحديث)
- `public/css/components/badges.css` (جديد)

**الخطوات التقنية:**
1. إنشاء مكون الشارات مع تصاميم مختلفة
2. إضافة شارات للحالة (مميز، جديد، عرض محدود)
3. إضافة شارات للميزات (مجاني، قابل للتفاوض)
4. تحسين التصميم والألوان

**التقدير الزمني:** 2-3 ساعات
**المتطلبات:** حقول الحالة في نموذج Ad

### 🟢 أولوية منخفضة (Low Priority)

#### المهمة #6: إضافة إحصائيات المفضلة والتفاعل
**الوصف:** عرض إحصائيات تفاعل المستخدمين مع الإعلان
**الملفات المتأثرة:**
- `resources/views/components/interaction-stats.blade.php` (جديد)
- `resources/views/ads/show.blade.php` (تحديث)

**الخطوات التقنية:**
1. إنشاء مكون الإحصائيات
2. عرض عدد مرات الإضافة للمفضلة
3. عرض إحصائيات المشاهدة والتفاعل
4. إضافة رسوم بيانية بسيطة

**التقدير الزمني:** 3-4 ساعات
**المتطلبات:** نموذج Favorite موجود

#### المهمة #7: تحسين تجربة المستخدم العامة
**الوصف:** تحسينات إضافية لتجربة المستخدم والأداء
**الملفات المتأثرة:**
- `resources/views/ads/show.blade.php` (تحديث)
- `public/css/ads/show.css` (تحديث)
- `public/js/ads/show.js` (تحديث)

**الخطوات التقنية:**
1. تحسين الاستجابة للأجهزة المختلفة
2. إضافة تأثيرات بصرية محسنة
3. تحسين سرعة التحميل
4. إضافة ميزات إضافية (طباعة، مشاركة محسنة)

**التقدير الزمني:** 4-5 ساعات
**المتطلبات:** اختبار على أجهزة مختلفة

---

## �️ تطبيق القوانين الصارمة على جميع المهام

### 📋 قائمة التحقق الشاملة قبل البدء

#### 1. فحص الملفات والمكونات الموجودة:
```bash
# البحث عن جميع مكونات Blade الموجودة
find resources/views/components -name "*.blade.php" | sort

# البحث عن ملفات CSS للمكونات
find public/css -name "*.css" | grep -i component

# البحث عن ملفات JavaScript للمكونات
find public/js -name "*.js" | grep -i component

# فحص دوال النماذج الموجودة
grep -n "function\|public function" app/Models/Ad.php
```

#### 2. فحص قاعدة البيانات الحالية:
```bash
# عرض بنية جدول ads
php artisan tinker
>>> Schema::getColumnListing('ads')

# فحص العلاقات الموجودة
>>> App\Models\Ad::first()->getRelations()
```

#### 3. إنشاء النسخ الاحتياطية:
```bash
# نسخ احتياطي للملفات المهمة
mkdir -p backup/$(date +%Y%m%d_%H%M%S)
cp resources/views/ads/show.blade.php backup/$(date +%Y%m%d_%H%M%S)/
cp app/Models/Ad.php backup/$(date +%Y%m%d_%H%M%S)/
cp app/Http/Controllers/AdController.php backup/$(date +%Y%m%d_%H%M%S)/
```

### 🔍 أمثلة عملية لتطبيق القوانين

#### مثال 1: قبل إنشاء مكون جديد
```bash
# البحث عن مكونات مشابهة
grep -r "price" resources/views/components/
grep -r "contact" resources/views/components/
grep -r "rating" resources/views/components/

# إذا وُجد مكون مشابه، استخدمه وطوره بدلاً من إنشاء جديد
```

#### مثال 2: قبل إضافة دالة جديدة
```php
// البحث في نموذج Ad عن دوال مشابهة
// إذا وُجدت دالة getFormattedPrice، طورها بدلاً من إنشاء دالة جديدة

// ❌ خطأ - إنشاء دالة جديدة دون فحص
public function getDetailedPrice() { ... }

// ✅ صحيح - تطوير الدالة الموجودة
public function getFormattedPrice($detailed = false) {
    // تطوير الدالة الموجودة لتدعم التفاصيل
}
```

#### مثال 3: إضافة Logging مناسب
```php
// في بداية كل عملية مهمة
Log::info('بدء عملية عرض تفاصيل الإعلان', [
    'ad_id' => $this->id,
    'user_id' => auth()->id(),
    'timestamp' => now(),
    'ip' => request()->ip()
]);

// عند حدوث خطأ
try {
    $contactInfo = $this->getContactInfo();
} catch (Exception $e) {
    Log::error('خطأ في جلب معلومات التواصل', [
        'ad_id' => $this->id,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    throw $e;
}
```

### 🚨 تحذيرات مهمة

#### ما يُمنع فعله:
```php
// ❌ إنشاء migration جديد دون ضرورة
php artisan make:migration add_contact_reveals_to_ads_table

// ❌ إنشاء جدول جديد
Schema::create('ad_contact_reveals', function (Blueprint $table) { ... });

// ❌ كسر الوظائف الموجودة
// حذف أو تعديل دالة موجودة دون اختبار
```

#### ما يُسمح فعله:
```php
// ✅ تعديل جدول موجود (البيانات تجريبية)
Schema::table('ads', function (Blueprint $table) {
    $table->integer('contact_reveals_count')->default(0);
});

// ✅ إضافة دالة جديدة إذا لم توجد مشابهة
public function incrementContactReveals() {
    Log::info('زيادة عداد كشف معلومات التواصل', ['ad_id' => $this->id]);
    $this->increment('contact_reveals_count');
}

// ✅ تطوير دالة موجودة
public function getContactInfo($masked = true) {
    // تطوير الدالة لتدعم الإخفاء/الإظهار
}
```

---

## �📅 ترتيب التنفيذ المقترح

### المرحلة الأولى (الأسبوع الأول)
1. **المهمة #1** - مكون عرض الأسعار المتقدم
2. **المهمة #2** - تحسين معلومات التواصل
3. **المهمة #3** - معلومات صاحب الإعلان

### المرحلة الثانية (الأسبوع الثاني)
4. **المهمة #5** - شارات الحالة والميزات
5. **المهمة #4** - نظام التقييمات والتعليقات

### المرحلة الثالثة (الأسبوع الثالث)
6. **المهمة #6** - إحصائيات المفضلة
7. **المهمة #7** - تحسينات تجربة المستخدم

---

## ✅ معايير الاكتمال

### لكل مهمة:
- [ ] إنشاء جميع الملفات المطلوبة
- [ ] إضافة التعليقات العربية الشارحة للكود
- [ ] اختبار الوظائف على بيانات حقيقية
- [ ] التأكد من الاستجابة للأجهزة المختلفة
- [ ] مراجعة الأداء والتحسين
- [ ] توثيق التغييرات

### للمشروع ككل:
- [ ] عرض جميع البيانات المتاحة في قاعدة البيانات
- [ ] تحسين تجربة المستخدم بشكل ملحوظ
- [ ] عدم وجود تكرار في الكود
- [ ] استخدام مكونات قابلة لإعادة الاستخدام
- [ ] توافق مع معايير الأمان
- [ ] اختبار شامل لجميع الميزات

---

## 📝 ملاحظات تقنية مهمة

### تجنب التكرار:
- استخدام مكونات Blade منفصلة لكل قسم
- إنشاء ملفات CSS و JS منفصلة للمكونات
- استخدام متغيرات CSS للألوان والمقاسات المتكررة

### التعليقات العربية:
- إضافة تعليق عربي لكل دالة يشرح الغرض منها
- توثيق المتغيرات والمعاملات المهمة
- شرح المنطق المعقد بتعليقات مفصلة

### الأمان:
- التأكد من تشفير معلومات التواصل الحساسة
- استخدام CSRF protection في النماذج
- تنظيف وتعقيم جميع المدخلات

---

**إجمالي التقدير الزمني:** 24-33 ساعة عمل
**مدة التنفيذ المقترحة:** 3 أسابيع
**عدد المطورين المطلوب:** 1-2 مطور

---

## 🛠️ أمثلة تقنية مفصلة

### مثال: مكون عرض الأسعار المتقدم

```blade
{{-- resources/views/components/price-display-detailed.blade.php --}}
{{-- مكون عرض الأسعار المتقدم مع دعم العملات والخصومات --}}
@props(['ad', 'showDetails' => true])

<div class="pricing-section">
    {{-- عرض السعر الرئيسي --}}
    @if($ad->is_free)
        <div class="price-free">
            <span class="price-label">مجاني</span>
            <i class="fas fa-gift text-success"></i>
        </div>
    @elseif($ad->price_type === 'on_request')
        <div class="price-on-request">
            <span class="price-label">السعر عند الطلب</span>
            <i class="fas fa-phone text-primary"></i>
        </div>
    @else
        <div class="price-display">
            {{-- السعر الحالي --}}
            <div class="current-price">
                <span class="price-amount">{{ number_format($ad->price, 0) }}</span>
                <span class="currency">{{ $ad->currency ?? 'ر.ي' }}</span>
            </div>

            {{-- السعر الأصلي مع الخصم --}}
            @if($ad->hasDiscount())
                <div class="original-price">
                    <span class="crossed-price">{{ number_format($ad->original_price, 0) }}</span>
                    <span class="discount-badge">
                        خصم {{ $ad->calculated_discount_percentage }}%
                    </span>
                </div>
                <div class="savings">
                    توفر {{ number_format($ad->savings_amount, 0) }} {{ $ad->currency }}
                </div>
            @endif
        </div>
    @endif

    {{-- شارات إضافية --}}
    <div class="price-badges">
        @if($ad->is_negotiable)
            <span class="badge badge-negotiable">قابل للتفاوض</span>
        @endif

        @if($ad->is_limited_offer)
            <span class="badge badge-limited">عرض محدود</span>
        @endif

        @if($ad->discount_expires_at && !$ad->isDiscountExpired())
            <span class="badge badge-expires">
                ينتهي في {{ $ad->discount_expires_at->diffForHumans() }}
            </span>
        @endif
    </div>

    {{-- ملاحظات السعر --}}
    @if($ad->price_notes && $showDetails)
        <div class="price-notes">
            <small class="text-muted">{{ $ad->price_notes }}</small>
        </div>
    @endif
</div>
```

### مثال: مكون معلومات التواصل المحسن

```blade
{{-- resources/views/components/contact-info-detailed.blade.php --}}
{{-- مكون عرض معلومات التواصل مع ميزات الأمان --}}
@props(['ad'])

<div class="contact-section">
    <h4 class="section-title">
        <i class="fas fa-phone text-primary me-2"></i>
        معلومات التواصل
    </h4>

    {{-- رقم الهاتف --}}
    @if($ad->phone)
        <div class="contact-item phone-contact">
            <div class="contact-preview" id="phone-preview">
                <i class="fas fa-phone text-success me-2"></i>
                <span class="masked-info">{{ $ad->masked_phone }}</span>
                <button class="btn btn-sm btn-outline-primary reveal-btn"
                        onclick="revealPhone('{{ $ad->id }}')">
                    إظهار الرقم
                </button>
            </div>

            <div class="contact-revealed d-none" id="phone-revealed">
                <i class="fas fa-phone text-success me-2"></i>
                <span class="full-info" id="full-phone"></span>
                <div class="contact-actions mt-2">
                    <a href="#" class="btn btn-sm btn-success me-2" id="call-btn">
                        <i class="fas fa-phone me-1"></i>اتصال
                    </a>
                    <a href="#" class="btn btn-sm btn-success" id="whatsapp-btn">
                        <i class="fab fa-whatsapp me-1"></i>واتساب
                    </a>
                </div>
            </div>
        </div>
    @endif

    {{-- البريد الإلكتروني --}}
    @if($ad->email)
        <div class="contact-item email-contact">
            <div class="contact-preview" id="email-preview">
                <i class="fas fa-envelope text-primary me-2"></i>
                <span class="masked-info">{{ $ad->masked_email }}</span>
                <button class="btn btn-sm btn-outline-primary reveal-btn"
                        onclick="revealEmail('{{ $ad->id }}')">
                    إظهار الإيميل
                </button>
            </div>

            <div class="contact-revealed d-none" id="email-revealed">
                <i class="fas fa-envelope text-primary me-2"></i>
                <span class="full-info" id="full-email"></span>
                <div class="contact-actions mt-2">
                    <a href="#" class="btn btn-sm btn-primary" id="email-btn">
                        <i class="fas fa-envelope me-1"></i>إرسال إيميل
                    </a>
                </div>
            </div>
        </div>
    @endif

    {{-- إحصائيات التواصل --}}
    <div class="contact-stats mt-3">
        <small class="text-muted">
            <i class="fas fa-eye me-1"></i>
            تم الكشف عن معلومات التواصل {{ $ad->contact_reveals_count ?? 0 }} مرة
        </small>
    </div>
</div>

{{-- JavaScript للتفاعل --}}
@push('scripts')
<script>
// دالة كشف رقم الهاتف مع تسجيل الإحصائية
async function revealPhone(adId) {
    try {
        const response = await fetch(`/ads/${adId}/reveal-phone`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (data.success) {
            // إخفاء المعاينة وإظهار الرقم الكامل
            document.getElementById('phone-preview').classList.add('d-none');
            document.getElementById('phone-revealed').classList.remove('d-none');
            document.getElementById('full-phone').textContent = data.phone;

            // تحديث روابط الاتصال
            document.getElementById('call-btn').href = `tel:${data.phone}`;
            document.getElementById('whatsapp-btn').href = `https://wa.me/${data.phone.replace(/[^0-9]/g, '')}`;

            // إظهار رسالة نجاح
            showToast('تم كشف رقم الهاتف بنجاح', 'success');
        } else {
            showToast('حدث خطأ في كشف رقم الهاتف', 'error');
        }
    } catch (error) {
        console.error('خطأ في كشف رقم الهاتف:', error);
        showToast('حدث خطأ في الاتصال', 'error');
    }
}

// دالة كشف البريد الإلكتروني
async function revealEmail(adId) {
    try {
        const response = await fetch(`/ads/${adId}/reveal-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('email-preview').classList.add('d-none');
            document.getElementById('email-revealed').classList.remove('d-none');
            document.getElementById('full-email').textContent = data.email;
            document.getElementById('email-btn').href = `mailto:${data.email}`;

            showToast('تم كشف البريد الإلكتروني بنجاح', 'success');
        } else {
            showToast('حدث خطأ في كشف البريد الإلكتروني', 'error');
        }
    } catch (error) {
        console.error('خطأ في كشف البريد الإلكتروني:', error);
        showToast('حدث خطأ في الاتصال', 'error');
    }
}
</script>
@endpush
```

---

## 📊 مؤشرات الأداء والنجاح

### مؤشرات تقنية:
- **سرعة التحميل:** أقل من 3 ثواني
- **نقاط Lighthouse:** أعلى من 90
- **استجابة الموقع:** دعم جميع الأجهزة
- **معدل الأخطاء:** أقل من 1%

### مؤشرات تجربة المستخدم:
- **زيادة وقت البقاء في الصفحة:** 25%+
- **زيادة معدل التفاعل:** 30%+
- **تحسين معدل التحويل:** 20%+
- **تقليل معدل الارتداد:** 15%+

### مؤشرات الأعمال:
- **زيادة عدد الاستفسارات:** 40%+
- **تحسين جودة العملاء المحتملين:** 25%+
- **زيادة عدد الإعلانات المميزة:** 20%+

---

## 🔄 خطة الصيانة والتطوير المستمر

### مراجعة شهرية:
- تحليل إحصائيات الاستخدام
- مراجعة ملاحظات المستخدمين
- تحديث المكونات حسب الحاجة

### تطوير ربع سنوي:
- إضافة ميزات جديدة
- تحسين الأداء
- تحديث التصميم

### مراجعة سنوية:
- إعادة تقييم شاملة للنظام
- تحديث التقنيات المستخدمة
- تطوير استراتيجية جديدة

---

## 🔐 مراقبة الالتزام بالقوانين الصارمة

### 📊 قائمة التحقق النهائية لكل مهمة

#### ✅ قبل البدء في أي مهمة:
- [ ] تم البحث عن ملفات مشابهة في المشروع
- [ ] تم فحص جميع المكونات الموجودة في `resources/views/components/`
- [ ] تم مراجعة دوال النموذج الموجودة في `app/Models/Ad.php`
- [ ] تم إنشاء نسخة احتياطية من الملفات المهمة
- [ ] تم توثيق سبب إنشاء ملفات جديدة (إن وجدت)

#### ✅ أثناء التطوير:
- [ ] تم إضافة تعليقات عربية شارحة لكل دالة
- [ ] تم إضافة Logging مناسب للعمليات المهمة
- [ ] تم اختبار كل تغيير فور تنفيذه
- [ ] تم التأكد من عدم كسر الوظائف الحالية
- [ ] تم اتباع معايير التسمية الموجودة في المشروع

#### ✅ بعد إكمال المهمة:
- [ ] تم اختبار شامل لجميع الوظائف المرتبطة
- [ ] تم فحص سجلات الأخطاء في `storage/logs/laravel.log`
- [ ] تم التأكد من الأداء المقبول (أقل من 3 ثواني)
- [ ] تم توثيق جميع التغييرات المُجراة
- [ ] تم تحديث الخطة بالتقدم المحرز

### 🚨 إجراءات الطوارئ

#### في حالة كسر وظيفة موجودة:
```bash
# 1. إيقاف التطوير فوراً
# 2. استعادة النسخة الاحتياطية
cp backup/YYYYMMDD_HHMMSS/show.blade.php resources/views/ads/
cp backup/YYYYMMDD_HHMMSS/Ad.php app/Models/

# 3. مراجعة سجلات الأخطاء
tail -f storage/logs/laravel.log

# 4. اختبار استعادة الوظيفة
php artisan route:cache
php artisan view:cache
```

#### في حالة مشاكل الأداء:
```bash
# 1. فحص استهلاك الذاكرة
php artisan tinker
>>> memory_get_usage(true)

# 2. فحص استعلامات قاعدة البيانات
# إضافة في AppServiceProvider
DB::listen(function ($query) {
    Log::info('Database Query', [
        'sql' => $query->sql,
        'time' => $query->time
    ]);
});
```

### 📋 تقرير الالتزام بالقوانين

#### نموذج تقرير لكل مهمة:
```markdown
## تقرير المهمة #X: [اسم المهمة]

### الالتزام بالقوانين:
- ✅/❌ تم البحث عن ملفات مشابهة
- ✅/❌ تم استخدام الملفات الموجودة بدلاً من إنشاء جديدة
- ✅/❌ تم تجنب إنشاء migrations جديدة
- ✅/❌ تم إضافة Logging مناسب
- ✅/❌ تم اختبار عدم كسر الوظائف الحالية

### الملفات المُعدلة:
- [قائمة الملفات مع نوع التعديل]

### الدوال المُضافة/المُعدلة:
- [قائمة الدوال مع الوصف]

### المشاكل المواجهة:
- [وصف أي مشاكل وكيفية حلها]

### التوصيات للمهام القادمة:
- [اقتراحات للتحسين]
```

### 🎯 معايير النجاح النهائية

#### للمشروع ككل:
- [ ] **صفر ملفات مكررة:** لا توجد ملفات تؤدي نفس الوظيفة
- [ ] **صفر دوال مكررة:** لا توجد دوال تؤدي نفس العمل
- [ ] **صفر migrations جديدة:** تم استخدام البنية الحالية
- [ ] **100% تغطية Logging:** جميع العمليات المهمة مسجلة
- [ ] **صفر كسر للوظائف:** جميع الوظائف الحالية تعمل بنفس الكفاءة

#### للأداء:
- [ ] **سرعة التحميل < 3 ثواني**
- [ ] **استهلاك ذاكرة < 128MB**
- [ ] **عدد استعلامات قاعدة البيانات < 20 لكل صفحة**
- [ ] **حجم ملفات CSS/JS < 500KB**

---

## 📞 جهات الاتصال والدعم

### في حالة الحاجة لمساعدة:
- **المراجعة التقنية:** فحص الكود قبل التطبيق
- **حل المشاكل:** مساعدة في حل الأخطاء التقنية
- **تحسين الأداء:** اقتراحات لتحسين سرعة التطبيق

### موارد مفيدة:
- **Laravel Documentation:** https://laravel.com/docs
- **Blade Components:** https://laravel.com/docs/blade#components
- **Laravel Logging:** https://laravel.com/docs/logging

---

**تاريخ إنشاء الخطة:** $(date)
**آخر تحديث:** $(date)
**حالة المشروع:** جاهز للتنفيذ مع الالتزام الصارم بالقوانين المحددة
