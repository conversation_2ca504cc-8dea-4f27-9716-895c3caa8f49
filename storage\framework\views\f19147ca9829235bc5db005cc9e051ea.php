
<div id="compareBar" class="compare-bar" style="display: none;">
    <div class="compare-bar-content">
        <div class="compare-info">
            <div class="compare-icon">
                <i class="fas fa-balance-scale"></i>
            </div>
            <div class="compare-text">
                <span class="compare-count">0</span> <?php echo e(__('items selected for comparison')); ?>

            </div>
        </div>
        
        <div class="compare-actions">
            <button class="btn btn-primary btn-sm" onclick="goToComparePage()" id="compareBtn" disabled>
                <i class="fas fa-eye me-1"></i>
                <?php echo e(__('Compare')); ?>

            </button>
            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="clearCompareList()">
                <i class="fas fa-trash me-1"></i>
                <?php echo e(__('Clear')); ?>

            </button>
            <button class="btn btn-link btn-sm ms-2 text-white" onclick="hideCompareBar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    
    <!-- قائمة الإعلانات المختارة -->
    <div class="compare-items" id="compareItems">
        <!-- سيتم ملؤها بـ JavaScript -->
    </div>
</div>

<style>
.compare-bar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    z-index: 1050;
    min-width: 400px;
    max-width: 90vw;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.compare-bar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
}

.compare-info {
    display: flex;
    align-items: center;
}

.compare-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    opacity: 0.9;
}

.compare-text {
    font-weight: 500;
}

.compare-count {
    font-size: 1.2rem;
    font-weight: bold;
    color: #ffc107;
}

.compare-actions .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.compare-actions .btn:hover {
    transform: translateY(-2px);
}

.compare-items {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 1.5rem;
    display: none;
}

.compare-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.compare-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.compare-item:last-child {
    margin-bottom: 0;
}

.compare-item-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.compare-item-image {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
    margin-right: 0.75rem;
}

.compare-item-details h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
    color: white;
}

.compare-item-details small {
    color: rgba(255, 255, 255, 0.8);
}

.compare-item-remove {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.compare-item-remove:hover {
    color: #ff6b6b;
    background: rgba(255, 255, 255, 0.1);
}

/* تنسيق أزرار المقارنة في البطاقات */
.compare-btn.active {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
}

.compare-btn.active i {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Responsive */
@media (max-width: 768px) {
    .compare-bar {
        min-width: 350px;
        bottom: 10px;
    }
    
    .compare-bar-content {
        padding: 0.75rem 1rem;
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .compare-info {
        order: 1;
    }
    
    .compare-actions {
        order: 2;
    }
}

@media (max-width: 480px) {
    .compare-bar {
        min-width: 300px;
        left: 10px;
        right: 10px;
        transform: none;
        max-width: none;
    }
    
    .compare-actions .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
}
</style>

<script>
// نظام المقارنة
class CompareSystem {
    constructor() {
        this.compareList = this.loadFromStorage();
        this.maxItems = 4; // حد أقصى 4 عناصر للمقارنة
        this.init();
    }

    init() {
        this.updateUI();
        this.bindEvents();
    }

    // تحميل من Local Storage
    loadFromStorage() {
        try {
            const stored = localStorage.getItem('compareList');
            return stored ? JSON.parse(stored) : [];
        } catch (e) {
            console.error('Error loading compare list:', e);
            return [];
        }
    }

    // حفظ في Local Storage
    saveToStorage() {
        try {
            localStorage.setItem('compareList', JSON.stringify(this.compareList));
        } catch (e) {
            console.error('Error saving compare list:', e);
        }
    }

    // إضافة/إزالة عنصر
    toggle(adId) {
        const index = this.compareList.findIndex(item => item.id == adId);
        
        if (index > -1) {
            // إزالة من القائمة
            this.compareList.splice(index, 1);
            this.updateButtonState(adId, false);
            
            if (typeof showToast === 'function') {
                showToast('<?php echo e(__("Removed from comparison")); ?>', 'info');
            }
        } else {
            // التحقق من الحد الأقصى
            if (this.compareList.length >= this.maxItems) {
                if (typeof showToast === 'function') {
                    showToast(`<?php echo e(__("Maximum :max items allowed for comparison", ["max" => ":max"])); ?>`.replace(':max', this.maxItems), 'warning');
                } else {
                    alert(`<?php echo e(__("Maximum :max items allowed for comparison", ["max" => ":max"])); ?>`.replace(':max', this.maxItems));
                }
                return;
            }

            // إضافة للقائمة
            this.addToCompare(adId);
        }

        this.saveToStorage();
        this.updateUI();
    }

    // إضافة عنصر للمقارنة
    async addToCompare(adId) {
        try {
            // جلب بيانات الإعلان
            const response = await fetch(`/api/ads/${adId}/basic-info`);
            if (!response.ok) {
                throw new Error('Failed to fetch ad data');
            }
            
            const adData = await response.json();
            
            this.compareList.push({
                id: adId,
                title: adData.title,
                price: adData.price,
                image: adData.image_url,
                category: adData.category,
                url: adData.url
            });

            this.updateButtonState(adId, true);
            
            if (typeof showToast === 'function') {
                showToast('<?php echo e(__("Added to comparison")); ?>', 'success');
            }
        } catch (error) {
            console.error('Error adding to compare:', error);
            
            // إضافة بيانات أساسية كـ fallback
            this.compareList.push({
                id: adId,
                title: `<?php echo e(__("Ad")); ?> #${adId}`,
                price: null,
                image: null,
                category: null,
                url: null
            });

            this.updateButtonState(adId, true);
        }
    }

    // تحديث حالة الزر
    updateButtonState(adId, isActive) {
        const button = document.querySelector(`[data-ad-id="${adId}"].compare-btn`);
        if (button) {
            if (isActive) {
                button.classList.add('active');
                button.title = '<?php echo e(__("Remove from Compare")); ?>';
            } else {
                button.classList.remove('active');
                button.title = '<?php echo e(__("Add to Compare")); ?>';
            }
        }
    }

    // تحديث واجهة المستخدم
    updateUI() {
        const compareBar = document.getElementById('compareBar');
        const compareCount = document.querySelector('.compare-count');
        const compareBtn = document.getElementById('compareBtn');
        
        if (this.compareList.length > 0) {
            compareBar.style.display = 'block';
            compareCount.textContent = this.compareList.length;
            compareBtn.disabled = this.compareList.length < 2;
            
            this.updateCompareItems();
        } else {
            compareBar.style.display = 'none';
        }

        // تحديث حالة جميع الأزرار
        this.compareList.forEach(item => {
            this.updateButtonState(item.id, true);
        });
    }

    // تحديث قائمة العناصر
    updateCompareItems() {
        const container = document.getElementById('compareItems');
        
        if (this.compareList.length === 0) {
            container.style.display = 'none';
            return;
        }

        let html = '';
        this.compareList.forEach(item => {
            html += `
                <div class="compare-item">
                    <div class="compare-item-info">
                        ${item.image ? `<img src="${item.image}" alt="${item.title}" class="compare-item-image">` : '<div class="compare-item-image bg-secondary d-flex align-items-center justify-content-center"><i class="fas fa-image text-white"></i></div>'}
                        <div class="compare-item-details">
                            <h6>${item.title}</h6>
                            <small>${item.price ? item.price : '<?php echo e(__("Price not specified")); ?>'}</small>
                        </div>
                    </div>
                    <button class="compare-item-remove" onclick="compareSystem.remove(${item.id})" title="<?php echo e(__('Remove')); ?>">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        });

        container.innerHTML = html;
        container.style.display = 'block';
    }

    // إزالة عنصر محدد
    remove(adId) {
        const index = this.compareList.findIndex(item => item.id == adId);
        if (index > -1) {
            this.compareList.splice(index, 1);
            this.updateButtonState(adId, false);
            this.saveToStorage();
            this.updateUI();
            
            if (typeof showToast === 'function') {
                showToast('<?php echo e(__("Removed from comparison")); ?>', 'info');
            }
        }
    }

    // مسح القائمة
    clear() {
        this.compareList.forEach(item => {
            this.updateButtonState(item.id, false);
        });
        
        this.compareList = [];
        this.saveToStorage();
        this.updateUI();
        
        if (typeof showToast === 'function') {
            showToast('<?php echo e(__("Comparison list cleared")); ?>', 'info');
        }
    }

    // الانتقال لصفحة المقارنة
    goToComparePage() {
        if (this.compareList.length < 2) {
            if (typeof showToast === 'function') {
                showToast('<?php echo e(__("Select at least 2 items to compare")); ?>', 'warning');
            } else {
                alert('<?php echo e(__("Select at least 2 items to compare")); ?>');
            }
            return;
        }

        const ids = this.compareList.map(item => item.id).join(',');
        window.open(`<?php echo e(route('compare.index')); ?>?ids=${ids}`, '_blank');
    }

    // ربط الأحداث
    bindEvents() {
        // إخفاء الشريط عند النقر خارجه
        document.addEventListener('click', (e) => {
            const compareBar = document.getElementById('compareBar');
            if (compareBar && !compareBar.contains(e.target) && !e.target.closest('.compare-btn')) {
                // يمكن إضافة منطق إخفاء تلقائي هنا إذا رغبت
            }
        });
    }
}

// إنشاء نسخة عامة من النظام
let compareSystem;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    compareSystem = new CompareSystem();
});

// دوال عامة للاستخدام
function toggleCompare(adId) {
    if (compareSystem) {
        compareSystem.toggle(adId);
    }
}

function clearCompareList() {
    if (compareSystem) {
        compareSystem.clear();
    }
}

function goToComparePage() {
    if (compareSystem) {
        compareSystem.goToComparePage();
    }
}

function hideCompareBar() {
    document.getElementById('compareBar').style.display = 'none';
}
</script>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/compare-bar.blade.php ENDPATH**/ ?>