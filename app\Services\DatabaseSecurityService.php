<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use PDO;

/**
 * خدمة أمان قاعدة البيانات
 * تدير فحص وتحسين أمان قاعدة البيانات
 */
class DatabaseSecurityService
{
    /**
     * فحص أمان قاعدة البيانات
     */
    public static function performSecurityAudit(): array
    {
        $results = [
            'connection_security' => self::checkConnectionSecurity(),
            'user_privileges' => self::checkUserPrivileges(),
            'table_security' => self::checkTableSecurity(),
            'query_security' => self::checkQuerySecurity(),
            'backup_security' => self::checkBackupSecurity(),
            'recommendations' => [],
        ];

        // إنشاء التوصيات بناءً على النتائج
        $results['recommendations'] = self::generateRecommendations($results);

        return $results;
    }

    /**
     * فحص أمان الاتصال
     */
    private static function checkConnectionSecurity(): array
    {
        $config = config('database.connections.' . config('database.default'));
        $issues = [];
        $score = 100;

        // التحقق من استخدام SSL
        if (!isset($config['options'][PDO::MYSQL_ATTR_SSL_CA])) {
            $issues[] = 'SSL غير مفعل للاتصال بقاعدة البيانات';
            $score -= 20;
        }

        // التحقق من كلمة مرور قوية
        if (empty($config['password']) || strlen($config['password']) < 12) {
            $issues[] = 'كلمة مرور قاعدة البيانات ضعيفة';
            $score -= 30;
        }

        // التحقق من المضيف
        if ($config['host'] === '0.0.0.0' || $config['host'] === '*') {
            $issues[] = 'مضيف قاعدة البيانات غير آمن';
            $score -= 25;
        }

        // التحقق من المنفذ الافتراضي
        if ($config['port'] == '3306' && config('app.env') === 'production') {
            $issues[] = 'استخدام المنفذ الافتراضي لقاعدة البيانات في الإنتاج';
            $score -= 10;
        }

        return [
            'score' => max(0, $score),
            'issues' => $issues,
            'ssl_enabled' => isset($config['options'][PDO::MYSQL_ATTR_SSL_CA]),
            'host' => $config['host'],
            'port' => $config['port'],
        ];
    }

    /**
     * فحص صلاحيات المستخدم
     */
    private static function checkUserPrivileges(): array
    {
        try {
            $privileges = DB::select('SHOW GRANTS');
            $issues = [];
            $score = 100;

            foreach ($privileges as $privilege) {
                $grant = $privilege->{'Grants for ' . config('database.connections.mysql.username') . '@%'} ?? 
                        $privilege->{'Grants for ' . config('database.connections.mysql.username') . '@localhost'} ?? 
                        '';

                // التحقق من الصلاحيات الخطيرة
                if (stripos($grant, 'ALL PRIVILEGES') !== false) {
                    $issues[] = 'المستخدم لديه جميع الصلاحيات - خطر أمني';
                    $score -= 40;
                }

                if (stripos($grant, 'SUPER') !== false) {
                    $issues[] = 'المستخدم لديه صلاحية SUPER - خطر أمني';
                    $score -= 30;
                }

                if (stripos($grant, 'FILE') !== false) {
                    $issues[] = 'المستخدم لديه صلاحية FILE - خطر أمني';
                    $score -= 25;
                }

                if (stripos($grant, 'PROCESS') !== false) {
                    $issues[] = 'المستخدم لديه صلاحية PROCESS - قد يكون خطر أمني';
                    $score -= 15;
                }
            }

            return [
                'score' => max(0, $score),
                'issues' => $issues,
                'privileges' => $privileges,
            ];

        } catch (\Exception $e) {
            return [
                'score' => 0,
                'issues' => ['لا يمكن فحص صلاحيات المستخدم: ' . $e->getMessage()],
                'privileges' => [],
            ];
        }
    }

    /**
     * فحص أمان الجداول
     */
    private static function checkTableSecurity(): array
    {
        $issues = [];
        $score = 100;

        // الحصول على قائمة الجداول بطريقة متوافقة
        try {
            $tables = DB::select('SHOW TABLES');
        } catch (\Exception $e) {
            return [
                'score' => 0,
                'issues' => ['لا يمكن الوصول لقائمة الجداول: ' . $e->getMessage()],
                'tables_count' => 0,
            ];
        }

        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];

            // التحقق من وجود فهارس على الحقول الحساسة
            try {
                $columns = Schema::getColumnListing($tableName);
            } catch (\Exception $e) {
                continue; // تخطي الجدول في حالة وجود خطأ
            }
            
            if (in_array('email', $columns)) {
                $indexes = DB::select("SHOW INDEX FROM {$tableName} WHERE Column_name = 'email'");
                if (empty($indexes)) {
                    $issues[] = "جدول {$tableName}: لا يوجد فهرس على حقل البريد الإلكتروني";
                    $score -= 5;
                }
            }

            if (in_array('password', $columns)) {
                // التحقق من تشفير كلمات المرور
                $samplePassword = DB::table($tableName)->whereNotNull('password')->first();
                if ($samplePassword && strlen($samplePassword->password) < 60) {
                    $issues[] = "جدول {$tableName}: كلمات المرور قد تكون غير مشفرة بشكل صحيح";
                    $score -= 20;
                }
            }
        }

        return [
            'score' => max(0, $score),
            'issues' => $issues,
            'tables_count' => count($tables),
        ];
    }

    /**
     * فحص أمان الاستعلامات
     */
    private static function checkQuerySecurity(): array
    {
        $issues = [];
        $score = 100;

        // فحص إعدادات PDO
        $config = config('database.connections.' . config('database.default'));
        
        if (!isset($config['options'][PDO::ATTR_EMULATE_PREPARES]) || 
            $config['options'][PDO::ATTR_EMULATE_PREPARES] !== false) {
            $issues[] = 'PDO::ATTR_EMULATE_PREPARES لم يتم تعطيله - قد يسمح بـ SQL Injection';
            $score -= 30;
        }

        if (!isset($config['options'][PDO::ATTR_STRINGIFY_FETCHES]) || 
            $config['options'][PDO::ATTR_STRINGIFY_FETCHES] !== false) {
            $issues[] = 'PDO::ATTR_STRINGIFY_FETCHES لم يتم تعطيله';
            $score -= 10;
        }

        // التحقق من strict mode
        if (!$config['strict']) {
            $issues[] = 'Strict mode غير مفعل - قد يسمح ببيانات غير صحيحة';
            $score -= 20;
        }

        return [
            'score' => max(0, $score),
            'issues' => $issues,
            'prepared_statements' => true, // Laravel يستخدم prepared statements افتراضياً
        ];
    }

    /**
     * فحص أمان النسخ الاحتياطية
     */
    private static function checkBackupSecurity(): array
    {
        $issues = [];
        $score = 100;

        // التحقق من وجود إعدادات النسخ الاحتياطية
        if (!config('backup.backup.destination.disks')) {
            $issues[] = 'لا توجد إعدادات للنسخ الاحتياطية';
            $score -= 40;
        }

        // التحقق من تشفير النسخ الاحتياطية
        if (!config('backup.backup.password')) {
            $issues[] = 'النسخ الاحتياطية غير مشفرة';
            $score -= 30;
        }

        return [
            'score' => max(0, $score),
            'issues' => $issues,
        ];
    }

    /**
     * إنشاء التوصيات
     */
    private static function generateRecommendations(array $results): array
    {
        $recommendations = [];

        // توصيات الاتصال
        if ($results['connection_security']['score'] < 80) {
            $recommendations[] = [
                'priority' => 'high',
                'category' => 'connection',
                'title' => 'تحسين أمان الاتصال',
                'description' => 'فعل SSL، استخدم كلمة مرور قوية، غير المنفذ الافتراضي',
            ];
        }

        // توصيات الصلاحيات
        if ($results['user_privileges']['score'] < 70) {
            $recommendations[] = [
                'priority' => 'critical',
                'category' => 'privileges',
                'title' => 'تقليل صلاحيات المستخدم',
                'description' => 'امنح المستخدم الصلاحيات الضرورية فقط (SELECT, INSERT, UPDATE, DELETE)',
            ];
        }

        // توصيات الجداول
        if ($results['table_security']['score'] < 90) {
            $recommendations[] = [
                'priority' => 'medium',
                'category' => 'tables',
                'title' => 'تحسين أمان الجداول',
                'description' => 'أضف فهارس على الحقول الحساسة، تأكد من تشفير كلمات المرور',
            ];
        }

        // توصيات النسخ الاحتياطية
        if ($results['backup_security']['score'] < 60) {
            $recommendations[] = [
                'priority' => 'high',
                'category' => 'backup',
                'title' => 'إعداد النسخ الاحتياطية الآمنة',
                'description' => 'فعل النسخ الاحتياطية المشفرة والمجدولة',
            ];
        }

        return $recommendations;
    }

    /**
     * تطبيق إصلاحات أمنية تلقائية
     */
    public static function applySecurityFixes(): array
    {
        $applied = [];

        try {
            // تفعيل strict mode إذا لم يكن مفعلاً
            DB::statement("SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
            $applied[] = 'تم تفعيل strict mode';

            // تعطيل local_infile
            DB::statement("SET GLOBAL local_infile = 0");
            $applied[] = 'تم تعطيل local_infile';

        } catch (\Exception $e) {
            Log::warning('فشل في تطبيق بعض الإصلاحات الأمنية: ' . $e->getMessage());
        }

        return $applied;
    }
}
