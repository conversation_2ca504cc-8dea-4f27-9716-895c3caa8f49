<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * موديل Review مؤقت للتوافق مع النظام القديم
 * يعيد التوجيه إلى نظام Interaction الجديد
 */
class Review extends Model
{
    /**
     * الحصول على متوسط التقييم لكائن معين
     */
    public static function getAverageRating($reviewable): float
    {
        if ($reviewable instanceof User) {
            // للمستخدمين
            $ratings = Interaction::where('target_user_id', $reviewable->id)
                ->where('type', 'rating')
                ->get()
                ->pluck('data.rating')
                ->filter();
        } else {
            // للإعلانات
            $ratings = Interaction::where('ad_id', $reviewable->id)
                ->where('type', 'rating')
                ->get()
                ->pluck('data.rating')
                ->filter();
        }

        return $ratings->count() > 0 ? round($ratings->average(), 2) : 0;
    }

    /**
     * الحصول على عدد التقييمات لكائن معين
     */
    public static function getReviewsCount($reviewable): int
    {
        if ($reviewable instanceof User) {
            // للمستخدمين
            return Interaction::where('target_user_id', $reviewable->id)
                ->where('type', 'rating')
                ->count();
        } else {
            // للإعلانات
            return Interaction::where('ad_id', $reviewable->id)
                ->where('type', 'rating')
                ->count();
        }
    }

    /**
     * التحقق من وجود تقييم من مستخدم معين
     */
    public static function hasUserReviewed(int $userId, $reviewable): bool
    {
        if ($reviewable instanceof User) {
            // للمستخدمين
            return Interaction::where('user_id', $userId)
                ->where('target_user_id', $reviewable->id)
                ->where('type', 'rating')
                ->exists();
        } else {
            // للإعلانات
            return Interaction::where('user_id', $userId)
                ->where('ad_id', $reviewable->id)
                ->where('type', 'rating')
                ->exists();
        }
    }

    /**
     * الحصول على تقييم المستخدم
     */
    public static function getUserReview(int $userId, $reviewable): ?Interaction
    {
        if ($reviewable instanceof User) {
            // للمستخدمين
            return Interaction::where('user_id', $userId)
                ->where('target_user_id', $reviewable->id)
                ->where('type', 'rating')
                ->first();
        } else {
            // للإعلانات
            return Interaction::where('user_id', $userId)
                ->where('ad_id', $reviewable->id)
                ->where('type', 'rating')
                ->first();
        }
    }

    /**
     * إنشاء تقييم جديد
     */
    public static function createReview(
        int $userId,
        $reviewable,
        int $rating,
        ?string $title = null,
        ?string $comment = null
    ): Interaction {
        $data = [
            'rating' => $rating,
        ];

        if ($title) {
            $data['title'] = $title;
        }

        if ($comment) {
            $data['comment'] = $comment;
        }

        $interactionData = [
            'user_id' => $userId,
            'type' => 'rating',
            'data' => $data,
        ];

        if ($reviewable instanceof User) {
            $interactionData['target_user_id'] = $reviewable->id;
        } else {
            $interactionData['ad_id'] = $reviewable->id;
        }

        return Interaction::create($interactionData);
    }

    /**
     * الحصول على إحصائيات التقييمات
     */
    public static function getStats(): array
    {
        $totalRatings = Interaction::where('type', 'rating')->count();
        $averageRating = Interaction::where('type', 'rating')
            ->get()
            ->pluck('data.rating')
            ->filter()
            ->average();

        return [
            'total' => $totalRatings,
            'approved' => $totalRatings, // جميع التقييمات معتمدة في النظام الجديد
            'pending' => 0,
            'average_rating' => $averageRating ? round($averageRating, 2) : 0,
        ];
    }

    /**
     * علاقة المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * علاقة polymorphic للكائن المُقيَّم
     */
    public function reviewable()
    {
        return $this->morphTo();
    }

    /**
     * scope للتقييمات المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query; // جميع التقييمات معتمدة في النظام الجديد
    }

    /**
     * اعتماد التقييم (لا يفعل شيئاً في النظام الجديد)
     */
    public function approve()
    {
        return true;
    }

    /**
     * رفض التقييم (لا يفعل شيئاً في النظام الجديد)
     */
    public function reject()
    {
        return true;
    }

    /**
     * خصائص وهمية للتوافق
     */
    public function getIdAttribute()
    {
        return $this->attributes['id'] ?? 0;
    }

    public function getUserIdAttribute()
    {
        return $this->attributes['user_id'] ?? 0;
    }

    public function getRatingAttribute()
    {
        return $this->attributes['rating'] ?? 0;
    }

    public function getTitleAttribute()
    {
        return $this->attributes['title'] ?? '';
    }

    public function getCommentAttribute()
    {
        return $this->attributes['comment'] ?? '';
    }
}
