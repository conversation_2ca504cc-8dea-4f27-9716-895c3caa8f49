<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration شامل لإنشاء هيكل قاعدة بيانات محسن ومنظم
 * يحل محل جميع الـ migrations المتناثرة ويوحد الهيكل
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. جدول المستخدمين الموحد
        $this->createUsersTable();
        
        // 2. جدول التصنيفات
        $this->createCategoriesTable();
        
        // 3. جدول الإعلانات المحسن
        $this->createAdsTable();
        
        // 4. جدول التفاعلات (مفضلة، تقييمات، تعليقات)
        $this->createInteractionsTable();
        
        // 5. جدول الإشعارات الموحد
        $this->createNotificationsTable();
        
        // 6. جدول السجلات الموحد
        $this->createSystemLogsTable();
        
        // 7. جدول الإعدادات الموحد
        $this->createSettingsTable();
        
        // 8. جداول النظام الأساسية
        $this->createSystemTables();
    }

    /**
     * إنشاء جدول المستخدمين الموحد
     */
    private function createUsersTable(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            
            // المعلومات الأساسية
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();
            
            // معلومات الأمان
            $table->timestamp('password_changed_at')->nullable();
            $table->timestamp('last_login_at')->nullable();
            $table->string('last_login_ip', 45)->nullable();
            $table->string('registration_ip', 45)->nullable();
            $table->integer('failed_login_attempts')->default(0);
            $table->timestamp('locked_until')->nullable();
            $table->boolean('force_password_change')->default(false);
            $table->json('password_history')->nullable();
            
            // معلومات الدفع (مشفرة)
            $table->text('card_number')->nullable(); // مشفر
            $table->string('card_type')->nullable();
            $table->enum('preferred_currency', ['YER', 'USD', 'SAR', 'AED', 'EGP'])->default('YER');
            
            // الصلاحيات
            $table->boolean('is_admin')->default(false);
            $table->boolean('is_active')->default(true);
            
            // معلومات التواصل الأساسية (مشفرة)
            $table->text('phone')->nullable(); // مشفر
            $table->text('whatsapp')->nullable(); // مشفر
            
            // إعدادات الخصوصية
            $table->json('privacy_settings')->nullable();
            
            $table->timestamps();
            
            // الفهارس المحسنة
            $table->index(['email', 'is_active']);
            $table->index(['last_login_at']);
            $table->index(['is_admin']);
            $table->index(['created_at']);
        });
    }

    /**
     * إنشاء جدول التصنيفات
     */
    private function createCategoriesTable(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            
            // أسماء التصنيفات
            $table->string('name_ar');
            $table->string('name_en');
            $table->string('slug')->unique();
            
            // معلومات إضافية
            $table->string('icon')->default('fas fa-folder');
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            
            // التسلسل الهرمي
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->integer('sort_order')->default(0);
            
            // الحالة
            $table->boolean('is_active')->default(true);
            
            // إحصائيات (محسوبة)
            $table->integer('ads_count')->default(0);
            
            $table->timestamps();
            
            // العلاقات والفهارس
            $table->foreign('parent_id')->references('id')->on('categories')->onDelete('set null');
            $table->index(['is_active', 'sort_order']);
            $table->index(['parent_id', 'is_active']);
        });
    }

    /**
     * إنشاء جدول الإعلانات المحسن
     */
    private function createAdsTable(): void
    {
        Schema::create('ads', function (Blueprint $table) {
            $table->id();
            
            // المعلومات الأساسية
            $table->string('title_ar');
            $table->string('title_en');
            $table->text('description_ar');
            $table->text('description_en');
            $table->string('slug')->unique();
            
            // العلاقات
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            
            // الصور (JSON array للصور المتعددة)
            $table->json('images')->nullable();
            $table->string('main_image')->nullable();
            
            // معلومات السعر الموحدة
            $table->decimal('price', 12, 2)->nullable();
            $table->decimal('original_price', 12, 2)->nullable();
            $table->enum('currency', ['YER', 'USD', 'SAR', 'AED', 'EGP'])->default('YER');
            $table->enum('price_type', ['fixed', 'negotiable', 'free', 'on_request'])->default('fixed');
            $table->boolean('is_negotiable')->default(false);
            $table->text('price_notes')->nullable();
            
            // معلومات التواصل (مشفرة)
            $table->text('contact_phone')->nullable(); // مشفر
            $table->text('contact_email')->nullable(); // مشفر
            $table->text('contact_whatsapp')->nullable(); // مشفر
            
            // الموقع
            $table->string('location')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->default('Yemen');
            
            // الحالة والإعدادات
            $table->enum('status', ['pending', 'active', 'inactive', 'rejected', 'expired'])->default('pending');
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_urgent')->default(false);
            $table->date('expires_at')->nullable();
            $table->text('rejection_reason')->nullable();
            
            // الإحصائيات
            $table->integer('views_count')->default(0);
            $table->integer('contact_reveals_count')->default(0);
            $table->integer('favorites_count')->default(0);
            $table->decimal('rating_average', 3, 2)->default(0);
            $table->integer('rating_count')->default(0);
            
            $table->timestamps();
            
            // الفهارس المحسنة
            $table->index(['status', 'expires_at', 'is_featured']);
            $table->index(['category_id', 'status', 'created_at']);
            $table->index(['user_id', 'status']);
            $table->index(['location', 'status']);
            $table->index(['is_featured', 'status', 'created_at']);
            $table->index(['views_count']);
            $table->index(['created_at']);
        });
    }

    /**
     * إنشاء جدول التفاعلات الموحد
     */
    private function createInteractionsTable(): void
    {
        Schema::create('interactions', function (Blueprint $table) {
            $table->id();
            
            // العلاقات
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('ad_id')->constrained()->onDelete('cascade');
            
            // نوع التفاعل
            $table->enum('type', ['favorite', 'rating', 'comment', 'view', 'contact_reveal']);
            
            // بيانات التفاعل (JSON مرن)
            $table->json('data')->nullable(); // للتقييم: {rating: 5, comment: "ممتاز"}
            
            // معلومات إضافية
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            
            $table->timestamps();
            
            // فهارس وقيود
            $table->index(['ad_id', 'type']);
            $table->index(['user_id', 'type']);
            $table->index(['created_at']);

            // منع التكرار للمفضلة والتقييم وكشف التواصل فقط (التعليقات والمشاهدات مسموحة بالتكرار)
            // سنضيف القيود في الكود بدلاً من قاعدة البيانات للمرونة
        });
    }

    /**
     * إنشاء جدول الإشعارات الموحد
     */
    private function createNotificationsTable(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            
            // المستقبل
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            // محتوى الإشعار
            $table->string('title');
            $table->text('message');
            $table->enum('type', ['info', 'success', 'warning', 'error', 'ad_status', 'system']);
            
            // بيانات إضافية
            $table->json('data')->nullable(); // معرف الإعلان، روابط، إلخ
            $table->string('action_url')->nullable();
            
            // الحالة
            $table->timestamp('read_at')->nullable();
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            
            $table->timestamps();
            
            // الفهارس
            $table->index(['user_id', 'read_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['type', 'created_at']);
        });
    }

    /**
     * إنشاء جدول السجلات الموحد
     */
    private function createSystemLogsTable(): void
    {
        Schema::create('system_logs', function (Blueprint $table) {
            $table->id();
            
            // نوع السجل
            $table->enum('type', ['security', 'search', 'contact_access', 'admin_action', 'error', 'audit']);
            
            // المستخدم (اختياري)
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            
            // تفاصيل السجل
            $table->string('action');
            $table->text('description')->nullable();
            $table->json('data')->nullable(); // بيانات إضافية مرنة
            
            // معلومات الطلب
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('url')->nullable();
            
            // مستوى الخطورة
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium');
            
            $table->timestamps();
            
            // الفهارس
            $table->index(['type', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['severity', 'created_at']);
            $table->index(['ip_address']);
        });
    }

    /**
     * إنشاء جدول الإعدادات الموحد
     */
    private function createSettingsTable(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            
            // مفتاح الإعداد
            $table->string('key')->unique();
            
            // قيمة الإعداد
            $table->text('value')->nullable();
            
            // نوع الإعداد
            $table->enum('type', ['string', 'integer', 'boolean', 'json', 'text']);
            
            // تصنيف الإعداد
            $table->string('group')->default('general'); // general, ads, announcements, security
            
            // وصف الإعداد
            $table->string('description')->nullable();
            
            // هل يمكن تعديله من الواجهة
            $table->boolean('is_editable')->default(true);
            
            $table->timestamps();
            
            // الفهارس
            $table->index(['group']);
            $table->index(['is_editable']);
        });
    }

    /**
     * إنشاء الجداول الأساسية للنظام
     */
    private function createSystemTables(): void
    {
        // جدول reset tokens
        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        // جدول الجلسات
        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });

        // جدول الكاش
        Schema::create('cache', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->mediumText('value');
            $table->integer('expiration');
        });

        Schema::create('cache_locks', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->string('owner');
            $table->integer('expiration');
        });

        // جدول المهام
        Schema::create('jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('queue')->index();
            $table->longText('payload');
            $table->unsignedTinyInteger('attempts');
            $table->unsignedInteger('reserved_at')->nullable();
            $table->unsignedInteger('available_at');
            $table->unsignedInteger('created_at');
        });

        Schema::create('job_batches', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->integer('total_jobs');
            $table->integer('pending_jobs');
            $table->integer('failed_jobs');
            $table->longText('failed_job_ids');
            $table->mediumText('options')->nullable();
            $table->integer('cancelled_at')->nullable();
            $table->integer('created_at');
            $table->integer('finished_at')->nullable();
        });

        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('failed_jobs');
        Schema::dropIfExists('job_batches');
        Schema::dropIfExists('jobs');
        Schema::dropIfExists('cache_locks');
        Schema::dropIfExists('cache');
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('settings');
        Schema::dropIfExists('system_logs');
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('interactions');
        Schema::dropIfExists('ads');
        Schema::dropIfExists('categories');
        Schema::dropIfExists('users');
    }
};
