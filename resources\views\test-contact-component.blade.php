@extends('layouts.app')

@section('title', 'اختبار مكون معلومات التواصل المحدث')

@section('content')
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h3><i class="fas fa-address-card me-2"></i>اختبار مكون معلومات التواصل المحدث</h3>
                    <p class="mb-0">اختبار عرض معلومات التواصل من جدول user_contacts</p>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات الاختبار:</h5>
                        <ul class="mb-0">
                            <li>المكون محدث ليستخدم جدول <code>user_contacts</code> بدلاً من <code>ads</code></li>
                            <li>يدعم جميع أنواع التواصل الجديدة (تليجرام، إنستجرام، فيسبوك، إلخ)</li>
                            <li>يطبق إعدادات الخصوصية والأمان</li>
                            <li>يسجل جميع محاولات الوصول لمعلومات التواصل</li>
                        </ul>
                    </div>

                    <!-- اختبار مع إعلانات وهمية -->
                    <div class="row">
                        @foreach(\App\Models\Ad::with('user.contacts')->limit(3)->get() as $ad)
                            <div class="col-md-4 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">{{ $ad->title }}</h6>
                                        <small class="text-muted">بواسطة: {{ $ad->user->name }}</small>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">{{ Str::limit($ad->description, 100) }}</p>
                                        
                                        <!-- عرض مكون التواصل المحدث -->
                                        <div class="border p-3 bg-light rounded">
                                            <h6 class="text-primary mb-3">معلومات التواصل:</h6>
                                            
                                            @if($ad->user->contacts->count() > 0)
                                                <!-- اختبار العرض البسيط -->
                                                <div class="mb-3">
                                                    <strong>العرض البسيط:</strong>
                                                    <x-ad-card.partials.contact 
                                                        :ad="$ad" 
                                                        :config="['variant' => 'default']"
                                                        :showIcons="true"
                                                        :showDetails="false"
                                                        iconStyle="circular"
                                                    />
                                                </div>

                                                <!-- اختبار العرض المفصل -->
                                                <div class="mb-3">
                                                    <strong>العرض المفصل:</strong>
                                                    <x-ad-card.partials.contact 
                                                        :ad="$ad" 
                                                        :config="['variant' => 'detailed']"
                                                        :showIcons="true"
                                                        :showDetails="true"
                                                        :showReveal="true"
                                                        iconStyle="circular"
                                                    />
                                                </div>

                                                <!-- اختبار العرض المضغوط -->
                                                <div class="mb-3">
                                                    <strong>العرض المضغوط:</strong>
                                                    <x-ad-card.partials.contact 
                                                        :ad="$ad" 
                                                        :config="['variant' => 'compact']"
                                                        :showIcons="false"
                                                        :showDetails="true"
                                                        iconStyle="minimal"
                                                    />
                                                </div>
                                            @else
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    لا توجد معلومات تواصل لهذا المستخدم
                                                </div>
                                            @endif
                                        </div>

                                        <!-- معلومات إضافية للتشخيص -->
                                        <div class="mt-3">
                                            <small class="text-muted">
                                                <strong>معلومات التشخيص:</strong><br>
                                                - عدد معلومات التواصل: {{ $ad->user->contacts->count() }}<br>
                                                - معلومات عامة: {{ $ad->user->contacts->where('privacy_level', 'public')->count() }}<br>
                                                - معلومات متحققة: {{ $ad->user->contacts->where('is_verified', true)->count() }}<br>
                                                - معلومات أساسية: {{ $ad->user->contacts->where('is_primary', true)->count() }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- اختبار مع بيانات وهمية -->
                    <div class="mt-5">
                        <h5>اختبار مع بيانات وهمية:</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6>مستخدم بمعلومات تواصل كاملة</h6>
                                    </div>
                                    <div class="card-body">
                                        @php
                                            // إنشاء إعلان وهمي للاختبار
                                            $testAd = new \App\Models\Ad([
                                                'id' => 999,
                                                'title' => 'إعلان اختبار',
                                                'description' => 'هذا إعلان للاختبار فقط'
                                            ]);
                                            
                                            // إنشاء مستخدم وهمي مع معلومات تواصل
                                            $testUser = \App\Models\User::first();
                                            $testAd->setRelation('user', $testUser);
                                        @endphp
                                        
                                        <x-ad-card.partials.contact 
                                            :ad="$testAd" 
                                            :config="['variant' => 'detailed']"
                                            :showIcons="true"
                                            :showDetails="true"
                                            :showReveal="true"
                                            iconStyle="circular"
                                        />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6>إحصائيات النظام</h6>
                                    </div>
                                    <div class="card-body">
                                        @php
                                            $totalContacts = \App\Models\UserContact::count();
                                            $publicContacts = \App\Models\UserContact::where('privacy_level', 'public')->count();
                                            $verifiedContacts = \App\Models\UserContact::where('is_verified', true)->count();
                                            $primaryContacts = \App\Models\UserContact::where('is_primary', true)->count();
                                            $contactTypes = \App\Models\UserContact::select('contact_type')
                                                ->groupBy('contact_type')
                                                ->selectRaw('contact_type, count(*) as count')
                                                ->get();
                                        @endphp
                                        
                                        <div class="row text-center">
                                            <div class="col-6 mb-3">
                                                <div class="bg-primary text-white p-2 rounded">
                                                    <h4>{{ $totalContacts }}</h4>
                                                    <small>إجمالي معلومات التواصل</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="bg-success text-white p-2 rounded">
                                                    <h4>{{ $publicContacts }}</h4>
                                                    <small>معلومات عامة</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="bg-info text-white p-2 rounded">
                                                    <h4>{{ $verifiedContacts }}</h4>
                                                    <small>معلومات متحققة</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="bg-warning text-white p-2 rounded">
                                                    <h4>{{ $primaryContacts }}</h4>
                                                    <small>معلومات أساسية</small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <h6 class="mt-3">توزيع أنواع التواصل:</h6>
                                        <div class="list-group">
                                            @foreach($contactTypes as $type)
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    {{ ucfirst($type->contact_type) }}
                                                    <span class="badge bg-primary rounded-pill">{{ $type->count }}</span>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الاختبار -->
                    <div class="mt-4 text-center">
                        <a href="{{ route('test-contacts') }}" class="btn btn-primary me-2">
                            <i class="fas fa-vial me-1"></i>اختبار API
                        </a>
                        <a href="{{ route('test-security') }}" class="btn btn-success me-2">
                            <i class="fas fa-shield-alt me-1"></i>اختبار الأمان
                        </a>
                        <a href="{{ route('auth.profile') }}" class="btn btn-info">
                            <i class="fas fa-user me-1"></i>الملف الشخصي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 صفحة اختبار مكون معلومات التواصل محملة');
    console.log('📊 إحصائيات النظام:');
    console.log('- إجمالي معلومات التواصل: {{ $totalContacts ?? 0 }}');
    console.log('- معلومات عامة: {{ $publicContacts ?? 0 }}');
    console.log('- معلومات متحققة: {{ $verifiedContacts ?? 0 }}');
    
    // اختبار وظائف JavaScript
    if (typeof trackContactClick === 'function') {
        console.log('✅ وظيفة trackContactClick متوفرة');
    } else {
        console.log('❌ وظيفة trackContactClick غير متوفرة');
    }
    
    if (typeof revealContact === 'function') {
        console.log('✅ وظيفة revealContact متوفرة');
    } else {
        console.log('❌ وظيفة revealContact غير متوفرة');
    }
});
</script>
@endsection
