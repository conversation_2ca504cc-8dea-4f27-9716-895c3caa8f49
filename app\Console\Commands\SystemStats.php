<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AdManagementService;
use App\Services\ReviewService;
use App\Services\SearchService;
use App\Services\SecurityMonitor;
use App\Models\User;
use App\Models\Category;
use Illuminate\Support\Facades\DB;

/**
 * أمر عرض إحصائيات النظام الشاملة
 */
class SystemStats extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'system:stats
                            {--detailed : عرض إحصائيات مفصلة}
                            {--export= : تصدير الإحصائيات إلى ملف}';

    /**
     * وصف الأمر
     */
    protected $description = 'عرض إحصائيات النظام الشاملة';

    /**
     * تنفيذ الأمر
     */
    public function handle()
    {
        $this->info('📊 إحصائيات النظام الشاملة');
        $this->info('=' . str_repeat('=', 50));
        $this->newLine();

        $stats = $this->gatherStats();

        $this->displayBasicStats($stats);

        if ($this->option('detailed')) {
            $this->displayDetailedStats($stats);
        }

        if ($exportFile = $this->option('export')) {
            $this->exportStats($stats, $exportFile);
        }

        return 0;
    }

    /**
     * جمع جميع الإحصائيات
     */
    private function gatherStats(): array
    {
        return [
            'users' => $this->getUserStats(),
            'ads' => $this->getAdStats(),
            'categories' => $this->getCategoryStats(),
            'reviews' => $this->getReviewStats(),
            'search' => $this->getSearchStats(),
            'security' => $this->getSecurityStats(),
            'system' => $this->getSystemStats(),
        ];
    }

    /**
     * إحصائيات المستخدمين
     */
    private function getUserStats(): array
    {
        return [
            'total' => User::count(),
            'active_today' => User::whereNotNull('last_login_at')->whereDate('last_login_at', today())->count(),
            'active_week' => User::whereNotNull('last_login_at')->where('last_login_at', '>=', now()->subWeek())->count(),
            'new_today' => User::whereDate('created_at', today())->count(),
            'new_week' => User::where('created_at', '>=', now()->subWeek())->count(),
            'admins' => User::where('is_admin', true)->count(),
        ];
    }

    /**
     * إحصائيات الإعلانات - استخدام Service موجود
     */
    private function getAdStats(): array
    {
        try {
            $stats = AdManagementService::getAdStats();

            // إضافة إحصائيات إضافية للتقرير الشامل
            $stats['new_today'] = DB::table('ads')->whereDate('created_at', today())->count();
            $stats['new_week'] = DB::table('ads')->where('created_at', '>=', now()->subWeek())->count();
            $stats['avg_views'] = round(DB::table('ads')->avg('views_count') ?? 0, 2);

            return $stats;
        } catch (\Exception $e) {
            // Fallback في حالة فشل Service
            return [
                'total' => DB::table('ads')->count(),
                'active' => DB::table('ads')->where('status', 'active')->count(),
                'pending' => DB::table('ads')->where('status', 'pending')->count(),
                'total_views' => DB::table('ads')->sum('views_count') ?? 0,
                'new_today' => DB::table('ads')->whereDate('created_at', today())->count(),
                'new_week' => DB::table('ads')->where('created_at', '>=', now()->subWeek())->count(),
                'avg_views' => round(DB::table('ads')->avg('views_count') ?? 0, 2),
            ];
        }
    }

    /**
     * إحصائيات التصنيفات
     */
    private function getCategoryStats(): array
    {
        return [
            'total' => Category::count(),
            'active' => Category::where('is_active', true)->count(),
            'with_ads' => Category::has('ads')->count(),
            'most_popular' => DB::table('categories')
                ->join('ads', 'categories.id', '=', 'ads.category_id')
                ->select('categories.name_ar', DB::raw('COUNT(ads.id) as ads_count'))
                ->groupBy('categories.id', 'categories.name_ar')
                ->orderBy('ads_count', 'desc')
                ->limit(5)
                ->get()
                ->toArray(),
        ];
    }

    /**
     * إحصائيات التقييمات - استخدام Service موجود
     */
    private function getReviewStats(): array
    {
        try {
            return ReviewService::getReviewStats();
        } catch (\Exception $e) {
            // Fallback في حالة فشل Service
            return [
                'total' => DB::table('reviews')->count(),
                'approved' => DB::table('reviews')->where('is_approved', true)->count(),
                'pending' => DB::table('reviews')->where('is_approved', false)->count(),
                'average_rating' => round(DB::table('reviews')->where('is_approved', true)->avg('rating') ?? 0, 2),
            ];
        }
    }

    /**
     * إحصائيات البحث - استخدام Service موجود
     */
    private function getSearchStats(): array
    {
        try {
            return SearchService::getSearchStats();
        } catch (\Exception $e) {
            // Fallback في حالة فشل Service
            return [
                'total_searches' => DB::table('search_logs')->count(),
                'searches_today' => DB::table('search_logs')->whereDate('created_at', today())->count(),
                'top_terms' => [],
            ];
        }
    }

    /**
     * إحصائيات الأمان - استخدام Service موجود
     */
    private function getSecurityStats(): array
    {
        try {
            return SecurityMonitor::getSecurityStats();
        } catch (\Exception $e) {
            // Fallback في حالة فشل Service
            return [
                'total_events' => DB::table('security_logs')->count(),
                'critical_events' => DB::table('security_logs')->where('severity', 'critical')->count(),
                'unresolved_events' => DB::table('security_logs')->where('resolved', false)->count(),
            ];
        }
    }

    /**
     * إحصائيات النظام
     */
    private function getSystemStats(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'database_size' => $this->getDatabaseSize(),
            'storage_size' => $this->getStorageSize(),
            'uptime' => $this->getUptime(),
        ];
    }

    /**
     * عرض الإحصائيات الأساسية
     */
    private function displayBasicStats(array $stats): void
    {
        // إحصائيات المستخدمين
        $this->info('👥 المستخدمون:');
        $this->line("   إجمالي المستخدمين: " . number_format($stats['users']['total']));
        $this->line("   نشطون اليوم: " . number_format($stats['users']['active_today']));
        $this->line("   مستخدمون جدد هذا الأسبوع: " . number_format($stats['users']['new_week']));
        $this->newLine();

        // إحصائيات الإعلانات
        $this->info('📢 الإعلانات:');
        $this->line("   إجمالي الإعلانات: " . number_format($stats['ads']['total']));
        $this->line("   الإعلانات النشطة: " . number_format($stats['ads']['active']));
        $this->line("   في انتظار المراجعة: " . number_format($stats['ads']['pending']));
        $this->line("   إجمالي المشاهدات: " . number_format($stats['ads']['total_views']));
        $this->newLine();

        // إحصائيات التقييمات
        $this->info('⭐ التقييمات:');
        $this->line("   إجمالي التقييمات: " . number_format($stats['reviews']['total']));
        $this->line("   التقييمات المعتمدة: " . number_format($stats['reviews']['approved']));
        $this->line("   متوسط التقييم: " . $stats['reviews']['average_rating']);
        $this->newLine();

        // إحصائيات البحث
        $this->info('🔍 البحث:');
        $this->line("   إجمالي عمليات البحث: " . number_format($stats['search']['total_searches']));
        $this->line("   عمليات البحث اليوم: " . number_format($stats['search']['searches_today']));
        $this->newLine();

        // إحصائيات الأمان
        $this->info('🔒 الأمان:');
        $this->line("   الأحداث الأمنية: " . number_format($stats['security']['total_events']));
        $this->line("   الأحداث الحرجة: " . number_format($stats['security']['critical_events']));
        $this->line("   الأحداث غير المحلولة: " . number_format($stats['security']['unresolved_events']));
        $this->newLine();
    }

    /**
     * عرض الإحصائيات المفصلة
     */
    private function displayDetailedStats(array $stats): void
    {
        $this->info('📋 إحصائيات مفصلة:');
        $this->info('-' . str_repeat('-', 30));
        $this->newLine();

        // التصنيفات الأكثر شعبية
        $this->info('🏆 التصنيفات الأكثر شعبية:');
        foreach ($stats['categories']['most_popular'] as $category) {
            $this->line("   {$category['name_ar']}: {$category['ads_count']} إعلان");
        }
        $this->newLine();

        // أهم مصطلحات البحث
        $this->info('🔥 أهم مصطلحات البحث:');
        foreach ($stats['search']['top_terms'] as $term) {
            $this->line("   {$term['search_term']}: {$term['count']} مرة");
        }
        $this->newLine();

        // معلومات النظام
        $this->info('⚙️  معلومات النظام:');
        $this->line("   إصدار PHP: " . $stats['system']['php_version']);
        $this->line("   إصدار Laravel: " . $stats['system']['laravel_version']);
        $this->line("   حجم قاعدة البيانات: " . $stats['system']['database_size']);
        $this->line("   حجم التخزين: " . $stats['system']['storage_size']);
        $this->newLine();
    }

    /**
     * تصدير الإحصائيات
     */
    private function exportStats(array $stats, string $filename): void
    {
        try {
            $content = "إحصائيات النظام - " . now()->format('Y-m-d H:i:s') . "\n";
            $content .= str_repeat('=', 50) . "\n\n";

            $content .= json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            file_put_contents($filename, $content);

            $this->info("✅ تم تصدير الإحصائيات إلى: {$filename}");

        } catch (\Exception $e) {
            $this->error("❌ فشل في تصدير الإحصائيات: " . $e->getMessage());
        }
    }

    /**
     * الحصول على حجم قاعدة البيانات
     */
    private function getDatabaseSize(): string
    {
        try {
            $size = DB::select("SELECT
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()")[0]->size_mb ?? 0;

            return $size . ' MB';
        } catch (\Exception $e) {
            return 'غير متاح';
        }
    }

    /**
     * الحصول على حجم التخزين
     */
    private function getStorageSize(): string
    {
        try {
            $path = storage_path();
            $size = $this->getDirectorySize($path);
            return $this->formatBytes($size);
        } catch (\Exception $e) {
            return 'غير متاح';
        }
    }

    /**
     * الحصول على وقت التشغيل
     */
    private function getUptime(): string
    {
        try {
            $uptime = file_get_contents('/proc/uptime');
            $seconds = (int)explode(' ', $uptime)[0];

            $days = floor($seconds / 86400);
            $hours = floor(($seconds % 86400) / 3600);
            $minutes = floor(($seconds % 3600) / 60);

            return "{$days} يوم، {$hours} ساعة، {$minutes} دقيقة";
        } catch (\Exception $e) {
            return 'غير متاح';
        }
    }

    /**
     * حساب حجم المجلد
     */
    private function getDirectorySize(string $path): int
    {
        $size = 0;
        foreach (glob(rtrim($path, '/').'/*', GLOB_NOSORT) as $each) {
            $size += is_file($each) ? filesize($each) : $this->getDirectorySize($each);
        }
        return $size;
    }

    /**
     * تنسيق حجم الملف
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
