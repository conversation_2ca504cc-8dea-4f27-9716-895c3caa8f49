/**
 * ملف JavaScript محسن لصفحة تفاصيل الإعلان
 * تم إنشاؤه كجزء من خطة تحسين تجربة المستخدم العامة
 * يحتوي على تحسينات للأداء والتفاعل وإمكانية الوصول
 */

class AdDetailsEnhancer {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.setupIntersectionObserver();
        this.setupImageLazyLoading();
        this.setupPerformanceOptimizations();
    }

    /**
     * تهيئة الكلاس
     */
    init() {
        this.isLoading = false;
        this.imageGallery = document.querySelector('.ad-image-gallery');
        this.mainImage = document.querySelector('.ad-main-image');
        this.thumbnails = document.querySelectorAll('.thumbnail');
        this.actionButtons = document.querySelectorAll('.action-button');
        this.contentCards = document.querySelectorAll('.content-card');
        this.sidebarCards = document.querySelectorAll('.sidebar-card');
        
        // تطبيق الرسوم المتحركة عند التحميل
        this.applyLoadAnimations();
        
        // تحسين الصور
        this.optimizeImages();
        
        // إعداد معرض الصور
        this.setupImageGallery();
        
        // تحسين الأزرار
        this.enhanceButtons();
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تحسين التمرير
        this.setupSmoothScrolling();
        
        // إعداد اختصارات لوحة المفاتيح
        this.setupKeyboardShortcuts();
        
        // تحسين النقر على الأزرار
        this.setupButtonInteractions();
        
        // إعداد مشاركة الصفحة
        this.setupSocialSharing();
        
        // تحسين التفاعل مع البطاقات
        this.setupCardInteractions();
        
        // إعداد التحديث التلقائي للمحتوى
        this.setupAutoRefresh();
    }

    /**
     * تطبيق الرسوم المتحركة عند التحميل
     */
    applyLoadAnimations() {
        // تحريك البطاقات بالتتابع
        this.contentCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
                card.classList.add('fade-in');
            }, index * 150);
        });

        // تحريك الشريط الجانبي
        this.sidebarCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateX(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateX(0)';
                card.classList.add('slide-in-right');
            }, 300 + (index * 100));
        });
    }

    /**
     * تحسين الصور
     */
    optimizeImages() {
        // إضافة loading="lazy" للصور
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
            
            // إضافة placeholder أثناء التحميل
            this.addImagePlaceholder(img);
        });
    }

    /**
     * إضافة placeholder للصور
     */
    addImagePlaceholder(img) {
        const placeholder = document.createElement('div');
        placeholder.className = 'loading-skeleton';
        placeholder.style.width = img.offsetWidth + 'px';
        placeholder.style.height = img.offsetHeight + 'px';
        placeholder.style.borderRadius = '8px';
        
        img.style.opacity = '0';
        img.parentNode.insertBefore(placeholder, img);
        
        img.addEventListener('load', () => {
            img.style.transition = 'opacity 0.3s ease';
            img.style.opacity = '1';
            placeholder.remove();
        });
        
        img.addEventListener('error', () => {
            placeholder.innerHTML = '<i class="fas fa-image text-muted"></i>';
            placeholder.style.display = 'flex';
            placeholder.style.alignItems = 'center';
            placeholder.style.justifyContent = 'center';
            placeholder.style.background = '#f8f9fa';
        });
    }

    /**
     * إعداد معرض الصور
     */
    setupImageGallery() {
        if (!this.imageGallery || !this.mainImage) return;

        // إعداد النقر على الصور المصغرة
        this.thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchMainImage(thumbnail.src, thumbnail.alt);
                this.setActiveThumbnail(thumbnail);
            });
        });

        // إعداد التنقل بلوحة المفاتيح
        this.setupImageKeyboardNavigation();
        
        // إعداد التكبير
        this.setupImageZoom();
    }

    /**
     * تبديل الصورة الرئيسية
     */
    switchMainImage(src, alt) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.mainImage.style.opacity = '0.5';
        
        const newImage = new Image();
        newImage.onload = () => {
            this.mainImage.src = src;
            this.mainImage.alt = alt;
            this.mainImage.style.opacity = '1';
            this.isLoading = false;
        };
        newImage.src = src;
    }

    /**
     * تعيين الصورة المصغرة النشطة
     */
    setActiveThumbnail(activeThumbnail) {
        this.thumbnails.forEach(thumb => thumb.classList.remove('active'));
        activeThumbnail.classList.add('active');
    }

    /**
     * إعداد التنقل بلوحة المفاتيح للصور
     */
    setupImageKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (e.target.closest('.ad-image-gallery')) {
                const currentIndex = Array.from(this.thumbnails).findIndex(thumb => 
                    thumb.classList.contains('active')
                );
                
                let newIndex = currentIndex;
                
                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    newIndex = currentIndex > 0 ? currentIndex - 1 : this.thumbnails.length - 1;
                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    newIndex = currentIndex < this.thumbnails.length - 1 ? currentIndex + 1 : 0;
                }
                
                if (newIndex !== currentIndex) {
                    e.preventDefault();
                    this.thumbnails[newIndex].click();
                    this.thumbnails[newIndex].focus();
                }
            }
        });
    }

    /**
     * إعداد تكبير الصور
     */
    setupImageZoom() {
        if (!this.mainImage) return;
        
        this.mainImage.addEventListener('click', () => {
            this.openImageModal(this.mainImage.src, this.mainImage.alt);
        });
    }

    /**
     * فتح نافذة منبثقة للصورة
     */
    openImageModal(src, alt) {
        const modal = document.createElement('div');
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="image-modal-backdrop" onclick="this.parentElement.remove()">
                <div class="image-modal-content" onclick="event.stopPropagation()">
                    <button class="image-modal-close" onclick="this.closest('.image-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                    <img src="${src}" alt="${alt}" class="image-modal-img">
                </div>
            </div>
        `;
        
        // إضافة أنماط CSS للنافذة المنبثقة
        if (!document.querySelector('#image-modal-styles')) {
            const styles = document.createElement('style');
            styles.id = 'image-modal-styles';
            styles.textContent = `
                .image-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    animation: fadeIn 0.3s ease;
                }
                .image-modal-backdrop {
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 2rem;
                }
                .image-modal-content {
                    position: relative;
                    max-width: 90vw;
                    max-height: 90vh;
                }
                .image-modal-img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    border-radius: 8px;
                }
                .image-modal-close {
                    position: absolute;
                    top: -40px;
                    right: 0;
                    background: rgba(255, 255, 255, 0.2);
                    border: none;
                    color: white;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: background 0.3s ease;
                }
                .image-modal-close:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
            `;
            document.head.appendChild(styles);
        }
        
        document.body.appendChild(modal);
        
        // إغلاق بمفتاح Escape
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    /**
     * تحسين الأزرار
     */
    enhanceButtons() {
        this.actionButtons.forEach(button => {
            // إضافة تأثير الموجة عند النقر
            button.addEventListener('click', (e) => {
                this.createRippleEffect(e, button);
            });
            
            // تحسين إمكانية الوصول
            if (!button.hasAttribute('aria-label') && button.textContent.trim()) {
                button.setAttribute('aria-label', button.textContent.trim());
            }
        });
    }

    /**
     * إنشاء تأثير الموجة عند النقر
     */
    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        // إضافة أنماط CSS للتأثير
        if (!document.querySelector('#ripple-styles')) {
            const styles = document.createElement('style');
            styles.id = 'ripple-styles';
            styles.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
                .action-button {
                    position: relative;
                    overflow: hidden;
                }
            `;
            document.head.appendChild(styles);
        }
        
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * إعداد التمرير السلس
     */
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * إعداد اختصارات لوحة المفاتيح
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S للمشاركة
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.shareAd();
            }
            
            // Ctrl/Cmd + P للطباعة
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                this.printAd();
            }
            
            // F للإضافة إلى المفضلة
            if (e.key === 'f' && !e.ctrlKey && !e.metaKey && !e.altKey) {
                const favoriteButton = document.querySelector('[data-action="favorite"]');
                if (favoriteButton && !e.target.matches('input, textarea')) {
                    e.preventDefault();
                    favoriteButton.click();
                }
            }
        });
    }

    /**
     * إعداد التفاعل مع الأزرار
     */
    setupButtonInteractions() {
        // تحسين أزرار المشاركة
        document.querySelectorAll('[data-share]').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const platform = button.dataset.share;
                this.shareToSocial(platform);
            });
        });
        
        // تحسين أزرار الإجراءات
        document.querySelectorAll('[data-action]').forEach(button => {
            button.addEventListener('click', () => {
                const action = button.dataset.action;
                this.handleAction(action, button);
            });
        });
    }

    /**
     * معالجة الإجراءات
     */
    handleAction(action, button) {
        switch (action) {
            case 'favorite':
                this.toggleFavorite(button);
                break;
            case 'share':
                this.shareAd();
                break;
            case 'print':
                this.printAd();
                break;
            case 'contact':
                this.showContactInfo(button);
                break;
            default:
                console.log('Unknown action:', action);
        }
    }

    /**
     * تبديل المفضلة
     */
    async toggleFavorite(button) {
        if (button.disabled) return;
        
        button.disabled = true;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
        
        try {
            const adId = button.dataset.adId || this.getAdId();
            const response = await fetch(`/favorites/toggle/${adId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.updateFavoriteButton(button, data.is_favorited);
                this.showNotification(data.message, 'success');
                
                // إرسال حدث مخصص
                document.dispatchEvent(new CustomEvent('favoriteToggled', {
                    detail: { adId, action: data.action, is_favorited: data.is_favorited }
                }));
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
            this.showNotification('حدث خطأ في إضافة/إزالة المفضلة', 'error');
            button.innerHTML = originalText;
        } finally {
            button.disabled = false;
        }
    }

    /**
     * تحديث زر المفضلة
     */
    updateFavoriteButton(button, isFavorited) {
        if (isFavorited) {
            button.innerHTML = '<i class="fas fa-heart text-danger"></i> إزالة من المفضلة';
            button.classList.add('favorited');
        } else {
            button.innerHTML = '<i class="far fa-heart"></i> إضافة للمفضلة';
            button.classList.remove('favorited');
        }
    }

    /**
     * مشاركة الإعلان
     */
    shareAd() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                text: document.querySelector('meta[name="description"]')?.content || '',
                url: window.location.href
            }).catch(console.error);
        } else {
            this.copyToClipboard(window.location.href);
            this.showNotification('تم نسخ رابط الإعلان', 'success');
        }
    }

    /**
     * مشاركة على منصات التواصل
     */
    shareToSocial(platform) {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        
        const urls = {
            facebook: `https://www.facebook.com/sharer/sharer.php?u=${url}`,
            twitter: `https://twitter.com/intent/tweet?url=${url}&text=${title}`,
            whatsapp: `https://wa.me/?text=${title} ${url}`,
            telegram: `https://t.me/share/url?url=${url}&text=${title}`
        };
        
        if (urls[platform]) {
            window.open(urls[platform], '_blank', 'width=600,height=400');
        }
    }

    /**
     * طباعة الإعلان
     */
    printAd() {
        window.print();
    }

    /**
     * نسخ النص للحافظة
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
        } catch (err) {
            // Fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }

    /**
     * إعداد Intersection Observer للرسوم المتحركة
     */
    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.content-card, .sidebar-card').forEach(el => {
            observer.observe(el);
        });
    }

    /**
     * إعداد التحميل البطيء للصور
     */
    setupImageLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * تحسينات الأداء
     */
    setupPerformanceOptimizations() {
        // تأجيل تحميل المحتوى غير الضروري
        this.deferNonCriticalContent();

        // تحسين الذاكرة
        this.setupMemoryOptimizations();

        // تحسين الشبكة (يمكن إضافتها لاحقاً)
        // this.setupNetworkOptimizations();

        // إضافة زر العودة إلى الأعلى
        this.setupScrollToTop();
    }

    /**
     * إعداد زر العودة إلى الأعلى
     */
    setupScrollToTop() {
        // إنشاء الزر إذا لم يكن موجوداً
        let scrollButton = document.querySelector('.scroll-to-top');
        if (!scrollButton) {
            scrollButton = document.createElement('button');
            scrollButton.className = 'scroll-to-top';
            scrollButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
            scrollButton.setAttribute('aria-label', 'العودة إلى الأعلى');
            document.body.appendChild(scrollButton);
        }

        // إظهار/إخفاء الزر حسب موقع التمرير
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                scrollButton.classList.add('visible');
            } else {
                scrollButton.classList.remove('visible');
            }
        });

        // النقر على الزر
        scrollButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    /**
     * تأجيل المحتوى غير الضروري
     */
    deferNonCriticalContent() {
        // تأجيل تحميل المكونات الثقيلة
        setTimeout(() => {
            this.loadDeferredComponents();
        }, 1000);
    }

    /**
     * تحميل المكونات المؤجلة
     */
    loadDeferredComponents() {
        // تحميل الخرائط إذا كانت موجودة
        const mapContainer = document.querySelector('[data-map]');
        if (mapContainer && !mapContainer.hasAttribute('data-loaded')) {
            this.loadMap(mapContainer);
        }
        
        // تحميل المحتوى التفاعلي الإضافي
        this.loadInteractiveContent();
    }

    /**
     * إعداد تحسينات الذاكرة
     */
    setupMemoryOptimizations() {
        // تنظيف مستمعي الأحداث عند إلغاء تحميل الصفحة
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        // إزالة مستمعي الأحداث
        // تنظيف المؤقتات
        // إلغاء الطلبات المعلقة
    }

    /**
     * دوال مساعدة
     */
    getAdId() {
        return document.querySelector('[data-ad-id]')?.dataset.adId || 
               window.location.pathname.match(/\/ads\/(\d+)/)?.[1];
    }

    showNotification(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// تهيئة المحسن عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.ad-details-page') || document.body.classList.contains('ad-show')) {
        window.AdDetailsEnhancer = new AdDetailsEnhancer();
    }
});

// تصدير الكلاس للاستخدام العام
window.AdDetailsEnhancer = AdDetailsEnhancer;
