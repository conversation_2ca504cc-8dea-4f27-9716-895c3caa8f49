/*
 * ملف CSS متقدم للتقييمات والتعليقات
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على أنماط متقدمة لعرض التقييمات والتعليقات والتفاعل معها
 */

/* ===== متغيرات الألوان للتقييمات والتعليقات ===== */
:root {
    --reviews-primary: #fbbf24;
    --reviews-secondary: #f59e0b;
    --comments-primary: #3b82f6;
    --comments-secondary: #1d4ed8;
    --rating-star: #fbbf24;
    --rating-empty: #e5e7eb;
    --review-bg: #fefefe;
    --comment-bg: #f8fafc;
    --reply-bg: #f1f5f9;
    --border-light: #e5e7eb;
    --text-muted: #6b7280;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
}

/* ===== قسم التقييمات الرئيسي ===== */
.reviews-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.reviews-detailed {
    padding: 2.5rem;
}

.reviews-compact {
    padding: 1.5rem;
}

.reviews-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.reviews-count {
    color: var(--text-muted);
    font-weight: 500;
}

/* ===== ملخص التقييمات ===== */
.reviews-summary {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.average-rating-display {
    text-align: center;
}

.rating-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--reviews-secondary);
    line-height: 1;
}

.rating-stars {
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

.rating-text {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* ===== توزيع التقييمات ===== */
.rating-distribution {
    padding-left: 1rem;
}

.rating-bar-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    gap: 1rem;
}

.rating-label {
    min-width: 60px;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.rating-bar {
    flex: 1;
    height: 8px;
    background: var(--rating-empty);
    border-radius: 4px;
    overflow: hidden;
}

.rating-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--rating-star) 0%, var(--reviews-secondary) 100%);
    transition: width 0.3s ease;
}

.rating-count {
    min-width: 30px;
    text-align: right;
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* ===== نموذج إضافة تقييم ===== */
.add-review-form,
.add-comment-form {
    margin-bottom: 2rem;
}

.review-form-card,
.comment-form-card {
    background: var(--review-bg);
    border: 2px solid var(--border-light);
    border-radius: 12px;
    padding: 1.5rem;
}

.star-rating-input {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.star-input {
    font-size: 1.5rem;
    color: var(--rating-empty);
    cursor: pointer;
    transition: all 0.2s ease;
}

.star-input:hover,
.star-input.active {
    color: var(--rating-star);
    transform: scale(1.1);
}

.star-input.active {
    text-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
}

/* ===== قائمة التقييمات ===== */
.reviews-list,
.comments-list {
    margin-top: 2rem;
}

.review-item,
.comment-item {
    background: var(--review-bg);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.review-item:hover,
.comment-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.review-header,
.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.reviewer-info,
.commenter-info {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.reviewer-avatar,
.commenter-avatar {
    flex-shrink: 0;
}

.avatar-sm {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-light);
}

.avatar-placeholder-sm {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--border-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 1.2rem;
}

.avatar-xs {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid var(--border-light);
}

.avatar-placeholder-xs {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--border-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 0.8rem;
}

.reviewer-name,
.commenter-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.review-meta,
.comment-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.review-rating {
    display: flex;
    gap: 0.1rem;
}

.review-actions,
.comment-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* ===== محتوى التقييمات والتعليقات ===== */
.review-content,
.comment-content {
    margin-top: 1rem;
}

.review-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.review-text,
.comment-text {
    color: #374151;
    line-height: 1.6;
    margin: 0;
}

/* ===== الشارات ===== */
.badge-verified {
    background: var(--success-color);
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
}

.badge-owner {
    background: var(--comments-primary);
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
}

/* ===== قسم التعليقات ===== */
.comments-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.comments-detailed {
    padding: 2.5rem;
}

.comments-compact {
    padding: 1.5rem;
}

.comments-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.comments-count {
    color: var(--text-muted);
    font-weight: 500;
}

/* ===== الردود ===== */
.replies-section {
    margin-top: 1rem;
    margin-left: 2rem;
    padding-left: 1rem;
    border-left: 3px solid var(--border-light);
}

.reply-item {
    background: var(--reply-bg);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.replier-info {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.replier-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.reply-meta {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.reply-content {
    margin-top: 0.75rem;
}

.reply-text {
    color: #374151;
    line-height: 1.5;
    margin: 0;
    font-size: 0.9rem;
}

/* ===== نماذج الرد ===== */
.reply-form {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--comment-bg);
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

.reply-form-actions,
.comment-form-actions,
.review-form-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* ===== حالات فارغة ===== */
.no-reviews,
.no-comments {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.no-reviews i,
.no-comments i {
    opacity: 0.5;
}

/* ===== الاستجابة للأجهزة المحمولة ===== */
@media (max-width: 768px) {
    .reviews-section,
    .comments-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .reviews-detailed,
    .comments-detailed {
        padding: 1.5rem;
    }
    
    .reviews-summary {
        padding: 1.5rem;
    }
    
    .rating-number {
        font-size: 2.5rem;
    }
    
    .review-header,
    .comment-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .review-actions,
    .comment-actions {
        align-self: stretch;
        justify-content: flex-end;
    }
    
    .replies-section {
        margin-left: 1rem;
    }
    
    .rating-bar-row {
        gap: 0.5rem;
    }
    
    .rating-label {
        min-width: 50px;
    }
}

@media (max-width: 576px) {
    .reviews-section,
    .comments-section {
        padding: 0.75rem;
        border-radius: 10px;
    }
    
    .review-item,
    .comment-item {
        padding: 1rem;
    }
    
    .reply-item {
        padding: 0.75rem;
    }
    
    .reviewer-info,
    .commenter-info {
        gap: 0.75rem;
    }
    
    .avatar-sm {
        width: 40px;
        height: 40px;
    }
    
    .avatar-placeholder-sm {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .star-rating-input {
        justify-content: center;
    }
    
    .star-input {
        font-size: 1.25rem;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .reviews-section,
    .comments-section {
        box-shadow: none;
        border: 1px solid var(--border-light);
    }
    
    .review-actions,
    .comment-actions,
    .reply-actions,
    .add-review-form,
    .add-comment-form,
    .reply-form {
        display: none !important;
    }
    
    .review-item,
    .comment-item {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--border-light);
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.star-input:focus {
    outline: 2px solid var(--comments-primary);
    outline-offset: 2px;
}

.btn:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

/* ===== تأثيرات التفاعل ===== */
.review-item,
.comment-item {
    will-change: transform, box-shadow;
}

.star-input {
    will-change: transform, color;
}

/* ===== تحسينات الوضع المظلم ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --review-bg: #1f2937;
        --comment-bg: #374151;
        --reply-bg: #4b5563;
        --border-light: #4b5563;
        --text-muted: #9ca3af;
    }
    
    .reviews-section,
    .comments-section {
        background: #111827;
        color: #f9fafb;
    }
    
    .reviews-title,
    .comments-title {
        color: #f9fafb;
    }
    
    .reviewer-name,
    .commenter-name {
        color: #f9fafb;
    }
    
    .review-text,
    .comment-text,
    .reply-text {
        color: #d1d5db;
    }
}

/* ===== تأثيرات حركية إضافية ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.review-item,
.comment-item {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes starGlow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(251, 191, 36, 0.5);
    }
    50% {
        text-shadow: 0 0 15px rgba(251, 191, 36, 0.8);
    }
}

.star-input.active {
    animation: starGlow 1s ease-in-out;
}
