

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'ad',                           // الإعلان المطلوب عرضه (مطلوب)
    'variant' => 'default',        // نوع البطاقة: default, compact, detailed, list
    'showFavorites' => true,       // إظهار أيقونة المفضلة
    'descriptionLength' => 100,    // طول الوصف المعروض
    'showPricing' => true,         // إظهار قسم الأسعار
    'showMeta' => true,            // إظهار المعلومات الإضافية
    'showActions' => true,         // إظهار أزرار الإجراءات
    'showExpiry' => true,          // إظهار معلومات انتهاء الصلاحية
    'noWrapper' => false,          // عدم إضافة wrapper div
    'customClass' => '',           // فئات CSS إضافية
    'imageHeight' => null,         // ارتفاع الصورة المخصص
    'cardHeight' => null,          // ارتفاع البطاقة المخصص
    'columns' => null,             // تخصيص عدد الأعمدة: 2, 3, 4, 5, 6
    'gridClass' => '',             // فئات Bootstrap Grid مخصصة
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'ad',                           // الإعلان المطلوب عرضه (مطلوب)
    'variant' => 'default',        // نوع البطاقة: default, compact, detailed, list
    'showFavorites' => true,       // إظهار أيقونة المفضلة
    'descriptionLength' => 100,    // طول الوصف المعروض
    'showPricing' => true,         // إظهار قسم الأسعار
    'showMeta' => true,            // إظهار المعلومات الإضافية
    'showActions' => true,         // إظهار أزرار الإجراءات
    'showExpiry' => true,          // إظهار معلومات انتهاء الصلاحية
    'noWrapper' => false,          // عدم إضافة wrapper div
    'customClass' => '',           // فئات CSS إضافية
    'imageHeight' => null,         // ارتفاع الصورة المخصص
    'cardHeight' => null,          // ارتفاع البطاقة المخصص
    'columns' => null,             // تخصيص عدد الأعمدة: 2, 3, 4, 5, 6
    'gridClass' => '',             // فئات Bootstrap Grid مخصصة
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>


<?php
    $cardConfig = match($variant) {
        'compact' => [
            'descriptionLength' => $descriptionLength ?: 80,
            'imageHeight' => $imageHeight ?: '220px',
            'cardHeight' => $cardHeight ?: '480px',
            'padding' => 'p-3',
            'marginBottom' => 'mb-2',
            'titleSize' => 'h6',
            'showFullMeta' => false,
            'showRating' => true,
        ],
        'detailed' => [
            'descriptionLength' => $descriptionLength ?: 150,
            'imageHeight' => $imageHeight ?: '320px',
            'cardHeight' => $cardHeight ?: '600px',
            'padding' => 'p-4',
            'marginBottom' => 'mb-3',
            'titleSize' => 'h4',
            'showFullMeta' => true,
            'showRating' => true,
            'showDetailedRating' => true,
            'showComments' => true,
        ],
        'list' => [
            'descriptionLength' => $descriptionLength ?: 200,
            'imageHeight' => $imageHeight ?: '200px',
            'cardHeight' => $cardHeight ?: 'auto',
            'padding' => 'p-3',
            'marginBottom' => 'mb-3',
            'titleSize' => 'h5',
            'showFullMeta' => true,
            'showRating' => true,
        ],
        'favorites' => [
            'descriptionLength' => $descriptionLength ?: 80,
            'imageHeight' => $imageHeight ?: '200px',
            'cardHeight' => $cardHeight ?: '480px',
            'padding' => 'p-3',
            'marginBottom' => 'mb-3',
            'titleSize' => 'h6',
            'showFullMeta' => true,
            'showFavoriteDate' => true,
            'showRating' => true,
        ],
        default => [
            'descriptionLength' => $descriptionLength ?: 100,
            'imageHeight' => $imageHeight ?: '280px',
            'cardHeight' => $cardHeight ?: '550px',
            'padding' => 'p-4',
            'marginBottom' => 'mb-3',
            'titleSize' => 'h5',
            'showFullMeta' => true,
            'showRating' => true,
        ],
    };

    // تحديد فئات Bootstrap Grid حسب عدد الأعمدة
    $gridClasses = '';
    if ($gridClass) {
        $gridClasses = $gridClass;
    } elseif ($columns) {
        $gridClasses = match($columns) {
            2 => 'col-xl-6 col-lg-6 col-md-6 col-sm-12',
            3 => 'col-xl-4 col-lg-6 col-md-6 col-sm-12',
            4 => 'col-xl-3 col-lg-4 col-md-6 col-sm-12',
            5 => 'col-xl-2 col-lg-3 col-md-4 col-sm-6',
            6 => 'col-xl-2 col-lg-2 col-md-3 col-sm-6',
            default => 'col-xl-4 col-lg-6 col-md-6 col-sm-12', // 3 أعمدة افتراضي
        };
    } else {
        // الأعمدة الافتراضية حسب variant
        $gridClasses = match($variant) {
            'compact' => 'col-xl-2 col-lg-3 col-md-4 col-sm-6',     // 6 أعمدة
            'detailed' => 'col-xl-6 col-lg-6 col-md-12 col-sm-12',  // 2 أعمدة
            'list' => 'col-12',                                      // عمود واحد
            'favorites' => 'col-xl-3 col-lg-4 col-md-6 col-sm-12',  // 4 أعمدة
            default => 'col-xl-4 col-lg-6 col-md-6 col-sm-12',      // 3 أعمدة
        };
    }

    // دمج الإعدادات المخصصة
    $config = array_merge($cardConfig, [
        'variant' => $variant,
        'showFavorites' => $showFavorites,
        'showPricing' => $showPricing,
        'showMeta' => $showMeta,
        'showActions' => $showActions,
        'showExpiry' => $showExpiry,
        'gridClasses' => $gridClasses,
        'customClass' => $customClass,
        'customAttributes' => $customAttributes ?? [],
    ]);
?>


<?php if(!$noWrapper): ?>
    <div class="<?php echo e($config['gridClasses']); ?> mb-5 <?php echo e($config['customAttributes']['class'] ?? ''); ?>"
         <?php $__currentLoopData = $config['customAttributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attr => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
             <?php if($attr !== 'class'): ?>
                 <?php echo e($attr); ?>="<?php echo e($value); ?>"
             <?php endif; ?>
         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>>
<?php endif; ?>


<?php if($variant === 'favorites'): ?>
    <?php echo $__env->make('components.ad-card.variants.favorites', [
        'ad' => $ad,
        'favorite' => $favorite ?? null,
        'config' => $config
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php else: ?>
    
    <div class="card ad-card ad-card-<?php echo e($variant); ?> h-100 shadow-sm border-0 <?php echo e($config['customClass']); ?>"
         data-ad-id="<?php echo e($ad->id); ?>"
         style="min-height: <?php echo e($config['cardHeight']); ?>;">

        
        <?php echo $__env->make('components.ad-card.partials.image', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        
        <div class="card-body <?php echo e($config['padding']); ?>" style="overflow: visible; position: relative;">
            
            <?php echo $__env->make('components.ad-card.partials.category-badge', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            
            <?php echo $__env->make('components.ad-card.partials.title', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            
            <?php echo $__env->make('components.ad-card.partials.description', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            
            <?php if($config['showRating'] ?? true): ?>
                <?php echo $__env->make('components.ad-card.partials.rating', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>

            
            <?php if($config['showMeta']): ?>
                <?php echo $__env->make('components.ad-card.partials.meta', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>

            
            <?php echo $__env->make('components.ad-card.partials.contact', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            
            <?php if($config['showPricing']): ?>
                <?php echo $__env->make('components.ad-card.partials.pricing', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>

            
            <?php if($config['showComments'] ?? false): ?>
                <?php echo $__env->make('components.ad-card.partials.comments', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
        </div>

        
        <?php if($config['showActions'] || $config['showExpiry']): ?>
            <?php echo $__env->make('components.ad-card.partials.footer', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>

        
        <div class="card-overlay"></div>
    </div>
<?php endif; ?>


<?php if(!$noWrapper): ?>
    </div>
<?php endif; ?>


<?php if (! $__env->hasRenderedOnce('a5ebb49d-cd23-4552-9774-a0151b974b20')): $__env->markAsRenderedOnce('a5ebb49d-cd23-4552-9774-a0151b974b20'); ?>
    <?php $__env->startPush('styles'); ?>
        <link rel="stylesheet" href="<?php echo e(asset('css/ad-cards.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('css/badge-fix.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('css/components/contact-responsive.css')); ?>">
    <?php $__env->stopPush(); ?>
<?php endif; ?>


<?php if (! $__env->hasRenderedOnce('7b3fa703-dd37-40ed-afff-62e7dc864cf5')): $__env->markAsRenderedOnce('7b3fa703-dd37-40ed-afff-62e7dc864cf5'); ?>
    <?php $__env->startPush('scripts'); ?>
        <script src="<?php echo e(asset('js/favorites.js')); ?>"></script>
        <script>
            // دالة مشاركة الإعلان
            function shareAd(url, title) {
                if (navigator.share) {
                    navigator.share({
                        title: title,
                        url: url
                    }).catch(console.error);
                } else {
                    // نسخ الرابط للحافظة
                    navigator.clipboard.writeText(url).then(function() {
                        showToast('<?php echo e(__("Link copied to clipboard")); ?>', 'success');
                    }).catch(function() {
                        // فتح نافذة مشاركة تقليدية
                        window.open('https://wa.me/?text=' + encodeURIComponent(title + ' - ' + url), '_blank');
                    });
                }
            }
        </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?><?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/index.blade.php ENDPATH**/ ?>