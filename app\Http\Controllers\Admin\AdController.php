<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Ad;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/**
 * كونترولر إدارة الإعلانات للمدير
 * يدير مراجعة وموافقة الإعلانات المعلقة
 */
class AdController extends Controller
{
    /**
     * التأكد من أن المستخدم مدير
     */
    public function __construct()
    {
        // التحقق من تسجيل الدخول أولاً
        $this->middleware('auth');

        // استخدام middleware محسن للتحقق من صلاحيات المدير
        // سيتم التحقق من الصلاحيات عبر route middleware بدلاً من controller middleware
    }

    /**
     * عرض لوحة تحكم الإعلانات
     */
    public function index(Request $request)
    {
        \Log::info('Admin ads index accessed', [
            'user_id' => auth()->id(),
            'status_filter' => $request->get('status', 'pending')
        ]);
        $status = $request->get('status', 'pending');

        // بناء الاستعلام
        $query = Ad::with(['category', 'user']);

        // تطبيق فلتر الحالة
        switch ($status) {
            case 'pending':
                $query->pending();
                break;
            case 'active':
                $query->active();
                break;
            case 'rejected':
                $query->where('status', 'rejected');
                break;
            case 'all':
                // عرض جميع الإعلانات
                break;
            default:
                $query->pending();
        }

        // ترتيب حسب التاريخ
        $ads = $query->orderBy('created_at', 'desc')->paginate(15);

        // إحصائيات سريعة
        $stats = [
            'pending' => Ad::pending()->count(),
            'active' => Ad::active()->count(),
            'rejected' => Ad::where('status', 'rejected')->count(),
            'total' => Ad::count(),
        ];

        return view('admin.ads.index', compact('ads', 'stats', 'status'));
    }

    /**
     * عرض تفاصيل إعلان للمراجعة
     */
    public function show(Ad $ad)
    {
        $ad->load(['category', 'user']);
        return view('admin.ads.show', compact('ad'));
    }

    /**
     * الموافقة على إعلان
     */
    public function approve(Ad $ad)
    {
        // إضافة logging للتشخيص
        \Log::info('Admin approve ad attempt', [
            'ad_id' => $ad->id,
            'ad_title' => $ad->title_ar,
            'current_status' => $ad->status,
            'user_id' => auth()->id(),
            'user_email' => auth()->user()->email
        ]);

        $ad->update(['status' => 'active']);

        \Log::info('Admin approve ad success', [
            'ad_id' => $ad->id,
            'new_status' => $ad->fresh()->status
        ]);

        return redirect()->back()
            ->with('success', __('تم الموافقة على الإعلان بنجاح'));
    }

    /**
     * رفض إعلان
     */
    public function reject(Request $request, Ad $ad)
    {
        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        $ad->update([
            'status' => 'rejected',
            'rejection_reason' => $request->reason
        ]);

        return redirect()->back()
            ->with('success', __('تم رفض الإعلان'));
    }

    /**
     * تعديل حالة إعلان
     */
    public function updateStatus(Request $request, Ad $ad)
    {
        $request->validate([
            'status' => 'required|in:pending,active,rejected,inactive'
        ]);

        $ad->update(['status' => $request->status]);

        $statusText = [
            'pending' => 'في انتظار المراجعة',
            'active' => 'نشط',
            'rejected' => 'مرفوض',
            'inactive' => 'غير نشط'
        ];

        return redirect()->back()
            ->with('success', __('تم تحديث حالة الإعلان إلى: ') . $statusText[$request->status]);
    }

    /**
     * تبديل حالة الإعلان المميز
     */
    public function toggleFeatured(Ad $ad)
    {
        $ad->update(['is_featured' => !$ad->is_featured]);

        $message = $ad->is_featured
            ? __('تم إضافة الإعلان للإعلانات المميزة')
            : __('تم إزالة الإعلان من الإعلانات المميزة');

        return redirect()->back()->with('success', $message);
    }

    /**
     * حذف إعلان نهائياً
     */
    public function destroy(Ad $ad)
    {
        // حذف الصورة إذا وجدت
        if ($ad->image) {
            \Storage::disk('public')->delete($ad->image);
        }

        $ad->delete();

        return redirect()->route('admin.ads.index')
            ->with('success', __('تم حذف الإعلان نهائياً'));
    }

    /**
     * الموافقة على عدة إعلانات
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'ads' => 'required|array',
            'ads.*' => 'exists:ads,id'
        ]);

        Ad::whereIn('id', $request->ads)->update(['status' => 'active']);

        return redirect()->back()
            ->with('success', __('تم الموافقة على الإعلانات المحددة'));
    }

    /**
     * رفض عدة إعلانات
     */
    public function bulkReject(Request $request)
    {
        $request->validate([
            'ads' => 'required|array',
            'ads.*' => 'exists:ads,id'
        ]);

        Ad::whereIn('id', $request->ads)->update(['status' => 'rejected']);

        return redirect()->back()
            ->with('success', __('تم رفض الإعلانات المحددة'));
    }

    /**
     * إحصائيات مفصلة
     */
    public function stats()
    {
        $stats = [
            'total_ads' => Ad::count(),
            'pending_ads' => Ad::pending()->count(),
            'active_ads' => Ad::active()->count(),
            'rejected_ads' => Ad::where('status', 'rejected')->count(),
            'featured_ads' => Ad::featured()->count(),
            'ads_today' => Ad::whereDate('created_at', today())->count(),
            'ads_this_week' => Ad::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'ads_this_month' => Ad::whereMonth('created_at', now()->month)->count(),
        ];

        // إحصائيات حسب التصنيف
        $categoryStats = Category::withCount([
            'ads',
            'ads as active_ads_count' => function ($query) {
                $query->active()->notExpired();
            }
        ])->get();

        // أحدث الإعلانات
        $recentAds = Ad::with(['category', 'user'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.ads.stats', compact('stats', 'categoryStats', 'recentAds'));
    }
}
