@extends('layouts.app')

@section('title', __('Testimonials') . ' - ' . __('site_name'))
@section('description', __('Read what our customers say about our platform and services'))

@section('content')
<!-- القسم الرئيسي -->
<section class="testimonials-hero py-5" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); min-height: 40vh;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center text-white">
                <div class="testimonials-header">
                    <div class="testimonials-icon mb-4">
                        <i class="fas fa-star fa-5x"></i>
                    </div>
                    <h1 class="display-4 fw-bold arabic-text mb-3">
                        {{ __('Customer Testimonials') }}
                    </h1>
                    <p class="lead arabic-text mb-4">
                        {{ __('Read what our customers say about our platform and services') }}
                    </p>
                    <div class="testimonials-stats">
                        <div class="row text-center">
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold">{{ $feedbacks->total() }}+</h3>
                                    <p class="arabic-text">{{ __('Total Reviews') }}</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold">{{ number_format($feedbacks->avg('rating'), 1) }}</h3>
                                    <p class="arabic-text">{{ __('Average Rating') }}</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold">{{ $feedbacks->where('rating', 5)->count() }}+</h3>
                                    <p class="arabic-text">{{ __('5-Star Reviews') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="testimonials-content py-5">
    <div class="container">
        @if($feedbacks->count() > 0)
            <!-- التقييمات -->
            <div class="row">
                @foreach($feedbacks as $feedback)
                    <div class="col-lg-6 mb-4">
                        <div class="testimonial-card h-100">
                            <div class="testimonial-header">
                                <div class="customer-info">
                                    <div class="customer-avatar">
                                        <i class="fas fa-user-circle fa-3x text-primary"></i>
                                    </div>
                                    <div class="customer-details">
                                        <h5 class="arabic-text fw-bold mb-1">{{ $feedback->name }}</h5>
                                        @if($feedback->company)
                                            <p class="text-muted small mb-1 arabic-text">{{ $feedback->company }}</p>
                                        @endif
                                        <div class="rating mb-2">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $feedback->rating)
                                                    <i class="fas fa-star text-warning"></i>
                                                @else
                                                    <i class="far fa-star text-muted"></i>
                                                @endif
                                            @endfor
                                            <span class="rating-text ms-2 text-muted small">
                                                ({{ $feedback->rating }}/5)
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="testimonial-date">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $feedback->created_at->format('Y/m/d') }}
                                    </small>
                                </div>
                            </div>
                            <div class="testimonial-body">
                                <div class="quote-icon mb-2">
                                    <i class="fas fa-quote-left fa-2x text-primary opacity-25"></i>
                                </div>
                                <p class="testimonial-text arabic-text">
                                    {{ $feedback->message }}
                                </p>
                                @if($feedback->phone)
                                    <div class="contact-info mt-3">
                                        <small class="text-muted">
                                            <i class="fas fa-phone me-1"></i>
                                            {{ $feedback->phone }}
                                        </small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- التصفح -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="pagination-wrapper d-flex justify-content-center">
                        {{ $feedbacks->links() }}
                    </div>
                </div>
            </div>
        @else
            <!-- حالة عدم وجود تقييمات -->
            <div class="row">
                <div class="col-12">
                    <div class="empty-testimonials text-center py-5">
                        <div class="empty-icon mb-4">
                            <i class="fas fa-star fa-5x text-muted opacity-50"></i>
                        </div>
                        <h3 class="arabic-text fw-bold text-muted mb-3">
                            {{ __('No testimonials yet') }}
                        </h3>
                        <p class="text-muted arabic-text mb-4">
                            {{ __('Be the first to share your experience with us') }}
                        </p>
                        <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-comment me-2"></i>
                            {{ __('Share Your Experience') }}
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- إحصائيات التقييمات -->
@if($feedbacks->count() > 0)
<section class="rating-stats py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="arabic-text fw-bold">{{ __('Rating Breakdown') }}</h2>
                <p class="text-muted arabic-text">{{ __('Detailed analysis of customer ratings') }}</p>
            </div>
            <div class="col-lg-8 mx-auto">
                <div class="rating-breakdown">
                    @for($star = 5; $star >= 1; $star--)
                        @php
                            $count = $feedbacks->where('rating', $star)->count();
                            $percentage = $feedbacks->count() > 0 ? ($count / $feedbacks->count()) * 100 : 0;
                        @endphp
                        <div class="rating-row mb-3">
                            <div class="rating-label">
                                <span class="arabic-text fw-bold">{{ $star }} {{ __('stars') }}</span>
                            </div>
                            <div class="rating-bar">
                                <div class="progress">
                                    <div class="progress-bar bg-warning" 
                                         style="width: {{ $percentage }}%"
                                         role="progressbar">
                                    </div>
                                </div>
                            </div>
                            <div class="rating-count">
                                <span class="text-muted">{{ $count }}</span>
                            </div>
                        </div>
                    @endfor
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- دعوة للعمل -->
<section class="testimonials-cta py-5 bg-primary text-white">
    <div class="container text-center">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h2 class="arabic-text fw-bold mb-3">{{ __('Share Your Experience') }}</h2>
                <p class="lead arabic-text mb-4">
                    {{ __('Help others by sharing your experience with our platform') }}
                </p>
                <div class="cta-buttons">
                    <a href="{{ route('contact') }}" class="btn btn-warning btn-lg me-3 mb-2">
                        <i class="fas fa-star me-2"></i>
                        {{ __('Write a Review') }}
                    </a>
                    <a href="{{ route('auth.register') }}" class="btn btn-outline-light btn-lg mb-2">
                        <i class="fas fa-user-plus me-2"></i>
                        {{ __('Join Our Community') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق القسم الرئيسي */
.testimonials-hero {
    position: relative;
    overflow: hidden;
}

.testimonials-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.testimonials-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.testimonials-stats .stat-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

/* بطاقات التقييمات */
.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-left-color: #6f42c1;
}

.testimonial-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.customer-info {
    display: flex;
    align-items: center;
}

.customer-avatar {
    margin-right: 1rem;
}

.customer-details h5 {
    color: #495057;
}

.rating .fas.fa-star,
.rating .far.fa-star {
    font-size: 0.9rem;
}

.testimonial-body {
    position: relative;
}

.quote-icon {
    position: absolute;
    top: -10px;
    right: 0;
}

.testimonial-text {
    font-style: italic;
    line-height: 1.6;
    color: #6c757d;
    margin-top: 1rem;
}

/* حالة عدم وجود تقييمات */
.empty-testimonials {
    background: white;
    border-radius: 15px;
    padding: 3rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

/* إحصائيات التقييمات */
.rating-breakdown {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.rating-row {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.rating-label {
    min-width: 80px;
    text-align: right;
}

.rating-bar {
    flex: 1;
}

.rating-bar .progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.rating-count {
    min-width: 40px;
    text-align: center;
}

/* دعوة للعمل */
.testimonials-cta .btn {
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.testimonials-cta .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .testimonials-hero {
        min-height: 30vh;
        padding: 3rem 0;
    }
    
    .testimonial-card {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .testimonial-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .customer-info {
        flex-direction: column;
        text-align: center;
        width: 100%;
    }
    
    .customer-avatar {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .testimonials-stats .stat-item {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .rating-breakdown {
        padding: 1.5rem;
    }
    
    .rating-row {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .rating-label,
    .rating-count {
        min-width: auto;
    }
    
    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .testimonials-hero h1 {
        font-size: 2rem;
    }
    
    .testimonial-card {
        padding: 0.75rem;
    }
    
    .empty-testimonials {
        padding: 2rem 1rem;
    }
    
    .rating-breakdown {
        padding: 1rem;
    }
}
</style>
@endpush
