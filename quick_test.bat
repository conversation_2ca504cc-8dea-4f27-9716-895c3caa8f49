@echo off
echo ========================================
echo    اختبار سريع للنظام المحسن
echo ========================================
echo.

echo 🔍 فحص ملفات النظام...
if not exist "database\migrations\2025_07_25_100000_create_optimized_database_structure.php" (
    echo ❌ ملف الهجرة غير موجود!
    pause
    exit /b 1
)

if not exist "database\seeders\DatabaseSeeder.php" (
    echo ❌ ملف البذر غير موجود!
    pause
    exit /b 1
)

if not exist "app\Services\EncryptionService.php" (
    echo ❌ خدمة التشفير غير موجودة!
    pause
    exit /b 1
)

if not exist "app\Services\InputSanitizationService.php" (
    echo ❌ خدمة تنظيف البيانات غير موجودة!
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة!
echo.

echo 🧪 اختبار الاتصال بقاعدة البيانات...
php artisan tinker --execute="echo 'Database connection: ' . (DB::connection()->getPdo() ? 'OK' : 'Failed');"

echo.
echo 📊 عرض إحصائيات قاعدة البيانات الحالية...
php artisan tinker --execute="
echo 'Users: ' . App\Models\User::count();
echo 'Categories: ' . App\Models\Category::count();
echo 'Ads: ' . App\Models\Ad::count();
echo 'Interactions: ' . App\Models\Interaction::count();
"

echo.
echo 🔧 فحص خدمات التشفير...
php artisan tinker --execute="
use App\Services\EncryptionService;
\$test = EncryptionService::encryptText('test');
echo 'Encryption test: ' . (\$test ? 'OK' : 'Failed');
"

echo.
echo 🧹 فحص خدمات تنظيف البيانات...
php artisan tinker --execute="
use App\Services\InputSanitizationService;
\$test = InputSanitizationService::sanitizeText('<script>alert(1)</script>Hello');
echo 'Sanitization test: ' . (\$test === 'Hello' ? 'OK' : 'Failed');
"

echo.
echo ✅ اكتمل الفحص السريع!
echo.
echo 🎨 فحص الأيقونات الافتراضية...
if exist "public\css\default-icons.css" (
    echo ✅ ملف CSS للأيقونات الافتراضية موجود
) else (
    echo ❌ ملف CSS للأيقونات الافتراضية غير موجود!
)

echo.
echo 💡 لإعادة إنشاء قاعدة البيانات بالكامل مع الأيقونات الافتراضية، استخدم:
echo    reset_database.bat
echo.
echo 🎯 الآن ستظهر الإعلانات بأيقونات جميلة بدلاً من المربعات الفارغة!
echo.
pause
