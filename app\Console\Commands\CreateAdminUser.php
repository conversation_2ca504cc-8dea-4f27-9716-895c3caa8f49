<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Services\PasswordHashingService;
use Illuminate\Support\Str;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create
                            {--email= : البريد الإلكتروني للمدير}
                            {--name= : اسم المدير}
                            {--password= : كلمة المرور (اختيارية - سيتم إنشاء كلمة مرور قوية تلقائياً)}
                            {--force : فرض إعادة إنشاء المدير إذا كان موجوداً}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إنشاء مستخدم مدير جديد بكلمة مرور قوية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // الحصول على البيانات من المستخدم أو الخيارات
        $email = $this->option('email') ?: $this->ask('البريد الإلكتروني للمدير', '<EMAIL>');
        $name = $this->option('name') ?: $this->ask('اسم المدير', 'المدير العام');

        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('البريد الإلكتروني غير صحيح!');
            return 1;
        }

        // التحقق من وجود المستخدم
        $existingAdmin = User::where('email', $email)->first();

        if ($existingAdmin && !$this->option('force')) {
            if ($existingAdmin->is_admin) {
                $this->warn('المستخدم موجود بالفعل وهو مدير!');
                return 0;
            } else {
                if ($this->confirm('المستخدم موجود ولكنه ليس مدير. هل تريد ترقيته لمدير؟')) {
                    $existingAdmin->update(['is_admin' => true]);
                    $this->info('✅ تم ترقية المستخدم ليصبح مدير!');
                    return 0;
                }
                return 0;
            }
        }

        // إنشاء كلمة مرور قوية
        $password = $this->option('password') ?: $this->generateSecurePassword();

        // التحقق من قوة كلمة المرور
        if (!$this->isPasswordSecure($password)) {
            $this->error('كلمة المرور ضعيفة! يجب أن تحتوي على 12 حرف على الأقل، أحرف كبيرة وصغيرة، أرقام ورموز.');
            return 1;
        }

        try {
            if ($existingAdmin && $this->option('force')) {
                // تحديث المستخدم الموجود
                $existingAdmin->update([
                    'name' => $name,
                    'password' => PasswordHashingService::hash($password),
                    'is_admin' => true,
                    'email_verified_at' => now(),
                    'password_changed_at' => now(),
                ]);
                $admin = $existingAdmin;
                $this->info('✅ تم تحديث المستخدم المدير بنجاح!');
            } else {
                // إنشاء مستخدم جديد
                $admin = User::create([
                    'name' => $name,
                    'email' => $email,
                    'password' => PasswordHashingService::hash($password),
                    'is_admin' => true,
                    'email_verified_at' => now(),
                    'password_changed_at' => now(),
                    'currency' => 'YER',
                ]);
                $this->info('✅ تم إنشاء المستخدم المدير بنجاح!');
            }

            // عرض معلومات تسجيل الدخول
            $this->newLine();
            $this->line('📋 <fg=yellow>معلومات تسجيل الدخول:</fg=yellow>');
            $this->line('📧 البريد الإلكتروني: <fg=green>' . $email . '</fg=green>');
            $this->line('🔑 كلمة المرور: <fg=red>' . $password . '</fg=red>');
            $this->newLine();
            $this->warn('⚠️  احفظ كلمة المرور في مكان آمن وقم بتغييرها عند أول تسجيل دخول!');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ حدث خطأ أثناء إنشاء المدير: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * إنشاء كلمة مرور قوية
     */
    private function generateSecurePassword(): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        $password = '';

        // ضمان وجود حرف من كل نوع
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];

        // إضافة باقي الأحرف عشوائياً
        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < 16; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // خلط الأحرف
        return str_shuffle($password);
    }

    /**
     * التحقق من قوة كلمة المرور
     */
    private function isPasswordSecure(string $password): bool
    {
        // الحد الأدنى 12 حرف
        if (strlen($password) < 12) {
            return false;
        }

        // يجب أن تحتوي على حرف كبير
        if (!preg_match('/[A-Z]/', $password)) {
            return false;
        }

        // يجب أن تحتوي على حرف صغير
        if (!preg_match('/[a-z]/', $password)) {
            return false;
        }

        // يجب أن تحتوي على رقم
        if (!preg_match('/[0-9]/', $password)) {
            return false;
        }

        // يجب أن تحتوي على رمز خاص
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            return false;
        }

        return true;
    }
}
