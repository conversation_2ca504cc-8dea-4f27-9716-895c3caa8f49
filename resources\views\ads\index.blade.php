@extends('layouts.app')

@section('title', __('All Ads') . ' - ' . __('site_name'))
@section('description', __('Browse all advertisements and find what you need'))

@push('styles')
<style>
/* تحسينات خاصة بصفحة الإعلانات */
.ads-header h1 {
    font-size: 3.2rem !important;
    margin-bottom: 1.5rem !important;
}

.ads-header p {
    font-size: 1.3rem !important;
}

.header-stats .stat-item {
    font-size: 1.2rem !important;
    margin-bottom: 0.5rem;
}

.header-stats .stat-item i {
    font-size: 1.4rem !important;
    margin-left: 0.5rem;
}

.search-form .form-control {
    font-size: 1.1rem !important;
    padding: 1rem 1.5rem !important;
    border-radius: 10px !important;
}

.search-form .btn {
    font-size: 1.1rem !important;
    padding: 1rem 2rem !important;
    border-radius: 10px !important;
}

.category-filter .form-select {
    font-size: 1.1rem !important;
    padding: 1rem 1.5rem !important;
    border-radius: 10px !important;
}

/* تحسين بطاقات الإعلانات */
.ads-results .ad-card {
    border-radius: 16px !important;
    margin-bottom: 2rem !important;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.ads-results .ad-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 20px 40px rgba(0,0,0,0.12) !important;
}

/* تحسين الشبكة */
.ads-grid {
    margin-top: 2rem;
}

.ads-grid .row {
    margin: 0 -15px;
}

.ads-grid .col-lg-4,
.ads-grid .col-md-6 {
    padding: 0 15px;
}

/* تحسين العرض المتجاوب */
@media (max-width: 768px) {
    .ads-results .ad-card {
        margin-bottom: 1.5rem !important;
    }

    .ads-grid .row {
        margin: 0 -10px;
    }

    .ads-grid .col-lg-4,
    .ads-grid .col-md-6 {
        padding: 0 10px;
    }
}

/* تحسين حالة عدم وجود نتائج */
.no-results .empty-state {
    background: white;
    border-radius: 20px;
    padding: 3rem 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    max-width: 500px;
    margin: 0 auto;
}

.no-results .empty-state i {
    color: #dee2e6;
    margin-bottom: 1.5rem;
}

.no-results .empty-state h3 {
    color: #6c757d;
    font-weight: 600;
}

.no-results .empty-state p {
    font-size: 1.1rem;
    line-height: 1.6;
}

.no-results .empty-actions .btn {
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    margin: 0 0.5rem;
}
</style>
@endpush

@section('content')
<!-- القسم الرئيسي -->
<section class="ads-header py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="text-white arabic-text fw-bold mb-2">
                    <i class="fas fa-bullhorn me-2"></i>
                    {{ __('All Ads') }}
                </h1>
                <p class="text-white-50 arabic-text mb-0">
                    {{ __('Browse all advertisements and find what you need') }}
                </p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="header-stats text-white">
                    <div class="stat-item d-inline-block me-3">
                        <i class="fas fa-bullhorn"></i>
                        <span class="fw-bold">{{ $ads->total() }}</span>
                        <small class="arabic-text">{{ __('ads') }}</small>
                    </div>
                    <div class="stat-item d-inline-block">
                        <i class="fas fa-th-large"></i>
                        <span class="fw-bold">{{ $categories->count() }}</span>
                        <small class="arabic-text">{{ __('categories') }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم البحث والتصفية -->
<section class="search-filters py-4 bg-light">
    <div class="container">
        <form method="GET" action="{{ route('ads.index') }}" class="search-form">
            <div class="row g-3">
                <!-- شريط البحث -->
                <div class="col-lg-4 col-md-6">
                    <div class="search-input">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text"
                               name="search"
                               class="form-control arabic-text"
                               placeholder="{{ __('Search in ads...') }}"
                               value="{{ request('search') }}">
                    </div>
                </div>

                <!-- فلتر التصنيف -->
                <div class="col-lg-3 col-md-6">
                    <select name="category" class="form-select arabic-text">
                        <option value="">{{ __('All Categories') }}</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->slug }}"
                                    {{ request('category') == $category->slug ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- فلتر الموقع -->
                <div class="col-lg-3 col-md-6">
                    <input type="text"
                           name="location"
                           class="form-control arabic-text"
                           placeholder="{{ __('Location') }}"
                           value="{{ request('location') }}">
                </div>

                <!-- أزرار البحث -->
                <div class="col-lg-2 col-md-6">
                    <div class="search-buttons">
                        <button type="submit" class="btn btn-primary w-100 arabic-text">
                            <i class="fas fa-search me-2"></i>
                            {{ __('Search') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- فلاتر إضافية -->
            <div class="row g-3 mt-2">
                <div class="col-lg-3 col-md-6">
                    <select name="sort" class="form-select arabic-text">
                        <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>
                            {{ __('Newest First') }}
                        </option>
                        <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>
                            {{ __('Oldest First') }}
                        </option>
                        <option value="most_viewed" {{ request('sort') == 'most_viewed' ? 'selected' : '' }}>
                            {{ __('Most Viewed') }}
                        </option>
                    </select>
                </div>

                <div class="col-lg-3 col-md-6">
                    <select name="status" class="form-select arabic-text">
                        <option value="">{{ __('All Status') }}</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>
                            {{ __('Active') }}
                        </option>
                        <option value="expiring" {{ request('status') == 'expiring' ? 'selected' : '' }}>
                            {{ __('Expiring Soon') }}
                        </option>
                    </select>
                </div>

                <div class="col-lg-3 col-md-6">
                    @if(request()->hasAny(['search', 'category', 'location', 'sort', 'status']))
                        <a href="{{ route('ads.index') }}" class="btn btn-outline-secondary w-100 arabic-text">
                            <i class="fas fa-times me-2"></i>
                            {{ __('Clear Filters') }}
                        </a>
                    @endif
                </div>
            </div>
        </form>
    </div>
</section>

<!-- قسم النتائج -->
<section class="ads-results py-5">
    <div class="container">
        <!-- معلومات النتائج -->
        <div class="results-info mb-4">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h5 class="arabic-text mb-0">
                        @if(request('search') || request('category') || request('location'))
                            {{ __('Search Results') }}
                            @if(request('search'))
                                {{ __('for') }}: "{{ request('search') }}"
                            @endif
                        @else
                            {{ __('All Ads') }}
                        @endif
                    </h5>
                    <small class="text-muted arabic-text">
                        {{ __('Showing') }} {{ $ads->firstItem() ?? 0 }} - {{ $ads->lastItem() ?? 0 }}
                        {{ __('of') }} {{ $ads->total() }} {{ __('results') }}
                    </small>
                </div>
                <div class="col-lg-6 text-end">
                    <div class="view-options">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" onclick="setView('grid')" title="عرض شبكي">
                                <i class="fas fa-th me-1"></i>
                                <span class="d-none d-md-inline">شبكي</span>
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setView('list')" title="عرض قائمة">
                                <i class="fas fa-list me-1"></i>
                                <span class="d-none d-md-inline">قائمة</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض الإعلانات -->
        @if($ads->count() > 0)
            <div class="ads-grid" id="ads-container">
                <div class="row" id="ads-row">
                    @foreach($ads as $ad)
                        @include('components.ad-card.index', [
                            'ad' => $ad,
                            'variant' => 'default',
                            'columns' => 3,                    // 3 أعمدة في الشاشات الكبيرة
                            'showFavorites' => true,
                            'descriptionLength' => 100,
                            'showPricing' => true,
                            'showMeta' => true,
                            'showActions' => true,
                            'showExpiry' => true
                        ])
                    @endforeach
                </div>
            </div>

            <!-- التصفح (Pagination) -->
            <div class="pagination-wrapper mt-5">
                <div class="d-flex justify-content-center">
                    {{ $ads->links('pagination.custom') }}
                </div>
            </div>
        @else
            <!-- رسالة عدم وجود نتائج -->
            <div class="no-results text-center py-5">
                <div class="empty-state">
                    <i class="fas fa-search fa-5x text-muted mb-4"></i>
                    <h3 class="arabic-text text-muted mb-3">{{ __('No ads found') }}</h3>
                    <p class="text-muted arabic-text mb-4">
                        @if(request()->hasAny(['search', 'category', 'location']))
                            {{ __('Try adjusting your search criteria') }}
                        @else
                            {{ __('No ads available at the moment') }}
                        @endif
                    </p>
                    <div class="empty-actions">
                        @if(request()->hasAny(['search', 'category', 'location']))
                            <a href="{{ route('ads.index') }}" class="btn btn-primary arabic-text me-2">
                                <i class="fas fa-refresh me-2"></i>
                                {{ __('View All Ads') }}
                            </a>
                        @endif
                        <a href="{{ route('categories.index') }}" class="btn btn-outline-primary arabic-text">
                            <i class="fas fa-th-large me-2"></i>
                            {{ __('Browse Categories') }}
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- قسم التصنيفات السريعة -->
@if($categories->count() > 0)
<section class="quick-categories py-5 bg-light">
    <div class="container">
        <h4 class="arabic-text fw-bold text-center mb-4">{{ __('Browse by Category') }}</h4>
        <div class="row g-3">
            @foreach($categories as $category)
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="{{ route('ads.category', $category->slug) }}" class="text-decoration-none">
                        <div class="quick-category-card text-center p-3">
                            <i class="{{ $category->icon }} fa-2x text-primary mb-2"></i>
                            <h6 class="arabic-text fw-bold mb-1">{{ $category->name }}</h6>
                            <small class="text-muted">{{ $category->ads_count ?? 0 }} {{ __('ads') }}</small>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif
@endsection

@push('styles')
<style>
/* تنسيق رأس الصفحة */
.ads-header {
    position: relative;
    overflow: hidden;
}

.ads-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header-stats .stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
}

/* تنسيق البحث والفلاتر */
.search-input {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 2;
}

.search-input .form-control {
    padding-left: 45px;
    border-radius: 50px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.search-input .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-form .form-select,
.search-form .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.search-form .form-select:focus,
.search-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تنسيق النتائج */
.results-info {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.view-options .btn {
    border-radius: 8px;
}

/* تنسيق الشبكة */
.ads-grid {
    min-height: 400px;
}

/* حالة فارغة */
.empty-state {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* التصنيفات السريعة */
.quick-category-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.quick-category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .ads-header {
        text-align: center;
    }

    .header-stats {
        margin-top: 1rem;
        text-align: center !important;
    }

    .results-info .row {
        text-align: center;
    }

    .view-options {
        margin-top: 1rem;
        text-align: center !important;
    }

    .search-form .search-buttons {
        margin-top: 1rem;
    }
}

@media (max-width: 576px) {
    .search-input .form-control {
        padding-left: 40px;
        font-size: 0.9rem;
    }

    .search-icon {
        left: 12px;
        font-size: 0.9rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .quick-category-card {
        padding: 1rem 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// تغيير طريقة العرض
function setView(viewType) {
    const container = document.getElementById('ads-container');
    const row = document.getElementById('ads-row');
    const buttons = document.querySelectorAll('.view-options .btn');

    // إزالة الفئة النشطة من جميع الأزرار
    buttons.forEach(btn => btn.classList.remove('active'));

    if (viewType === 'grid') {
        row.className = 'row';
        row.classList.remove('list-view');
        buttons[0].classList.add('active');
        // إعادة العرض الشبكي
        document.querySelectorAll('.ad-card').forEach((card, index) => {
            card.parentElement.className = 'col-xl-4 col-lg-6 col-md-6 mb-5';
            card.style.display = '';
            card.style.flexDirection = '';
            card.style.alignItems = '';
        });
    } else if (viewType === 'list') {
        row.className = 'row list-view';
        buttons[1].classList.add('active');
        // تغيير العرض للقائمة
        document.querySelectorAll('.ad-card').forEach((card, index) => {
            card.parentElement.className = 'col-12 mb-5';
        });
    }

    // حفظ التفضيل في localStorage
    localStorage.setItem('ads_view_preference', viewType);
}

// استرجاع تفضيل العرض عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const savedView = localStorage.getItem('ads_view_preference');
    if (savedView) {
        setView(savedView);
    }
});

// تحديث النتائج عند تغيير الفلاتر
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.search-form');
    const selects = form.querySelectorAll('select[name="sort"], select[name="status"]');

    selects.forEach(select => {
        select.addEventListener('change', function() {
            form.submit();
        });
    });
});
</script>

<style>
/* تحسين أزرار التبديل */
.view-options .btn-group .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid #007bff;
    margin: 0 2px;
}

.view-options .btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.view-options .btn-group .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.view-options .btn-group .btn:not(.active) {
    background-color: white;
    color: #007bff;
}

/* تحسين responsive للأزرار */
@media (max-width: 768px) {
    .view-options .btn-group .btn {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
}
</style>
@endpush
