@extends('layouts.app')

@section('title', __('Access Forbidden'))

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-ban text-danger" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h1 class="display-4 text-danger mb-3">403</h1>
                    <h2 class="h4 mb-3">{{ __('Access Forbidden') }}</h2>
                    
                    <p class="text-muted mb-4">
                        {{ $message ?? __('You do not have permission to access this resource.') }}
                    </p>
                    
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            {{ __('Go to Home') }}
                        </a>
                        
                        <button onclick="history.back()" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            {{ __('Go Back') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        border: none;
        border-radius: 15px;
    }
    
    .btn {
        border-radius: 25px;
        padding: 10px 25px;
    }
    
    .display-4 {
        font-weight: bold;
    }
</style>
@endpush
