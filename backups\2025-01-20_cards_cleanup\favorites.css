/**
 * أنماط نظام المفضلة
 * تحتوي على جميع الأنماط المتعلقة بنظام المفضلة
 */

/* أيقونة المفضلة الأساسية */
.favorite-icon {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
}

.btn-favorite {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-favorite::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-favorite:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-favorite:hover::before {
    opacity: 1;
}

/* حالة التحميل والحماية من النقرات المتكررة */
.btn-favorite.loading {
    pointer-events: none;
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-favorite.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* منع النقرات المتكررة */
.btn-favorite[data-processing="true"] {
    pointer-events: none;
    opacity: 0.7;
}


.btn-favorite:active {
    transform: scale(0.95);
}

.btn-favorite i {
    font-size: 18px;
    color: #6c757d;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.btn-favorite.favorited i {
    color: #dc3545;
    animation: heartBeat 0.6s ease-in-out;
}

.btn-favorite:hover i {
    color: #dc3545;
}

.btn-favorite.loading {
    animation: spin 1s linear infinite;
}

.btn-favorite.loading i {
    opacity: 0.5;
}

/* تأثير نبضة القلب */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تأثير الدوران للتحميل */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تأثير النبض للعداد */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* شارة عداد المفضلة */
.favorites-count {
    font-weight: 600;
    font-size: 0.75rem;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* تحسينات للشارة في navbar */
.navbar-nav .nav-link .badge.favorites-count {
    font-size: 0.6rem;
    min-width: 18px;
    height: 18px;
    animation: pulse 2s infinite;
}

/* أيقونة المفضلة في navbar */
.navbar-nav .nav-link i.fa-heart {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover i.fa-heart {
    color: #dc3545;
    transform: scale(1.1);
    animation: heartBeat 0.6s ease-in-out;
}

/* صفحة المفضلة */
.favorites-page {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

.favorites-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
}

.favorites-stats {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* بطاقات المفضلة المحسنة */
.favorites-page .ad-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    background: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 480px; /* ارتفاع ثابت للتوحيد */
}

.favorites-page .ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.favorites-page .ad-card .card-body {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.favorites-page .ad-card .card-footer {
    background: rgba(248, 249, 250, 0.8);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: auto;
}

/* تحسين صورة الإعلان في المفضلة */
.favorites-page .ad-image {
    height: 280px; /* ارتفاع ثابت */
    overflow: hidden;
    position: relative;
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .favorites-page .ad-image {
        height: 320px;
    }

    .favorites-page .ad-card {
        min-height: 600px;
    }
}

/* تحسينات للشاشات المتوسطة */
@media (min-width: 992px) and (max-width: 1399px) {
    .favorites-page .ad-image {
        height: 300px;
    }

    .favorites-page .ad-card {
        min-height: 580px;
    }
}

.favorites-page .ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.favorites-page .ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.favorites-page .placeholder-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين العنوان والوصف */
.favorites-page .ad-title {
    font-size: 1.1rem;
    line-height: 1.4;
    min-height: 2.8rem; /* ارتفاع ثابت للعناوين */
    margin-bottom: 0.75rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* للمتصفحات الحديثة */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.favorites-page .ad-description {
    font-size: 0.9rem;
    line-height: 1.5;
    min-height: 3rem; /* ارتفاع ثابت للوصف */
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* للمتصفحات الحديثة */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

/* شارة التصنيف الجديدة في المفضلة - متناسبة مع المحتوى */
.favorites-page .category-badge-body {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
    width: fit-content; /* العرض حسب المحتوى */
    max-width: 100%; /* لا يتجاوز عرض الحاوي */
    white-space: nowrap; /* منع كسر النص */
    overflow: hidden; /* إخفاء النص الزائد */
    text-overflow: ellipsis; /* إضافة نقاط في حالة النص الطويل */
}

.favorites-page .category-badge-body:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.favorites-page .category-badge-body i {
    font-size: 0.9rem;
    margin-left: 0.3rem;
    flex-shrink: 0; /* منع تقليص الأيقونة */
}

/* تحسين النص داخل الشارة */
.favorites-page .category-badge-body .arabic-text {
    flex: 1;
    min-width: 0; /* للسماح بالتقليص */
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .favorites-page .category-badge-body {
        font-size: 0.75rem;
        padding: 0.3rem 0.6rem;
        max-width: 150px; /* حد أقصى للعرض في الشاشات الصغيرة */
    }

    .favorites-page .category-badge-body i {
        font-size: 0.8rem;
        margin-left: 0.2rem;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    .favorites-page .category-badge-body {
        font-size: 0.85rem;
        padding: 0.45rem 0.9rem;
    }
}

/* شارات الحالة في موقع شارة التصنيف - المفضلة */
.favorites-page .category-badge.urgent-badge {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.favorites-page .category-badge.new-badge {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.favorites-page .category-badge.urgent-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.5) !important;
}

.favorites-page .category-badge.new-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.5) !important;
}

/* شارة تاريخ إضافة المفضلة */
.favorites-page .category-badge.favorite-time-badge {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.favorites-page .category-badge.favorite-time-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.5) !important;
}

/* تحسين حالة التحميل لأيقونة القلب في المفضلة */
.favorites-page .btn-favorite.loading {
    animation: spin 1s linear infinite;
    opacity: 0.7;
    pointer-events: none;
}

.favorites-page .btn-favorite.loading i {
    opacity: 0.5;
}

/* تحسين معلومات الإعلان */
.favorites-page .ad-meta {
    margin-bottom: 1rem;
}

.favorites-page .meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.favorites-page .meta-item i {
    width: 18px;
    text-align: center;
    margin-left: 0.5rem;
}

/* تاريخ الإضافة للمفضلة */
.favorite-date {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.7rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* حالة فارغة */
.empty-state {
    max-width: 400px;
    margin: 0 auto;
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    color: #6c757d;
    margin-bottom: 1.5rem;
    opacity: 0.7;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    line-height: 1.6;
}

/* أزرار الإجراءات */
.favorite-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.favorite-actions .btn {
    border-radius: 8px;
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.favorite-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .btn-favorite {
        width: 35px;
        height: 35px;
    }
    
    .btn-favorite i {
        font-size: 16px;
    }
    
    .favorites-header {
        padding: 1.5rem 0;
        margin-bottom: 1.5rem;
    }
    
    .favorite-card .card-body {
        padding: 1rem;
    }
    
    .favorite-actions {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .favorite-actions .btn {
        width: 100%;
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

@media (max-width: 576px) {
    .favorites-page .ad-image {
        height: 220px;
    }

    .btn-favorite {
        width: 32px;
        height: 32px;
    }
    
    .btn-favorite i {
        font-size: 14px;
    }
    
    .favorites-header {
        padding: 1rem 0;
        margin-bottom: 1rem;
    }
    
    .empty-state {
        padding: 2rem 0.5rem;
    }
    
    .empty-state i {
        font-size: 3rem !important;
    }
    
    .favorite-date {
        font-size: 0.6rem;
        padding: 0.2rem 0.4rem;
    }
}

/* تحسينات الشبكة والتوزيع - استخدام Bootstrap Grid بدلاً من CSS Grid */
.favorites-page .ads-grid {
    margin-top: 2rem;
}

.favorites-page .ads-grid .row {
    margin: 0 -15px;
}

.favorites-page .ads-grid .col-xl-3,
.favorites-page .ads-grid .col-lg-4,
.favorites-page .ads-grid .col-md-6,
.favorites-page .ads-grid .col-sm-12 {
    padding: 0 15px;
}

/* تحسينات responsive للمفضلة */
@media (max-width: 768px) {
    .favorites-page .ads-grid .row {
        margin: 0 -10px;
    }

    .favorites-page .ads-grid .col-xl-3,
    .favorites-page .ads-grid .col-lg-4,
    .favorites-page .ads-grid .col-md-6,
    .favorites-page .ads-grid .col-sm-12 {
        padding: 0 10px;
    }
}

/* تحسينات إضافية للتخطيط الجديد (4 بطاقات في الصف) */
@media (min-width: 1200px) {
    /* تقليل المسافات قليلاً للشاشات الكبيرة لتوفير مساحة أكثر */
    .favorites-page .ads-grid .col-xl-3 {
        padding: 0 12px;
    }

    /* تحسين حجم النص للبطاقات الأصغر */
    .favorites-page .ad-card .card-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }

    .favorites-page .ad-card .card-text {
        font-size: 0.9rem;
        line-height: 1.4;
    }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .favorites-page .ads-grid .col-xl-3 {
        padding: 0 15px;
    }

    .favorites-page .ad-card .card-title {
        font-size: 1.15rem;
    }

    .favorites-page .ad-card .card-text {
        font-size: 0.95rem;
    }
}

/* تحسينات RTL/LTR للمفضلة */
[dir="rtl"] .favorites-page .favorite-icon {
    left: auto;
    right: 10px;
}

[dir="rtl"] .favorites-page .category-badge {
    right: auto;
    left: 10px;
}

[dir="rtl"] .favorites-page .favorite-date {
    right: auto;
    left: 10px;
}

[dir="rtl"] .favorites-page .meta-item i {
    margin-left: 0;
    margin-right: 0.5rem;
}

/* تحسينات للوضع المظلم */
[data-theme="dark"] .btn-favorite {
    background: rgba(0, 0, 0, 0.7);
    color: white;
}

[data-theme="dark"] .btn-favorite:hover {
    background: rgba(0, 0, 0, 0.9);
}

[data-theme="dark"] .favorites-page .ad-card {
    background: #2d3748;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .favorites-page .ad-card .card-footer {
    background: rgba(45, 55, 72, 0.8);
    border-top-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .favorites-page {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
}

[data-theme="dark"] .empty-state h3,
[data-theme="dark"] .empty-state p {
    color: #e2e8f0;
}

/* تحسينات الأداء */
.favorites-page .ad-card {
    will-change: transform, box-shadow;
    backface-visibility: hidden;
    perspective: 1000px;
}

.favorites-page .ad-image img {
    will-change: transform;
    backface-visibility: hidden;
}

/* تحسين التحميل التدريجي */
.favorites-page .ad-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.favorites-page .ad-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
    z-index: 10;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* تحسين الذاكرة والأداء */
.favorites-page #favorites-grid {
    contain: layout style paint;
}

.favorites-page .favorite-card-wrapper {
    contain: layout style paint;
    isolation: isolate;
}

/* تحسين التمرير */
.favorites-page {
    scroll-behavior: smooth;
}

/* تحسين الخطوط */
.favorites-page {
    font-display: swap;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين الصور */
.favorites-page .ad-image img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* تحسين الحدود */
.favorites-page .ad-card {
    border-image-slice: 1;
    border-image-source: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

/* تحسين الظلال */
.favorites-page .ad-card {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.favorites-page .ad-card:hover {
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15));
}

/* تأثيرات إضافية */
.favorite-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(220, 53, 69, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.favorite-card:hover::before {
    opacity: 1;
}

/* تحسين التركيز للوصولية */
.btn-favorite:focus {
    outline: 2px solid #dc3545;
    outline-offset: 2px;
}

.btn-favorite:focus-visible {
    outline: 2px solid #dc3545;
    outline-offset: 2px;
}

/* تحسين الطباعة */
@media print {
    .btn-favorite,
    .favorite-actions {
        display: none !important;
    }
    
    .favorite-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* تحسين عرض تاريخ انتهاء الصلاحية */
.expiry-info {
    padding: 0.5rem;
    border-radius: 8px;
    background: rgba(248, 249, 250, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.expiry-info .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 576px) {
    .expiry-info {
        flex-direction: column;
        gap: 0.5rem !important;
    }
    
    .expiry-info .badge {
        font-size: 0.7rem;
    }
}

