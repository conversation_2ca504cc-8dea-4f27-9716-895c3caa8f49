<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\AdController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\InteractionController;
use App\Http\Controllers\CompareController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| هنا يمكنك تسجيل مسارات الويب لتطبيقك. هذه المسارات
| يتم تحميلها بواسطة RouteServiceProvider ضمن مجموعة "web"
| middleware التي تحتوي على حالة الجلسة، CSRF protection، وغيرها.
|
*/

// ===== مسارات الصفحة الرئيسية =====
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/action-choice', [HomeController::class, 'actionChoice'])->name('action.choice');

// ===== مسارات الصفحات الثابتة =====
Route::get('/about', [HomeController::class, 'about'])->name('about')->middleware('security.headers');
Route::get('/testimonials', [HomeController::class, 'testimonials'])->name('testimonials')->middleware('security.headers');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact')->middleware('security.headers');
Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit')->middleware(['rate.limit:contact', 'csrf.enhanced']);

// ===== مسارات التصنيفات =====
Route::prefix('categories')->name('categories.')->group(function () {
    Route::get('/', [CategoryController::class, 'index'])->name('index');
    Route::get('/search', [CategoryController::class, 'search'])->name('search')->middleware('rate.limit:search');
    Route::get('/{slug}', [CategoryController::class, 'show'])->name('show');
    Route::get('/{slug}/stats', [CategoryController::class, 'stats'])->name('stats');
});

// ===== مسارات الإعلانات العامة (للعرض فقط) =====
Route::prefix('ads')->name('ads.')->group(function () {
    Route::get('/', [AdController::class, 'index'])->name('index');
    // توجيه البحث القديم إلى البحث الموحد
    Route::get('/search', function(Request $request) {
        return redirect()->route('search.index', $request->all());
    })->name('search');
    Route::get('/{categorySlug}', [AdController::class, 'byCategory'])->name('category'); // إعلانات الفئة
    Route::get('/{categorySlug}/{adSlug}', [AdController::class, 'show'])->name('show'); // تفاصيل الإعلان

    // مسارات كشف معلومات التواصل
    Route::post('/{adId}/reveal-phone', [AdController::class, 'revealPhone'])->name('reveal-phone');
    Route::post('/{adId}/reveal-email', [AdController::class, 'revealEmail'])->name('reveal-email');

    // مسار عرض إعلانات مستخدم معين
    Route::get('/user/{userId}', [AdController::class, 'byUser'])->name('by-user');
});

// ===== مسارات البحث الموحد =====
Route::prefix('search')->name('search.')->group(function () {
    Route::get('/', [App\Http\Controllers\SearchController::class, 'index'])->name('index')->middleware('rate.limit:search');
    Route::get('/quick', [App\Http\Controllers\SearchController::class, 'quickSearch'])->name('quick')->middleware('rate.limit:search');
    Route::get('/suggestions', [App\Http\Controllers\SearchController::class, 'suggestions'])->name('suggestions')->middleware('rate.limit:search');
    // توجيه البحث المتقدم القديم إلى البحث الموحد
    Route::get('/advanced', function(Request $request) {
        return redirect()->route('search.index', $request->all());
    })->name('advanced');
    Route::get('/location', [App\Http\Controllers\SearchController::class, 'byLocation'])->name('location');
    Route::post('/save-favorite', [App\Http\Controllers\SearchController::class, 'saveFavorite'])->name('save-favorite')->middleware(['auth', 'rate.limit:general']);
    Route::post('/clear-history', [App\Http\Controllers\SearchController::class, 'clearHistory'])->name('clear-history')->middleware(['auth', 'rate.limit:general']);
    Route::get('/export', [App\Http\Controllers\SearchController::class, 'export'])->name('export')->middleware(['auth', 'rate.limit:general']);
});

// ===== مسار تسجيل الدخول المباشر (حسب الخطة) =====
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login')->middleware('guest');

// ===== مسارات المصادقة =====
Route::prefix('auth')->name('auth.')->group(function () {
    // مسارات الضيوف فقط
    Route::middleware('guest')->group(function () {
        Route::post('/login', [AuthController::class, 'login'])->name('login.submit')->middleware('rate.limit:login');
        Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
        Route::post('/register', [AuthController::class, 'register'])->name('register.submit')->middleware('rate.limit:register');
    });

    // مسارات المستخدمين المسجلين فقط
    Route::middleware('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('/profile', [AuthController::class, 'profile'])->name('profile');
        Route::put('/profile', [AuthController::class, 'updateProfile'])->name('profile.update');
    });
});

// ===== مسارات عامة إضافية =====
// المقارنة
Route::get('/compare', [CompareController::class, 'index'])->name('compare.index');

// المفضلة الموحدة - النظام الجديد المحسن
Route::prefix('favorites')->name('favorites.')->middleware('auth')->group(function () {
    Route::get('/', [App\Http\Controllers\FavoriteController::class, 'index'])->name('index');
    Route::post('/{adId}', [App\Http\Controllers\FavoriteController::class, 'store'])->name('store');
    Route::delete('/{favoriteId}', [App\Http\Controllers\FavoriteController::class, 'destroy'])->name('destroy');
    Route::delete('/', [App\Http\Controllers\FavoriteController::class, 'clear'])->name('clear');
});

// ===== مسارات التفاعلات الموحدة (النظام الجديد) =====
Route::prefix('interactions')->name('interactions.')->middleware(['auth', 'rate.limit:general'])->group(function () {
    // المفضلة
    Route::post('/favorite/{adId}', [InteractionController::class, 'toggleFavorite'])->name('favorite.toggle');

    // التقييمات
    Route::post('/rating/{adId}', [InteractionController::class, 'addRating'])->name('rating.add');

    // التعليقات
    Route::post('/comment/{adId}', [InteractionController::class, 'addComment'])->name('comment.add');

    // عرض التفاعلات
    Route::get('/ad/{adId}', [InteractionController::class, 'getAdInteractions'])->name('ad.interactions');
    Route::get('/user', [InteractionController::class, 'getUserInteractions'])->name('user.interactions');

    // حذف التفاعل
    Route::delete('/{interactionId}', [InteractionController::class, 'deleteInteraction'])->name('delete');
});

// ===== مسارات لوحة التحكم للمستخدم - موحدة ومنظمة =====
Route::prefix('dashboard')->name('dashboard.')->middleware(['auth', 'account.security', 'rate.limit:general'])->group(function () {
    // الصفحة الرئيسية للوحة التحكم - تحتوي على كل شيء
    Route::get('/', [App\Http\Controllers\UserDashboardController::class, 'index'])->name('index');

    // إدارة الإعلانات - جميع العمليات موحدة تحت dashboard
    Route::prefix('ads')->name('ads.')->group(function () {
        Route::get('/', [App\Http\Controllers\UserDashboardController::class, 'myAds'])->name('index'); // عرض جميع الإعلانات
        Route::get('/create', [App\Http\Controllers\UserDashboardController::class, 'createAd'])->name('create');
        Route::post('/', [App\Http\Controllers\UserDashboardController::class, 'storeAd'])->name('store')->middleware('rate.limit:upload');
        Route::get('/{ad}/edit', [App\Http\Controllers\UserDashboardController::class, 'editAd'])->name('edit');
        Route::put('/{ad}', [App\Http\Controllers\UserDashboardController::class, 'updateAd'])->name('update')->middleware('rate.limit:upload');
        Route::delete('/{ad}', [App\Http\Controllers\UserDashboardController::class, 'deleteAd'])->name('delete');
    });

    // الإشعارات - مبسطة
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\UserDashboardController::class, 'notifications'])->name('index');
        Route::get('/{notification}', [App\Http\Controllers\UserDashboardController::class, 'showNotification'])->name('show');
        Route::post('/{notification}/read', [App\Http\Controllers\UserDashboardController::class, 'markNotificationAsRead'])->name('mark-read');
        Route::post('/mark-all-read', [App\Http\Controllers\UserDashboardController::class, 'markAllNotificationsAsRead'])->name('mark-all-read');
        Route::delete('/{notification}', [App\Http\Controllers\UserDashboardController::class, 'deleteNotification'])->name('delete');
    });

    // الملف الشخصي
    Route::get('/profile', [App\Http\Controllers\UserDashboardController::class, 'profile'])->name('profile');
    Route::put('/profile', [App\Http\Controllers\UserDashboardController::class, 'updateProfile'])->name('update-profile');

    // معلومات التواصل - معطل مؤقتاً للنظام الجديد
    /*
    Route::prefix('contacts')->name('contacts.')->group(function () {
        Route::get('/', [App\Http\Controllers\UserContactController::class, 'index'])->name('index');
        Route::post('/', [App\Http\Controllers\UserContactController::class, 'store'])->name('store');
        Route::get('/{contact}', [App\Http\Controllers\UserContactController::class, 'show'])->name('show');
        Route::put('/{contact}', [App\Http\Controllers\UserContactController::class, 'update'])->name('update');
        Route::delete('/{contact}', [App\Http\Controllers\UserContactController::class, 'destroy'])->name('destroy');
        Route::post('/update-order', [App\Http\Controllers\UserContactController::class, 'updateOrder'])->name('update-order');
        Route::post('/{contact}/toggle-public', [App\Http\Controllers\UserContactController::class, 'togglePublic'])->name('toggle-public');
        Route::post('/{contact}/toggle-primary', [App\Http\Controllers\UserContactController::class, 'togglePrimary'])->name('toggle-primary');
        Route::get('/types/available', [App\Http\Controllers\UserContactController::class, 'getContactTypes'])->name('types');
        Route::get('/privacy/levels', [App\Http\Controllers\UserContactController::class, 'getPrivacyLevels'])->name('privacy-levels');
        Route::post('/{contact}/reveal', [App\Http\Controllers\UserContactController::class, 'revealContact'])->name('reveal');
    });
    */

    // إعدادات الخصوصية والأمان - معطل مؤقتاً للنظام الجديد
    /*
    Route::prefix('privacy')->name('privacy.')->group(function () {
        Route::get('/', [App\Http\Controllers\UserPrivacyController::class, 'index'])->name('index');
        Route::put('/', [App\Http\Controllers\UserPrivacyController::class, 'update'])->name('update');
        Route::post('/reset', [App\Http\Controllers\UserPrivacyController::class, 'reset'])->name('reset');
        Route::get('/security-stats', [App\Http\Controllers\UserPrivacyController::class, 'getSecurityStats'])->name('security-stats');
        Route::get('/suspicious-activities', [App\Http\Controllers\UserPrivacyController::class, 'getSuspiciousActivities'])->name('suspicious-activities');
        Route::post('/block-ip', [App\Http\Controllers\UserPrivacyController::class, 'blockIp'])->name('block-ip');
        Route::post('/unblock-ip', [App\Http\Controllers\UserPrivacyController::class, 'unblockIp'])->name('unblock-ip');
    });
    */

    // المفضلة - تم نقلها للنظام الموحد /interactions



    // API endpoints للـ AJAX
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/notifications', [App\Http\Controllers\UserDashboardController::class, 'getRecentNotifications'])->name('notifications');
        Route::get('/stats', [App\Http\Controllers\UserDashboardController::class, 'getQuickStats'])->name('stats');
        // Route::post('/contact-access', [App\Http\Controllers\UserContactController::class, 'logAccess'])->name('contact-access'); // معطل للنظام الجديد

        // API endpoints للتفاعلات - تم نقلها للنظام الموحد /interactions
    });

    // API endpoints عامة (بدون مصادقة)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/ads/{adId}/basic-info', [AdController::class, 'getBasicInfo'])->name('ads.basic-info'); // معلومات أساسية للإعلان
    });
});

// ===== مسارات التقييمات والمراجعات - معطل للنظام الجديد =====
/*
Route::prefix('reviews')->name('reviews.')->group(function () {
    // عرض التقييمات لإعلان معين
    Route::get('/ad/{ad}', [App\Http\Controllers\ReviewController::class, 'index'])->name('index');
    Route::get('/ad/{ad}/create', [App\Http\Controllers\ReviewController::class, 'create'])->name('create')->middleware('auth');
    Route::post('/ad/{ad}', [App\Http\Controllers\ReviewController::class, 'store'])->name('store')->middleware(['auth', 'rate.limit:general']);
    Route::get('/ad/{ad}/{review}/edit', [App\Http\Controllers\ReviewController::class, 'edit'])->name('edit')->middleware('auth');
    Route::put('/ad/{ad}/{review}', [App\Http\Controllers\ReviewController::class, 'update'])->name('update')->middleware(['auth', 'rate.limit:general']);
    Route::delete('/ad/{ad}/{review}', [App\Http\Controllers\ReviewController::class, 'destroy'])->name('destroy')->middleware(['auth', 'rate.limit:general']);

    // إجراءات التقييمات
    Route::post('/{review}/helpful', [App\Http\Controllers\ReviewController::class, 'markAsHelpful'])->name('helpful')->middleware(['auth', 'rate.limit:general']);
    Route::post('/{review}/report', [App\Http\Controllers\ReviewController::class, 'report'])->name('report')->middleware(['auth', 'rate.limit:general']);

    // API endpoints
    Route::get('/ad/{ad}/api', [App\Http\Controllers\ReviewController::class, 'getReviews'])->name('api.get');
    Route::get('/ad/{ad}/stats', [App\Http\Controllers\ReviewController::class, 'getStats'])->name('api.stats');

    // تقييمات المستخدم
    Route::get('/my-reviews', [App\Http\Controllers\ReviewController::class, 'userReviews'])->name('user-reviews')->middleware('auth');
});
*/

// ===== مسارات اللغة =====
Route::prefix('language')->name('language.')->group(function () {
    Route::get('/{locale}', [LanguageController::class, 'switch'])->name('switch');

    // مسارات للاختبار والتطوير
    Route::get('/debug', [LanguageController::class, 'debug'])->name('debug');
    Route::get('/clear/session', function() {
        Session::forget('locale');
        return redirect()->back()->with('success', 'تم حذف Session بنجاح');
    })->name('clear.session');

    Route::get('/clear/cookie', function() {
        return redirect()->back()
            ->withCookie(Cookie::forget('locale'))
            ->with('success', 'تم حذف Cookie بنجاح');
    })->name('clear.cookie');

    Route::get('/clear/all', function() {
        Session::forget('locale');
        return redirect()->back()
            ->withCookie(Cookie::forget('locale'))
            ->with('success', 'تم حذف Session و Cookie بنجاح');
    })->name('clear.all');
});

// ===== مسارات إدارة الـ Logs (للتطوير فقط) =====
Route::prefix('logs')->name('logs.')->middleware('throttle:10,1')->group(function () {
    // عرض محتوى الـ logs
    Route::get('/view', function() {
        if (!config('app.debug')) {
            abort(404);
        }

        $logFile = storage_path('logs/laravel.log');
        $content = file_exists($logFile) ? file_get_contents($logFile) : 'الملف فارغ أو غير موجود';
        $size = file_exists($logFile) ? filesize($logFile) : 0;

        return response()->view('debug.logs', [
            'content' => $content,
            'size' => $size,
            'formatted_size' => $size > 0 ? number_format($size / 1024, 2) . ' KB' : '0 bytes'
        ]);
    })->name('view');

    // تصفير الـ logs
    Route::post('/clear', function() {
        if (!config('app.debug')) {
            abort(404);
        }

        \Artisan::call('logs:clear');
        return redirect()->back()->with('success', 'تم تصفير ملف الـ logs بنجاح');
    })->name('clear');

    // تحميل الـ logs
    Route::get('/download', function() {
        if (!config('app.debug')) {
            abort(404);
        }

        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile) && filesize($logFile) > 0) {
            return response()->download($logFile, 'laravel_' . date('Y-m-d_H-i-s') . '.log');
        }

        return redirect()->back()->with('error', 'ملف الـ logs فارغ أو غير موجود');
    })->name('download');
});

// ===== مسارات لوحة التحكم الإدارية =====
Route::prefix('admin')->name('admin.')->middleware(['account.security:admin', 'rate.limit:admin'])->group(function () {
    // مسارات الإعدادات العامة
    Route::get('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings');
    Route::get('/settings/{section}', [\App\Http\Controllers\Admin\SettingsController::class, 'section'])->name('settings.section');

    // مسارات إعدادات الإعلانات
    Route::put('/settings/ads', [\App\Http\Controllers\Admin\SettingsController::class, 'updateAdSettings'])->name('settings.ads.update');

    // مسارات إدارة الإعلانات
    Route::resource('announcements', \App\Http\Controllers\Admin\AnnouncementController::class);
    Route::post('announcements/settings', [\App\Http\Controllers\Admin\AnnouncementController::class, 'updateSettings'])->name('announcements.settings');
    Route::patch('announcements/{announcement}/toggle', [\App\Http\Controllers\Admin\AnnouncementController::class, 'toggleStatus'])->name('announcements.toggle');

    // مسارات إدارة إعلانات المستخدمين
    Route::prefix('ads')->name('ads.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\AdController::class, 'index'])->name('index');
        Route::get('/stats', [\App\Http\Controllers\Admin\AdController::class, 'stats'])->name('stats');
        Route::get('/{ad}', [\App\Http\Controllers\Admin\AdController::class, 'show'])->name('show');
        Route::post('/{ad}/approve', [\App\Http\Controllers\Admin\AdController::class, 'approve'])->name('approve');
        Route::post('/{ad}/reject', [\App\Http\Controllers\Admin\AdController::class, 'reject'])->name('reject');
        Route::patch('/{ad}/status', [\App\Http\Controllers\Admin\AdController::class, 'updateStatus'])->name('update-status');
        Route::patch('/{ad}/featured', [\App\Http\Controllers\Admin\AdController::class, 'toggleFeatured'])->name('toggle-featured');
        Route::delete('/{ad}', [\App\Http\Controllers\Admin\AdController::class, 'destroy'])->name('destroy');
        Route::post('/bulk-approve', [\App\Http\Controllers\Admin\AdController::class, 'bulkApprove'])->name('bulk-approve');
        Route::post('/bulk-reject', [\App\Http\Controllers\Admin\AdController::class, 'bulkReject'])->name('bulk-reject');
    });
});

// ===== مسارات API للإحصائيات =====
Route::prefix('api')->name('api.')->middleware('throttle:60,1')->group(function () {
    // إحصائيات الإعلان
    Route::get('/ads/{ad}/stats', [App\Http\Controllers\AdController::class, 'getStats'])->name('ads.stats');
    Route::post('/ads/{ad}/stats/export', [App\Http\Controllers\AdController::class, 'exportStats'])->name('ads.stats.export')->middleware('auth');

    // تحديث إحصائيات المشاهدة
    Route::post('/ads/{ad}/view', [App\Http\Controllers\AdController::class, 'recordView'])->name('ads.view');
});

// ===== مسارات آمنة للمصادقة =====
// تم إزالة مسارات التطوير غير الآمنة
// استخدم php artisan admin:create لإنشاء حساب مدير آمن

// ===== مسارات الاختبار (للتطوير فقط) =====
Route::get('/test-contacts', function () {
    return view('test-contacts');
})->name('test-contacts')->middleware('auth');

Route::get('/test-modal', function () {
    return view('test-modal');
})->name('test-modal')->middleware('auth');

Route::get('/simple-contact-test', function () {
    return view('simple-contact-test');
})->name('simple-contact-test')->middleware('auth');

Route::get('/test-security', function () {
    return view('test-security');
})->name('test-security')->middleware('auth');

Route::get('/test-contact-component', function () {
    return view('test-contact-component');
})->name('test-contact-component')->middleware('auth');
