{{--
    بطاقة الإعلان المخصصة لصفحة المفضلة
    تستخدم النظام الموحد مع إضافة وظائف المفضلة
--}}

@props([
    'ad',                           // الإعلان المطلوب عرضه (مطلوب)
    'favorite' => null,             // بيانات المفضلة
    'config' => []                  // إعدادات البطاقة
])

@php
    // دمج الإعدادات الموجودة مع الإعدادات الخاصة ببطاقة المفضلة
    $config = array_merge($config, [
        'variant' => 'favorites',
        'descriptionLength' => $config['descriptionLength'] ?? 80,
        'imageHeight' => '200px',
        'cardHeight' => '480px',
        'padding' => 'p-3',
        'marginBottom' => 'mb-4',
        'titleSize' => 'h6',
        'showFullMeta' => true,
        'showFavorites' => false,  // لا نظهر أيقونة المفضلة العادية
        'showRating' => true,
        'favoriteId' => $favorite->id ?? null,
        'favoriteDate' => $favorite->created_at ?? null,
    ]);
@endphp

{{-- تم نقل wrapper إلى النظام الموحد --}}

{{-- البطاقة الرئيسية --}}
<div class="card ad-card ad-card-favorites h-100 shadow-sm border-0"
     data-ad-id="{{ $ad->id }}"
     data-animation="fadeInUp"
     style="min-height: {{ $config['cardHeight'] }};">

    {{-- رأس البطاقة مع الصورة --}}
    <div class="card-header-custom">
        <!-- صورة الإعلان -->
        <div class="ad-image" style="height: {{ $config['imageHeight'] }};">
            @if($ad->image_url)
                <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}" class="card-img-top">
            @else
                <div class="placeholder-image">
                    <i class="{{ $ad->category->icon }} fa-4x text-muted"></i>
                </div>
            @endif

            <!-- أيقونة إزالة من المفضلة - محسنة للنظام الجديد -->
            <div class="favorite-icon favorite-remove-icon"
                 data-ad-id="{{ $ad->id }}"
                 data-favorite-id="{{ $config['favoriteId'] ?? '' }}">
                <button class="btn-favorite-remove btn-favorite"
                        data-ad-id="{{ $ad->id }}"
                        data-favorite-id="{{ $config['favoriteId'] ?? '' }}"
                        title="{{ __('Remove from favorites') }}">
                    <i class="fas fa-heart-broken"></i>
                </button>
            </div>

            {{-- استخدام النظام الموحد للشارات --}}
            @include('components.ad-card.partials.badges', [
                'ad' => $ad,
                'config' => $config
            ])
        </div>
    </div>

    {{-- محتوى البطاقة --}}
    <div class="card-body {{ $config['padding'] }}" style="overflow: visible; position: relative;">
        {{-- شارة التصنيف --}}
        @include('components.ad-card.partials.category-badge', ['ad' => $ad, 'config' => $config])

        {{-- عنوان الإعلان --}}
        @include('components.ad-card.partials.title', ['ad' => $ad, 'config' => $config])

        {{-- وصف الإعلان --}}
        @include('components.ad-card.partials.description', ['ad' => $ad, 'config' => $config])

        {{-- قسم التقييم --}}
        @if($config['showRating'] ?? true)
            @include('components.ad-card.partials.rating', ['ad' => $ad, 'config' => $config])
        @endif

        {{-- المعلومات الإضافية --}}
        @if($config['showMeta'])
            @include('components.ad-card.partials.meta', ['ad' => $ad, 'config' => $config])
        @endif

        {{-- معلومات التواصل --}}
        @include('components.ad-card.partials.contact', ['ad' => $ad, 'config' => $config])

        {{-- قسم الأسعار --}}
        @if($config['showPricing'])
            @include('components.ad-card.partials.pricing', ['ad' => $ad, 'config' => $config])
        @endif
    </div>

    {{-- تذييل البطاقة --}}
    @if($config['showActions'] || $config['showExpiry'])
        @include('components.ad-card.partials.footer', ['ad' => $ad, 'config' => $config])
    @endif

    {{-- تأثير hover --}}
    <div class="card-overlay"></div>
</div>

{{-- تحميل CSS مرة واحدة فقط --}}
@once
    @push('styles')
        <link rel="stylesheet" href="{{ asset('css/ad-cards.css') }}">
        <link rel="stylesheet" href="{{ asset('css/badge-fix.css') }}">
        <style>
        /* أنماط إضافية خاصة ببطاقات المفضلة */
        .favorite-remove-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }

        .btn-favorite-remove {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(220, 53, 69, 0.9);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-favorite-remove:hover {
            background: #dc3545;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .ad-card-favorites:hover {
            border-color: #dc3545 !important;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .btn-favorite-remove {
                width: 30px;
                height: 30px;
            }
        }
        </style>
    @endpush
@endonce

{{-- تحميل JavaScript مرة واحدة فقط --}}
@once
    @push('scripts')
        <script src="{{ asset('js/favorites.js') }}"></script>
        <script>
            // دالة مشاركة الإعلان
            function shareAd(url, title) {
                if (navigator.share) {
                    navigator.share({
                        title: title,
                        url: url
                    }).catch(console.error);
                } else {
                    // نسخ الرابط للحافظة
                    navigator.clipboard.writeText(url).then(function() {
                        showToast('{{ __("Link copied to clipboard") }}', 'success');
                    }).catch(function() {
                        // فتح نافذة مشاركة تقليدية
                        window.open('https://wa.me/?text=' + encodeURIComponent(title + ' - ' + url), '_blank');
                    });
                }
            }

            // تم نقل معالج الأحداث إلى النظام الموحد في public/js/favorites.js
            // يتم التعامل مع الأحداث تلقائياً عبر النظام الموحد
        </script>
    @endpush
@endonce
