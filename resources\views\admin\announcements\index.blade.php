@extends('layouts.app')

@section('title', __('Manage Announcements'))

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="arabic-text fw-bold text-primary">
                    <i class="fas fa-bullhorn me-2"></i>
                    {{ __('Manage Announcement Bar') }}
                </h1>
                <a href="{{ route('admin.announcements.create') }}" class="btn btn-primary arabic-text">
                    <i class="fas fa-plus me-2"></i>
                    {{ __('Add New Announcement') }}
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                @if(session('settings_updated'))
                    <script>
                        // تحديث الصفحة الرئيسية في نافذة أخرى إذا كانت مفتوحة
                        if (window.opener && !window.opener.closed) {
                            window.opener.location.reload();
                        }

                        // تحديث أي نوافذ أخرى مفتوحة للموقع
                        localStorage.setItem('announcement_settings_updated', Date.now());
                    </script>
                @endif
            @endif

            <!-- إعدادات الشريط -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات شريط الإعلانات
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.announcements.settings') }}" method="POST" id="settingsForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input type="hidden" name="is_enabled" value="0">
                                    <input class="form-check-input" type="checkbox" name="is_enabled"
                                           id="is_enabled" value="1" {{ $settings->is_enabled ? 'checked' : '' }}>
                                    <label class="form-check-label arabic-text" for="is_enabled">
                                        تفعيل شريط الإعلانات
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label arabic-text">نوع الحركة</label>
                                <select name="animation_type" class="form-select">
                                    <option value="rotation" {{ $settings->animation_type === 'rotation' ? 'selected' : '' }}>
                                        تبديل تلقائي
                                    </option>
                                    <option value="marquee" {{ $settings->animation_type === 'marquee' ? 'selected' : '' }}>
                                        تمرير أفقي
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label arabic-text">مدة التبديل (ثانية)</label>
                                <input type="number" name="transition_duration" class="form-control"
                                       value="{{ $settings->transition_duration }}" min="1" max="60">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label arabic-text">سرعة التمرير (ثانية)</label>
                                <input type="number" name="scroll_speed" class="form-control"
                                       value="{{ $settings->scroll_speed }}" min="5" max="60">
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="submit" class="btn btn-success" id="saveSettingsBtn">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                            <small class="text-muted ms-3">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم تطبيق التغييرات فوراً على جميع الصفحات
                            </small>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة الإعلانات -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الإعلانات ({{ $announcements->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($announcements->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الترتيب</th>
                                        <th>العنوان</th>
                                        <th>الأيقونة</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($announcements as $announcement)
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">{{ $announcement->sort_order }}</span>
                                            </td>
                                            <td>
                                                <div class="arabic-text">
                                                    <strong>{{ $announcement->title_ar }}</strong><br>
                                                    <small class="text-muted">{{ $announcement->title_en }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="{{ $announcement->icon }} {{ $announcement->color }} fa-lg"></i>
                                            </td>
                                            <td>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input status-toggle" type="checkbox"
                                                           data-id="{{ $announcement->id }}"
                                                           {{ $announcement->is_active ? 'checked' : '' }}>
                                                </div>
                                            </td>
                                            <td>
                                                <small>{{ $announcement->created_at->format('Y-m-d H:i') }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.announcements.edit', $announcement) }}"
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.announcements.destroy', $announcement) }}"
                                                          method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                onclick="return confirm('هل أنت متأكد من الحذف؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted arabic-text">لا توجد إعلانات</h5>
                            <p class="text-muted">ابدأ بإضافة إعلان جديد</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تبديل حالة تفعيل الإعلان
document.querySelectorAll('.status-toggle').forEach(toggle => {
    toggle.addEventListener('change', function() {
        const announcementId = this.dataset.id;
        const isActive = this.checked;

        fetch(`/admin/announcements/${announcementId}/toggle`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ is_active: isActive })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار رسالة نجاح
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show';
                alert.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.querySelector('.container').insertBefore(alert, document.querySelector('.row'));

                // إخفاء الرسالة بعد 3 ثوان
                setTimeout(() => {
                    alert.remove();
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.checked = !isActive; // إعادة الحالة السابقة
        });
    });
});

// تحديث فوري عند تغيير الإعدادات
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('saveSettingsBtn');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{{ __("Saving...") }}';
    submitBtn.disabled = true;

    // إضافة تأخير قصير لإظهار التحديث
    setTimeout(() => {
        // السماح للنموذج بالإرسال
        this.submit();
    }, 500);
});

// تحديث فوري عند تغيير نوع الحركة
document.querySelector('select[name="animation_type"]').addEventListener('change', function() {
    // إرسال النموذج تلقائياً عند تغيير نوع الحركة
    setTimeout(() => {
        document.getElementById('settingsForm').submit();
    }, 300);
});

// تحديث فوري عند تغيير حالة التفعيل
document.getElementById('is_enabled').addEventListener('change', function() {
    // إرسال النموذج تلقائياً عند تغيير حالة التفعيل
    setTimeout(() => {
        document.getElementById('settingsForm').submit();
    }, 300);
});
</script>
@endsection
