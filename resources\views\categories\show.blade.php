@extends('layouts.app')

@section('title', $category->name . ' - ' . __('Categories'))

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="category-header bg-gradient-primary text-white py-5 mb-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav aria-label="breadcrumb" class="mb-3">
                        <ol class="breadcrumb bg-transparent mb-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('home') }}" class="text-white-50">{{ __('Home') }}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('categories.index') }}" class="text-white-50">{{ __('Categories') }}</a>
                            </li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ $category->name }}</li>
                        </ol>
                    </nav>
                    
                    <div class="d-flex align-items-center mb-3">
                        <div class="category-icon me-3">
                            <i class="{{ $category->icon ?? 'fas fa-folder' }} fa-3x"></i>
                        </div>
                        <div>
                            <h1 class="h2 mb-1">{{ $category->name }}</h1>
                            @if($category->description)
                                <p class="mb-0 text-white-75">{{ $category->description }}</p>
                            @endif
                        </div>
                    </div>
                    
                    <div class="category-stats">
                        <span class="badge bg-light text-dark me-2">
                            <i class="fas fa-ad me-1"></i>
                            {{ $ads->total() }} {{ __('Ads') }}
                        </span>
                    </div>
                </div>
                
                <div class="col-md-4 text-end">
                    <a href="{{ route('ads.create') }}" class="btn btn-warning btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        {{ __('Add New Ad') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filters and Sorting -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <form method="GET" action="{{ route('categories.show', $category->slug) }}" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">{{ __('Sort By') }}</label>
                                <select name="sort" class="form-select">
                                    <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>{{ __('Latest') }}</option>
                                    <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>{{ __('Oldest') }}</option>
                                    <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>{{ __('Price: Low to High') }}</option>
                                    <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>{{ __('Price: High to Low') }}</option>
                                    <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>{{ __('Most Popular') }}</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">{{ __('Price Range') }}</label>
                                <select name="price_range" class="form-select">
                                    <option value="">{{ __('All Prices') }}</option>
                                    <option value="0-1000" {{ request('price_range') == '0-1000' ? 'selected' : '' }}>0 - 1,000</option>
                                    <option value="1000-5000" {{ request('price_range') == '1000-5000' ? 'selected' : '' }}>1,000 - 5,000</option>
                                    <option value="5000-10000" {{ request('price_range') == '5000-10000' ? 'selected' : '' }}>5,000 - 10,000</option>
                                    <option value="10000+" {{ request('price_range') == '10000+' ? 'selected' : '' }}>10,000+</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">{{ __('Location') }}</label>
                                <input type="text" name="location" class="form-control" placeholder="{{ __('Enter location') }}" value="{{ request('location') }}">
                            </div>
                            
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>
                                    {{ __('Filter') }}
                                </button>
                                <a href="{{ route('categories.show', $category->slug) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    {{ __('Clear') }}
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ads Grid -->
        <div class="row">
            @if($ads->count() > 0)
                @foreach($ads as $ad)
                    <div class="col-lg-4 col-md-6 mb-4">
                        @include('components.ad-card.index', [
                            'ad' => $ad,
                            'showCategory' => false,
                            'showFavorite' => true,
                            'showContact' => false,
                            'showExpiry' => false
                        ])
                    </div>
                @endforeach
            @else
                <div class="col-12">
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-search fa-4x text-muted"></i>
                        </div>
                        <h3 class="text-muted">{{ __('No ads found') }}</h3>
                        <p class="text-muted">{{ __('No ads found in this category. Be the first to post!') }}</p>
                        <a href="{{ route('ads.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            {{ __('Add New Ad') }}
                        </a>
                    </div>
                </div>
            @endif
        </div>

        <!-- Pagination -->
        @if($ads->hasPages())
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-center">
                        {{ $ads->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.category-icon {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 15px;
    backdrop-filter: blur(10px);
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
@endpush
