
<?php if($config['showRating'] ?? true): ?>
    <div class="rating-section mb-2">
        <?php
            // استخدام النظام الجديد للتقييمات
            $averageRating = $ad->rating_average ?? 0;
            $ratingsCount = $ad->ratings_count ?? 0;
            $userRating = null;

            // الحصول على تقييم المستخدم الحالي من النظام الجديد
            if (auth()->check()) {
                $userInteraction = \App\Models\Interaction::where([
                    'user_id' => auth()->id(),
                    'ad_id' => $ad->id,
                    'type' => 'rating'
                ])->first();

                if ($userInteraction && isset($userInteraction->data['rating'])) {
                    $userRating = (object) [
                        'rating' => $userInteraction->data['rating'],
                        'comment' => $userInteraction->data['comment'] ?? null
                    ];
                }
            }
        ?>

        <div class="d-flex align-items-center justify-content-between">
            <!-- عرض النجوم والمتوسط -->
            <div class="rating-display d-flex align-items-center">
                <!-- النجوم -->
                <div class="stars-display me-2" data-rating="<?php echo e($averageRating); ?>">
                    <?php for($i = 1; $i <= 5; $i++): ?>
                        <?php if($i <= floor($averageRating)): ?>
                            <i class="fas fa-star text-warning"></i>
                        <?php elseif($i - 0.5 <= $averageRating): ?>
                            <i class="fas fa-star-half-alt text-warning"></i>
                        <?php else: ?>
                            <i class="far fa-star text-muted"></i>
                        <?php endif; ?>
                    <?php endfor; ?>
                </div>

                <!-- المتوسط والعدد -->
                <div class="rating-info">
                    <span class="rating-average fw-bold text-dark"><?php echo e(number_format($averageRating, 1)); ?></span>
                    <small class="text-muted">
                        (<?php echo e($ratingsCount); ?> <?php echo e($ratingsCount == 1 ? __('review') : __('reviews')); ?>)
                    </small>
                </div>
            </div>

            <!-- زر إضافة تقييم -->
            <?php if(auth()->guard()->check()): ?>
                <div class="rating-action">
                    <?php if($userRating): ?>
                        <!-- المستخدم قيّم مسبقاً -->
                        <button class="btn btn-outline-success btn-sm" 
                                onclick="showUserRating(<?php echo e($ad->id); ?>)"
                                title="<?php echo e(__('You rated this ad')); ?>">
                            <i class="fas fa-check me-1"></i>
                            <?php echo e($userRating->rating); ?>

                        </button>
                    <?php else: ?>
                        <!-- المستخدم لم يقيّم بعد -->
                        <button class="btn btn-outline-primary btn-sm" 
                                onclick="openRatingModal(<?php echo e($ad->id); ?>)"
                                title="<?php echo e(__('Rate this ad')); ?>">
                            <i class="fas fa-star me-1"></i>
                            <?php echo e(__('Rate')); ?>

                        </button>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <!-- غير مسجل الدخول -->
                <div class="rating-action">
                    <small class="text-muted">
                        <a href="<?php echo e(route('login')); ?>" class="text-decoration-none">
                            <?php echo e(__('Login to rate')); ?>

                        </a>
                    </small>
                </div>
            <?php endif; ?>
        </div>

        <!-- شريط التقييم المفصل (اختياري) -->
        <?php if($config['showDetailedRating'] ?? false): ?>
            <div class="rating-breakdown mt-2">
                <?php $distribution = $ad->rating_distribution; ?>
                <?php for($i = 5; $i >= 1; $i--): ?>
                    <div class="rating-bar d-flex align-items-center mb-1">
                        <span class="rating-label me-2"><?php echo e($i); ?></span>
                        <i class="fas fa-star text-warning me-1"></i>
                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                            <?php
                                $percentage = $ratingsCount > 0 ? ($distribution[$i] / $ratingsCount) * 100 : 0;
                            ?>
                            <div class="progress-bar bg-warning" 
                                 style="width: <?php echo e($percentage); ?>%"></div>
                        </div>
                        <small class="text-muted"><?php echo e($distribution[$i]); ?></small>
                    </div>
                <?php endfor; ?>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>


<?php if(auth()->guard()->check()): ?>
<div class="modal fade" id="ratingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__('Rate this ad')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="ratingForm">
                    <input type="hidden" id="ratingAdId" name="ad_id">
                    
                    <!-- النجوم التفاعلية -->
                    <div class="mb-3">
                        <label class="form-label"><?php echo e(__('Your rating')); ?></label>
                        <div class="interactive-stars" id="interactiveStars">
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <i class="far fa-star star-interactive" 
                                   data-rating="<?php echo e($i); ?>"
                                   onclick="setRating(<?php echo e($i); ?>)"></i>
                            <?php endfor; ?>
                        </div>
                        <input type="hidden" id="selectedRating" name="rating" required>
                        <small class="text-muted"><?php echo e(__('Click on stars to rate')); ?></small>
                    </div>

                    <!-- التعليق -->
                    <div class="mb-3">
                        <label for="ratingComment" class="form-label"><?php echo e(__('Comment')); ?> (<?php echo e(__('optional')); ?>)</label>
                        <textarea class="form-control" 
                                  id="ratingComment" 
                                  name="comment" 
                                  rows="3" 
                                  placeholder="<?php echo e(__('Share your experience with this ad...')); ?>"></textarea>
                    </div>

                    <!-- تقييمات فرعية -->
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label"><?php echo e(__('Quality')); ?></label>
                            <select class="form-select form-select-sm" name="quality_rating">
                                <option value=""><?php echo e(__('Select')); ?></option>
                                <option value="excellent"><?php echo e(__('Excellent')); ?></option>
                                <option value="good"><?php echo e(__('Good')); ?></option>
                                <option value="average"><?php echo e(__('Average')); ?></option>
                                <option value="poor"><?php echo e(__('Poor')); ?></option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label"><?php echo e(__('Price')); ?></label>
                            <select class="form-select form-select-sm" name="price_rating">
                                <option value=""><?php echo e(__('Select')); ?></option>
                                <option value="excellent"><?php echo e(__('Excellent')); ?></option>
                                <option value="good"><?php echo e(__('Good')); ?></option>
                                <option value="average"><?php echo e(__('Average')); ?></option>
                                <option value="poor"><?php echo e(__('Poor')); ?></option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label"><?php echo e(__('Service')); ?></label>
                            <select class="form-select form-select-sm" name="service_rating">
                                <option value=""><?php echo e(__('Select')); ?></option>
                                <option value="excellent"><?php echo e(__('Excellent')); ?></option>
                                <option value="good"><?php echo e(__('Good')); ?></option>
                                <option value="average"><?php echo e(__('Average')); ?></option>
                                <option value="poor"><?php echo e(__('Poor')); ?></option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                <button type="button" class="btn btn-primary" onclick="submitRating()"><?php echo e(__('Submit Rating')); ?></button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
/* تنسيق النجوم */
.stars-display i {
    font-size: 1rem;
    margin-right: 2px;
}

.interactive-stars {
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

.star-interactive {
    cursor: pointer;
    margin-right: 5px;
    transition: all 0.2s ease;
    color: #ddd;
}

.star-interactive:hover,
.star-interactive.active {
    color: #ffc107;
    transform: scale(1.1);
}

.rating-breakdown .progress {
    height: 8px;
    background-color: #f8f9fa;
}

.rating-breakdown .rating-label {
    min-width: 15px;
    font-size: 0.875rem;
}

.rating-action .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}
</style>

<script>
// متغيرات التقييم
let currentAdId = null;
let selectedRating = 0;

// فتح modal التقييم
function openRatingModal(adId) {
    currentAdId = adId;
    document.getElementById('ratingAdId').value = adId;
    resetRatingForm();
    
    const modal = new bootstrap.Modal(document.getElementById('ratingModal'));
    modal.show();
}

// إعادة تعيين النموذج
function resetRatingForm() {
    selectedRating = 0;
    document.getElementById('selectedRating').value = '';
    document.getElementById('ratingComment').value = '';
    
    // إعادة تعيين النجوم
    document.querySelectorAll('.star-interactive').forEach(star => {
        star.classList.remove('active');
        star.classList.remove('fas');
        star.classList.add('far');
    });
    
    // إعادة تعيين التقييمات الفرعية
    document.querySelectorAll('select[name$="_rating"]').forEach(select => {
        select.value = '';
    });
}

// تعيين التقييم
function setRating(rating) {
    selectedRating = rating;
    document.getElementById('selectedRating').value = rating;
    
    // تحديث النجوم
    document.querySelectorAll('.star-interactive').forEach((star, index) => {
        if (index < rating) {
            star.classList.remove('far');
            star.classList.add('fas', 'active');
        } else {
            star.classList.remove('fas', 'active');
            star.classList.add('far');
        }
    });
}

// إرسال التقييم
function submitRating() {
    if (selectedRating === 0) {
        alert('<?php echo e(__("Please select a rating")); ?>');
        return;
    }
    
    const formData = new FormData(document.getElementById('ratingForm'));
    
    fetch('<?php echo e(route("interactions.rating.add", ":adId")); ?>'.replace(':adId', currentAdId), {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق المودال
            bootstrap.Modal.getInstance(document.getElementById('ratingModal')).hide();
            
            // تحديث واجهة التقييم
            updateRatingDisplay(currentAdId, data.rating);
            
            // إظهار رسالة نجاح
            if (typeof showToast === 'function') {
                showToast(data.message, 'success');
            } else {
                alert(data.message);
            }
        } else {
            alert(data.message || '<?php echo e(__("Error submitting rating")); ?>');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('<?php echo e(__("Unexpected error occurred")); ?>');
    });
}

// تحديث عرض التقييم
function updateRatingDisplay(adId, ratingData) {
    // يمكن إضافة منطق تحديث الواجهة هنا
    location.reload(); // حل مؤقت
}

// عرض تقييم المستخدم
function showUserRating(adId) {
    // يمكن إضافة منطق عرض تفاصيل التقييم
    alert('<?php echo e(__("You have already rated this ad")); ?>');
}
</script>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/partials/rating.blade.php ENDPATH**/ ?>