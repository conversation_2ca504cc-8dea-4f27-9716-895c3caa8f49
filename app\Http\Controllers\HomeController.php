<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Ad;
use App\Models\Feedback;
use App\Services\InputSanitizationService;
use App\Services\CacheService;

/**
 * كونترولر الصفحة الرئيسية
 * يدير الصفحة الرئيسية وصفحة اختيار الإجراء
 */
class HomeController extends Controller
{
    /**
     * عرض الصفحة الرئيسية
     * تحتوي على زر "ابدأ الآن" والمعلومات الأساسية
     */
    public function index()
    {
        // الحصول على الإحصائيات من الكاش لتحسين الأداء
        $stats = CacheService::getSiteStats();

        // الحصول على الإعلانات المميزة من الكاش
        $featuredAds = CacheService::getFeaturedAds(6);

        // الحصول على الإعلانات الأكثر مشاهدة من الكاش
        $popularAds = CacheService::getPopularAds(6);

        return view('home.index', compact('stats', 'featuredAds', 'popularAds'));
    }

    /**
     * عرض صفحة اختيار الإجراء
     * تحتوي على زرين: "عرض الإعلانات" و "تسجيل الدخول"
     */
    public function actionChoice()
    {
        return view('home.action-choice');
    }

    /**
     * عرض صفحة "من نحن"
     */
    public function about()
    {
        return view('pages.about');
    }

    /**
     * عرض صفحة "آراء العملاء"
     */
    public function testimonials()
    {
        // جلب التقييمات المعتمدة مع ترتيب حسب التقييم
        $feedbacks = Feedback::approved()
            ->orderBy('rating', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('pages.testimonials', compact('feedbacks'));
    }

    /**
     * عرض صفحة "تواصل معنا"
     */
    public function contact()
    {
        return view('pages.contact');
    }

    /**
     * معالجة نموذج "تواصل معنا" (رسائل التواصل)
     */
    public function contactSubmit(Request $request)
    {
        // التحقق من صحة البيانات
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'subject' => 'required|string|in:general,support,business,complaint,suggestion',
            'message' => 'required|string|max:1000',
        ]);

        // تنظيف البيانات المدخلة لحماية من XSS
        $validated = InputSanitizationService::sanitizeFormData($validated);

        // إنشاء تقييم جديد (نستخدم نموذج Feedback لحفظ رسائل التواصل)
        Feedback::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'message' => $validated['message'],
            'rating' => 5, // تقييم افتراضي لرسائل التواصل
            'phone' => null,
            'company' => $validated['subject'], // نحفظ الموضوع في حقل الشركة مؤقتاً
            'status' => 'pending', // في انتظار الموافقة
        ]);

        // رسالة نجاح
        return redirect()->back()->with('success', 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
    }
}
