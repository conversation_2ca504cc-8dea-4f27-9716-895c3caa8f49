<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>عارض ملفات الـ Logs - <?php echo e(config('app.name')); ?></title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .log-container {
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 600px;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .log-line {
            padding: 2px 10px;
            border-bottom: 1px solid #333;
        }
        
        .log-line:hover {
            background-color: #2d2d2d;
        }
        
        .log-error {
            background-color: #3d1a1a;
            color: #ff6b6b;
        }
        
        .log-warning {
            background-color: #3d3d1a;
            color: #ffd93d;
        }
        
        .log-info {
            background-color: #1a3d3d;
            color: #6bcf7f;
        }
        
        .log-debug {
            background-color: #1a1a3d;
            color: #74c0fc;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .btn-action {
            margin: 5px;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-file-alt text-primary me-2"></i>
                        عارض ملفات الـ Logs
                    </h1>
                    <a href="<?php echo e(route('home')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-1"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-2">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات ملف الـ Logs
                            </h4>
                            <p class="mb-0">
                                <strong>حجم الملف:</strong> <?php echo e($formatted_size); ?>

                                <span class="mx-3">|</span>
                                <strong>عدد الأسطر:</strong> <?php echo e($size > 0 ? substr_count($content, "\n") + 1 : 0); ?>

                                <span class="mx-3">|</span>
                                <strong>آخر تحديث:</strong> <?php echo e(file_exists(storage_path('logs/laravel.log')) ? date('Y-m-d H:i:s', filemtime(storage_path('logs/laravel.log'))) : 'غير متاح'); ?>

                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('logs.view')); ?>" class="btn btn-light btn-action">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    تحديث
                                </a>
                                <?php if($size > 0): ?>
                                    <a href="<?php echo e(route('logs.download')); ?>" class="btn btn-success btn-action">
                                        <i class="fas fa-download me-1"></i>
                                        تحميل
                                    </a>
                                    <form method="POST" action="<?php echo e(route('logs.clear')); ?>" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="btn btn-danger btn-action" 
                                                onclick="return confirm('هل أنت متأكد من تصفير ملف الـ logs؟')">
                                            <i class="fas fa-trash me-1"></i>
                                            تصفير
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo e(session('error')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Log Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            محتوى ملف laravel.log
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if($size > 0): ?>
                            <div class="log-container">
                                <?php
                                    $lines = explode("\n", $content);
                                    $lines = array_reverse($lines); // عرض الأحدث أولاً
                                ?>
                                
                                <?php $__currentLoopData = $lines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(trim($line)): ?>
                                        <?php
                                            $class = '';
                                            if (str_contains($line, '.ERROR:')) {
                                                $class = 'log-error';
                                            } elseif (str_contains($line, '.WARNING:')) {
                                                $class = 'log-warning';
                                            } elseif (str_contains($line, '.INFO:')) {
                                                $class = 'log-info';
                                            } elseif (str_contains($line, '.DEBUG:')) {
                                                $class = 'log-debug';
                                            }
                                        ?>
                                        <div class="log-line <?php echo e($class); ?>"><?php echo e($line); ?></div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-file-alt"></i>
                                <h4>ملف الـ Logs فارغ</h4>
                                <p class="text-muted">لا توجد رسائل logs حالياً. ابدأ باستخدام التطبيق لرؤية الرسائل هنا.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        هذه الصفحة متاحة فقط في وضع التطوير (Debug Mode)
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Auto-refresh every 30 seconds -->
    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(function() {
            if (<?php echo e($size); ?> > 0) {
                location.reload();
            }
        }, 30000);
        
        // تمرير سلس للأسفل عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            const logContainer = document.querySelector('.log-container');
            if (logContainer) {
                logContainer.scrollTop = 0; // البداية لأن الأحدث في الأعلى
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/debug/logs.blade.php ENDPATH**/ ?>