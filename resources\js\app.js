import './bootstrap';
import './theme-switcher';
import './interactions';

// تحسينات إضافية للأداء
document.addEventListener('DOMContentLoaded', () => {
    // تحسين الأداء بتأخير تحميل الوظائف غير الأساسية
    setTimeout(() => {
        // تحميل الوظائف الإضافية بعد تحميل الصفحة
        if (window.uiInteractions) {
            console.log('UI Interactions loaded successfully');
        }
    }, 100);
});
