<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware لتطبيق اللغة المحفوظة في الجلسة
 */
class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // الحصول على اللغة من الجلسة أو استخدام الافتراضية
        $locale = Session::get('locale', config('app.locale', 'ar'));

        // التحقق من أن اللغة مدعومة
        $supportedLocales = config('app.supported_locales', ['ar', 'en']);
        if (!in_array($locale, $supportedLocales)) {
            $locale = 'ar';
        }

        // تطبيق اللغة
        App::setLocale($locale);

        // تسجيل العملية
        Log::info('SetLocale: تم تطبيق اللغة', [
            'locale' => $locale,
            'route' => $request->route() ? $request->route()->getName() : 'unknown',
            'url' => $request->url()
        ]);

        return $next($request);
    }
}
