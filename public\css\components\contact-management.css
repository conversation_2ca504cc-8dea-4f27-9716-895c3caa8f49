/*
 * أنماط CSS لإدارة معلومات التواصل في الملف الشخصي
 * يوفر تصميم جميل ومتجاوب لواجهة إدارة معلومات التواصل
 */

/* ===== حاوي معلومات التواصل ===== */
.contacts-container {
    max-height: 500px;
    overflow-y: auto;
}

.contacts-container::-webkit-scrollbar {
    width: 6px;
}

.contacts-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.contacts-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.contacts-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== عنصر معلومة التواصل ===== */
.contact-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.contact-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.contact-item .card-body {
    padding: 1rem;
}

/* ===== أيقونات أنواع التواصل ===== */
.contact-item i {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.contact-item i.fa-phone {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.contact-item i.fa-whatsapp {
    background: rgba(37, 211, 102, 0.1);
    color: #25d366;
}

.contact-item i.fa-envelope {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.contact-item i.fa-telegram {
    background: rgba(0, 136, 204, 0.1);
    color: #0088cc;
}

.contact-item i.fa-instagram {
    background: rgba(225, 48, 108, 0.1);
    color: #e1306c;
}

.contact-item i.fa-facebook {
    background: rgba(24, 119, 242, 0.1);
    color: #1877f2;
}

.contact-item i.fa-twitter {
    background: rgba(29, 161, 242, 0.1);
    color: #1da1f2;
}

.contact-item i.fa-linkedin {
    background: rgba(0, 119, 181, 0.1);
    color: #0077b5;
}

.contact-item i.fa-globe {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* ===== شارات الحالة ===== */
.contact-item .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-info {
    background-color: #17a2b8 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* ===== أزرار الإجراءات ===== */
.contact-item .btn-group .btn {
    border-radius: 4px;
    margin: 0 1px;
    padding: 0.375rem 0.5rem;
}

.contact-item .btn-group .btn:hover {
    transform: scale(1.05);
}

/* ===== Modal معلومات التواصل ===== */
#contactModal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

#contactModal .modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

#contactModal .modal-title {
    font-weight: 600;
}

#contactModal .btn-close {
    filter: invert(1);
}

#contactModal .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

#contactModal .form-control,
#contactModal .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

#contactModal .form-control:focus,
#contactModal .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#contactModal .form-check-input {
    margin-top: 0.25rem;
}

#contactModal .form-check-label {
    font-weight: 500;
    color: #495057;
}

/* ===== رسالة عدم وجود معلومات ===== */
#no-contacts-message {
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

#no-contacts-message i {
    opacity: 0.5;
}

/* ===== أزرار الإجراءات العلوية ===== */
.profile-card .card-header .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.profile-card .card-header .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* ===== تحسينات التجاوب ===== */
@media (max-width: 768px) {
    .contact-item .row {
        text-align: center;
    }
    
    .contact-item .col-md-3 {
        margin-top: 1rem;
    }
    
    .contact-item .btn-group {
        justify-content: center;
    }
    
    .contacts-container {
        max-height: 400px;
    }
    
    #contactModal .modal-dialog {
        margin: 1rem;
    }
    
    #contactModal .row .col-md-6 {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .contact-item .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-item i {
        margin: 0 auto 0.5rem;
    }
    
    .contact-item .badge {
        margin: 0.25rem;
    }
    
    .profile-card .card-header .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
}

/* ===== تحسينات الأداء ===== */
.contact-item {
    will-change: transform;
}

.contact-item:hover {
    will-change: auto;
}

/* ===== تحسينات إمكانية الوصول ===== */
.contact-item .btn:focus,
#contactModal .form-control:focus,
#contactModal .form-select:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* ===== حالة التحميل ===== */
.contacts-container .spinner-border {
    width: 2rem;
    height: 2rem;
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .contact-item .btn-group,
    .profile-card .card-header .btn {
        display: none !important;
    }
    
    .contact-item {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
