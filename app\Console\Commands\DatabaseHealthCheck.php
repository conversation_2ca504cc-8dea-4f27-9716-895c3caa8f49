<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Exception;

class DatabaseHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'db:health-check {--fix : محاولة إصلاح المشاكل تلقائياً}';

    /**
     * The console command description.
     */
    protected $description = 'فحص حالة قاعدة البيانات والاتصال';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء فحص حالة قاعدة البيانات المحسنة...');

        $issues = [];
        $warnings = [];

        // 1. فحص الاتصال بقاعدة البيانات
        $this->info('📡 فحص الاتصال بقاعدة البيانات...');
        try {
            DB::connection()->getPdo();
            $this->info('✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح');
        } catch (Exception $e) {
            $issues[] = "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage();
            $this->error($issues[count($issues) - 1]);

            if ($this->option('fix')) {
                $this->attemptDatabaseFix();
            }

            return 1;
        }

        // 1.5. فحص الهيكل الجديد
        $this->info('🏗️ فحص الهيكل المحسن...');
        $this->checkOptimizedStructure();

        // 2. فحص الجداول المطلوبة - الهيكل الجديد
        $this->info('📋 فحص الجداول المطلوبة (الهيكل المحسن)...');
        $requiredTables = [
            'users', 'ads', 'categories', 'interactions', 'notifications',
            'system_logs', 'settings', 'sessions', 'cache'
        ];
        
        foreach ($requiredTables as $table) {
            if (!Schema::hasTable($table)) {
                $issues[] = "❌ الجدول المطلوب غير موجود: {$table}";
                $this->error($issues[count($issues) - 1]);
            } else {
                $this->line("✅ الجدول موجود: {$table}");
            }
        }

        // 3. فحص الفهارس
        $this->info('🔍 فحص الفهارس...');
        $this->checkIndexes();

        // 4. فحص أداء الاستعلامات
        $this->info('⚡ فحص أداء الاستعلامات...');
        $this->checkQueryPerformance();

        // 5. فحص حجم قاعدة البيانات
        $this->info('💾 فحص حجم قاعدة البيانات...');
        $this->checkDatabaseSize();

        // 6. فحص الاتصالات النشطة
        $this->info('🔗 فحص الاتصالات النشطة...');
        $this->checkActiveConnections();

        // عرض النتائج
        $this->displayResults($issues, $warnings);
        
        return empty($issues) ? 0 : 1;
    }

    private function attemptDatabaseFix()
    {
        $this->info('🔧 محاولة إصلاح مشاكل قاعدة البيانات...');
        
        // التحقق من إعدادات .env
        $dbConnection = env('DB_CONNECTION');
        $dbHost = env('DB_HOST');
        $dbPort = env('DB_PORT');
        $dbDatabase = env('DB_DATABASE');
        
        $this->info("إعدادات قاعدة البيانات الحالية:");
        $this->line("- النوع: {$dbConnection}");
        $this->line("- المضيف: {$dbHost}");
        $this->line("- المنفذ: {$dbPort}");
        $this->line("- قاعدة البيانات: {$dbDatabase}");
        
        // اقتراحات للإصلاح
        $this->warn('💡 اقتراحات للإصلاح:');
        $this->line('1. تأكد من تشغيل خدمة MySQL');
        $this->line('2. تحقق من صحة إعدادات .env');
        $this->line('3. تأكد من وجود قاعدة البيانات');
        $this->line('4. تحقق من صلاحيات المستخدم');
    }

    private function checkIndexes()
    {
        try {
            $indexes = DB::select("
                SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME 
                FROM INFORMATION_SCHEMA.STATISTICS 
                WHERE TABLE_SCHEMA = ? 
                ORDER BY TABLE_NAME, INDEX_NAME
            ", [env('DB_DATABASE')]);
            
            $this->line("✅ تم العثور على " . count($indexes) . " فهرس");
        } catch (Exception $e) {
            $this->warn("⚠️ لا يمكن فحص الفهارس: " . $e->getMessage());
        }
    }

    private function checkQueryPerformance()
    {
        try {
            // فحص الاستعلامات البطيئة
            $slowQueries = DB::select("SHOW VARIABLES LIKE 'slow_query_log'");
            if (!empty($slowQueries)) {
                $this->line("✅ سجل الاستعلامات البطيئة: " . $slowQueries[0]->Value);
            }
        } catch (Exception $e) {
            $this->warn("⚠️ لا يمكن فحص أداء الاستعلامات: " . $e->getMessage());
        }
    }

    private function checkDatabaseSize()
    {
        try {
            $size = DB::select("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = ?
            ", [env('DB_DATABASE')]);
            
            if (!empty($size)) {
                $this->line("✅ حجم قاعدة البيانات: " . $size[0]->size_mb . " ميجابايت");
            }
        } catch (Exception $e) {
            $this->warn("⚠️ لا يمكن فحص حجم قاعدة البيانات: " . $e->getMessage());
        }
    }

    private function checkActiveConnections()
    {
        try {
            $connections = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            if (!empty($connections)) {
                $this->line("✅ الاتصالات النشطة: " . $connections[0]->Value);
            }
        } catch (Exception $e) {
            $this->warn("⚠️ لا يمكن فحص الاتصالات النشطة: " . $e->getMessage());
        }
    }

    private function displayResults($issues, $warnings)
    {
        $this->newLine();
        $this->info('📊 نتائج الفحص:');
        
        if (empty($issues) && empty($warnings)) {
            $this->info('🎉 قاعدة البيانات تعمل بشكل مثالي!');
        } else {
            if (!empty($issues)) {
                $this->error('❌ المشاكل المكتشفة:');
                foreach ($issues as $issue) {
                    $this->line("  - {$issue}");
                }
            }
            
            if (!empty($warnings)) {
                $this->warn('⚠️ التحذيرات:');
                foreach ($warnings as $warning) {
                    $this->line("  - {$warning}");
                }
            }
        }
    }

    /**
     * فحص الهيكل المحسن
     */
    private function checkOptimizedStructure()
    {
        $checks = [
            'users' => [
                'columns' => ['privacy_settings', 'preferred_currency', 'phone', 'whatsapp'],
                'description' => 'جدول المستخدمين الموحد'
            ],
            'ads' => [
                'columns' => ['images', 'contact_phone', 'contact_email', 'contact_whatsapp', 'rating_average'],
                'description' => 'جدول الإعلانات المحسن'
            ],
            'interactions' => [
                'columns' => ['type', 'data', 'user_id', 'ad_id'],
                'description' => 'جدول التفاعلات الموحد'
            ],
            'system_logs' => [
                'columns' => ['type', 'action', 'data', 'severity'],
                'description' => 'جدول السجلات الموحد'
            ],
            'settings' => [
                'columns' => ['key', 'value', 'type', 'group'],
                'description' => 'جدول الإعدادات الموحد'
            ]
        ];

        foreach ($checks as $table => $config) {
            if (Schema::hasTable($table)) {
                $this->line("✅ {$config['description']}: {$table}");

                // فحص الأعمدة المطلوبة
                foreach ($config['columns'] as $column) {
                    if (Schema::hasColumn($table, $column)) {
                        $this->line("  ✅ العمود موجود: {$column}");
                    } else {
                        $this->warn("  ⚠️ العمود مفقود: {$column}");
                    }
                }
            } else {
                $this->error("❌ الجدول مفقود: {$table}");
            }
        }

        // فحص الجداول القديمة التي يجب أن تكون محذوفة
        $oldTables = ['favorites', 'ratings', 'comments', 'reviews', 'search_logs', 'security_logs'];
        foreach ($oldTables as $table) {
            if (Schema::hasTable($table)) {
                $this->warn("⚠️ جدول قديم لا يزال موجود: {$table} (يجب حذفه)");
            } else {
                $this->line("✅ الجدول القديم محذوف بنجاح: {$table}");
            }
        }
    }
}
