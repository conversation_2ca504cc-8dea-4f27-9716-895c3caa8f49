@extends('layouts.app')

@section('title', __('Compare Ads'))

@section('content')
<div class="container-fluid py-4 compare-page">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-balance-scale text-primary me-2"></i>
                        {{ __('Compare Ads') }}
                    </h1>
                    <p class="text-muted">
                        {{ __('Compare :count selected ads side by side', ['count' => $ads->count()]) }}
                    </p>
                </div>
                
                <div class="compare-actions">
                    <button class="btn btn-outline-secondary me-2" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>
                        {{ __('Print') }}
                    </button>
                    <button class="btn btn-outline-primary me-2" onclick="shareComparison()">
                        <i class="fas fa-share-alt me-1"></i>
                        {{ __('Share') }}
                    </button>
                    <a href="{{ route('ads.index') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        {{ __('Back to Ads') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المقارنة -->
    <div class="comparison-table-container">
        <div class="table-responsive">
            <table class="table table-bordered comparison-table">
                <!-- رأس الجدول - صور الإعلانات -->
                <thead>
                    <tr class="ads-header">
                        <th class="feature-column">{{ __('Feature') }}</th>
                        @foreach($ads as $ad)
                            <th class="ad-column">
                                <div class="ad-header-card">
                                    <!-- صورة الإعلان -->
                                    <div class="ad-image-container">
                                        @if($ad->image_url)
                                            <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}" class="ad-comparison-image">
                                        @else
                                            <div class="ad-placeholder-image">
                                                <i class="{{ $ad->category->icon ?? 'fas fa-image' }} fa-3x text-muted"></i>
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <!-- عنوان الإعلان -->
                                    <div class="ad-header-info">
                                        <h5 class="ad-title">
                                            <a href="{{ route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]) }}" 
                                               target="_blank" class="text-decoration-none">
                                                {{ $ad->title }}
                                            </a>
                                        </h5>
                                        <small class="text-muted">{{ $ad->category->name ?? __('Uncategorized') }}</small>
                                    </div>
                                    
                                    <!-- أزرار الإجراءات -->
                                    <div class="ad-header-actions mt-2">
                                        <a href="{{ route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]) }}" 
                                           class="btn btn-primary btn-sm" target="_blank">
                                            <i class="fas fa-eye me-1"></i>
                                            {{ __('View') }}
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm ms-1" 
                                                onclick="removeFromComparison({{ $ad->id }})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </th>
                        @endforeach
                    </tr>
                </thead>

                <tbody>
                    <!-- المعلومات الأساسية -->
                    <tr class="section-header">
                        <td colspan="{{ $ads->count() + 1 }}" class="text-center bg-light fw-bold">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ __('Basic Information') }}
                        </td>
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Location') }}</td>
                        @foreach($comparisonData['basic_info'] as $info)
                            <td>{{ $info['location'] }}</td>
                        @endforeach
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Published Date') }}</td>
                        @foreach($comparisonData['basic_info'] as $info)
                            <td>{{ $info['published_date'] }}</td>
                        @endforeach
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Views') }}</td>
                        @foreach($comparisonData['basic_info'] as $info)
                            <td>
                                <span class="badge bg-info">{{ number_format($info['views']) }}</span>
                            </td>
                        @endforeach
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Expires At') }}</td>
                        @foreach($comparisonData['basic_info'] as $info)
                            <td>{{ $info['expires_at'] }}</td>
                        @endforeach
                    </tr>

                    <!-- الأسعار -->
                    <tr class="section-header">
                        <td colspan="{{ $ads->count() + 1 }}" class="text-center bg-light fw-bold">
                            <i class="fas fa-dollar-sign me-2"></i>
                            {{ __('Pricing') }}
                        </td>
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Price') }}</td>
                        @foreach($comparisonData['pricing'] as $pricing)
                            <td>
                                @if($pricing['is_free'])
                                    <span class="badge bg-success">{{ __('Free') }}</span>
                                @elseif($pricing['price'])
                                    <div class="price-display">
                                        <span class="current-price fw-bold text-primary">
                                            {{ number_format($pricing['price'], 2) }} {{ $pricing['currency'] }}
                                        </span>
                                        @if($pricing['original_price'] && $pricing['original_price'] > $pricing['price'])
                                            <br>
                                            <small class="text-muted text-decoration-line-through">
                                                {{ number_format($pricing['original_price'], 2) }} {{ $pricing['currency'] }}
                                            </small>
                                            <span class="badge bg-danger ms-1">
                                                -{{ $pricing['discount_percentage'] }}%
                                            </span>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-muted">{{ __('Not specified') }}</span>
                                @endif
                            </td>
                        @endforeach
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Negotiable') }}</td>
                        @foreach($comparisonData['pricing'] as $pricing)
                            <td>
                                @if($pricing['is_negotiable'])
                                    <span class="badge bg-warning">{{ __('Yes') }}</span>
                                @else
                                    <span class="badge bg-secondary">{{ __('No') }}</span>
                                @endif
                            </td>
                        @endforeach
                    </tr>

                    <!-- التقييمات -->
                    <tr class="section-header">
                        <td colspan="{{ $ads->count() + 1 }}" class="text-center bg-light fw-bold">
                            <i class="fas fa-star me-2"></i>
                            {{ __('Ratings') }}
                        </td>
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Average Rating') }}</td>
                        @foreach($comparisonData['ratings'] as $rating)
                            <td>
                                @if($rating['ratings_count'] > 0)
                                    <div class="rating-display">
                                        <div class="stars-small">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= floor($rating['average_rating']))
                                                    <i class="fas fa-star text-warning"></i>
                                                @elseif($i - 0.5 <= $rating['average_rating'])
                                                    <i class="fas fa-star-half-alt text-warning"></i>
                                                @else
                                                    <i class="far fa-star text-muted"></i>
                                                @endif
                                            @endfor
                                        </div>
                                        <small class="text-muted">
                                            {{ number_format($rating['average_rating'], 1) }} 
                                            ({{ $rating['ratings_count'] }} {{ __('reviews') }})
                                        </small>
                                    </div>
                                @else
                                    <span class="text-muted">{{ __('No ratings yet') }}</span>
                                @endif
                            </td>
                        @endforeach
                    </tr>

                    <!-- الميزات -->
                    <tr class="section-header">
                        <td colspan="{{ $ads->count() + 1 }}" class="text-center bg-light fw-bold">
                            <i class="fas fa-list me-2"></i>
                            {{ __('Features') }}
                        </td>
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Featured Ad') }}</td>
                        @foreach($comparisonData['features'] as $features)
                            <td>
                                @if($features['is_featured'])
                                    <span class="badge bg-warning">
                                        <i class="fas fa-star me-1"></i>
                                        {{ __('Featured') }}
                                    </span>
                                @else
                                    <span class="badge bg-secondary">{{ __('Regular') }}</span>
                                @endif
                            </td>
                        @endforeach
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Has Images') }}</td>
                        @foreach($comparisonData['features'] as $features)
                            <td>
                                @if($features['has_images'])
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        {{ __('Yes') }}
                                    </span>
                                @else
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>
                                        {{ __('No') }}
                                    </span>
                                @endif
                            </td>
                        @endforeach
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Description Length') }}</td>
                        @foreach($comparisonData['features'] as $features)
                            <td>
                                <span class="badge bg-info">
                                    {{ $features['description_length'] }} {{ __('characters') }}
                                </span>
                            </td>
                        @endforeach
                    </tr>

                    <!-- معلومات التواصل -->
                    <tr class="section-header">
                        <td colspan="{{ $ads->count() + 1 }}" class="text-center bg-light fw-bold">
                            <i class="fas fa-address-book me-2"></i>
                            {{ __('Contact Information') }}
                        </td>
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Seller') }}</td>
                        @foreach($comparisonData['contact'] as $contact)
                            <td>
                                <div class="seller-info">
                                    <strong>{{ $contact['user_name'] }}</strong>
                                    @if($contact['user_joined'])
                                        <br>
                                        <small class="text-muted">
                                            {{ __('Member since') }} {{ $contact['user_joined']->format('Y') }}
                                        </small>
                                    @endif
                                </div>
                            </td>
                        @endforeach
                    </tr>
                    
                    <tr>
                        <td class="feature-name">{{ __('Contact Available') }}</td>
                        @foreach($comparisonData['contact'] as $contact)
                            <td>
                                @if($contact['phone'] || $contact['email'])
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        {{ __('Available') }}
                                    </span>
                                @else
                                    <span class="badge bg-warning">
                                        <i class="fas fa-exclamation me-1"></i>
                                        {{ __('Limited') }}
                                    </span>
                                @endif
                            </td>
                        @endforeach
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- ملاحظات المقارنة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>{{ __('Comparison Notes') }}</h6>
                <ul class="mb-0">
                    <li>{{ __('This comparison is based on publicly available information') }}</li>
                    <li>{{ __('Prices and availability may change without notice') }}</li>
                    <li>{{ __('Contact sellers directly for the most up-to-date information') }}</li>
                    <li>{{ __('Ratings are based on user reviews and may not reflect current quality') }}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* تنسيق صفحة المقارنة */
.compare-page {
    background: #f8f9fa;
    min-height: 100vh;
}

.comparison-table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.comparison-table {
    margin: 0;
    border: none;
}

.comparison-table th,
.comparison-table td {
    border: 1px solid #e9ecef;
    vertical-align: middle;
    padding: 1rem;
}

.feature-column {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    font-weight: 600;
    min-width: 200px;
    position: sticky;
    left: 0;
    z-index: 10;
}

.ad-column {
    min-width: 250px;
    background: #f8f9fa;
}

.ad-header-card {
    text-align: center;
    padding: 1rem;
}

.ad-image-container {
    margin-bottom: 1rem;
}

.ad-comparison-image {
    width: 120px;
    height: 90px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.ad-placeholder-image {
    width: 120px;
    height: 90px;
    background: #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.ad-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.ad-title a {
    color: #007bff;
}

.ad-title a:hover {
    color: #0056b3;
}

.section-header td {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-size: 1.1rem;
    padding: 1.5rem 1rem;
}

.feature-name {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    left: 0;
    z-index: 5;
}

.price-display {
    text-align: center;
}

.current-price {
    font-size: 1.1rem;
}

.rating-display {
    text-align: center;
}

.stars-small i {
    font-size: 0.9rem;
    margin-right: 2px;
}

.seller-info {
    text-align: center;
}

/* تحسينات responsive */
@media (max-width: 768px) {
    .comparison-table-container {
        border-radius: 0;
        margin: 0 -15px;
    }

    .ad-column {
        min-width: 200px;
    }

    .ad-comparison-image {
        width: 80px;
        height: 60px;
    }

    .ad-placeholder-image {
        width: 80px;
        height: 60px;
    }

    .comparison-table th,
    .comparison-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }

    .compare-actions {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .compare-actions .btn {
        font-size: 0.8rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .compare-actions,
    .ad-header-actions {
        display: none !important;
    }

    .comparison-table {
        font-size: 0.8rem;
    }

    .ad-comparison-image {
        width: 60px;
        height: 45px;
    }
}

/* تأثيرات hover */
.comparison-table tbody tr:hover {
    background: rgba(0, 123, 255, 0.05);
}

.ad-header-actions .btn {
    transition: all 0.3s ease;
}

.ad-header-actions .btn:hover {
    transform: translateY(-2px);
}

/* تحسين الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
}

.badge i {
    font-size: 0.7rem;
}
</style>
@endpush

@push('scripts')
<script>
// إزالة إعلان من المقارنة
function removeFromComparison(adId) {
    if (confirm('{{ __("Remove this ad from comparison?") }}')) {
        // إزالة من localStorage
        if (typeof compareSystem !== 'undefined') {
            compareSystem.remove(adId);
        }

        // إعادة تحميل الصفحة
        const currentUrl = new URL(window.location);
        const ids = currentUrl.searchParams.get('ids');
        if (ids) {
            const idsArray = ids.split(',').filter(id => id != adId);
            if (idsArray.length > 0) {
                currentUrl.searchParams.set('ids', idsArray.join(','));
                window.location.href = currentUrl.toString();
            } else {
                window.location.href = '{{ route("ads.index") }}';
            }
        }
    }
}

// مشاركة المقارنة
function shareComparison() {
    const url = window.location.href;
    const title = '{{ __("Compare Ads") }} - {{ config("app.name") }}';

    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        navigator.clipboard.writeText(url).then(function() {
            if (typeof showToast === 'function') {
                showToast('{{ __("Comparison link copied to clipboard") }}', 'success');
            } else {
                alert('{{ __("Comparison link copied to clipboard") }}');
            }
        }).catch(function() {
            // فتح نافذة مشاركة بديلة
            window.open(`https://wa.me/?text=${encodeURIComponent(title + ' - ' + url)}`, '_blank');
        });
    }
}

// تحسين تجربة التمرير الأفقي
document.addEventListener('DOMContentLoaded', function() {
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
        // إضافة مؤشرات التمرير
        let isScrolling = false;

        tableContainer.addEventListener('scroll', function() {
            if (!isScrolling) {
                isScrolling = true;
                setTimeout(() => {
                    isScrolling = false;
                }, 100);
            }
        });
    }
});
</script>
@endpush
