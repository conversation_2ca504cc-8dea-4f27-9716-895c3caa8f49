
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'ad',
    'config' => [],
    'showIcons' => true,        // إظهار الأيقونات
    'showDetails' => false,     // إظهار التفاصيل النصية
    'showReveal' => false,      // إظهار نظام الكشف المتدرج
    'iconStyle' => 'circular'   // نمط الأيقونات: circular, square, minimal
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'ad',
    'config' => [],
    'showIcons' => true,        // إظهار الأيقونات
    'showDetails' => false,     // إظهار التفاصيل النصية
    'showReveal' => false,      // إظهار نظام الكشف المتدرج
    'iconStyle' => 'circular'   // نمط الأيقونات: circular, square, minimal
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    use App\Services\EncryptionService;
    
    // الحصول على معلومات التواصل من الحقول المشفرة الجديدة
    $currentUser = auth()->user();
    $adOwner = $ad->user;

    // فك تشفير معلومات التواصل من الإعلان
    $contactPhone = null;
    $contactEmail = null;
    $contactWhatsapp = null;
    
    try {
        $contactPhone = $ad->contact_phone ? EncryptionService::decryptText($ad->contact_phone) : null;
        $contactEmail = $ad->contact_email ? EncryptionService::decryptText($ad->contact_email) : null;
        $contactWhatsapp = $ad->contact_whatsapp ? EncryptionService::decryptText($ad->contact_whatsapp) : null;
    } catch (\Exception $e) {
        // في حالة فشل فك التشفير، استخدم البيانات من المستخدم
        try {
            $contactPhone = $adOwner->phone ? EncryptionService::decryptText($adOwner->phone) : null;
            $contactWhatsapp = $adOwner->whatsapp ? EncryptionService::decryptText($adOwner->whatsapp) : null;
        } catch (\Exception $e2) {
            // في حالة فشل فك التشفير تماماً، لا تعرض معلومات التواصل
            $contactPhone = null;
            $contactWhatsapp = null;
        }
        $contactEmail = $adOwner->email; // البريد الإلكتروني غير مشفر في جدول المستخدمين
    }

    // التحقق من إعدادات الخصوصية - استخدام الـ accessor الجديد
    $privacySettings = $adOwner->getPrivacySettingsAttribute();
    $showPhone = $privacySettings['show_phone'] ?? true;
    $showEmail = $privacySettings['show_email'] ?? true;
    $showWhatsapp = $privacySettings['show_whatsapp'] ?? true;
    $allowContactReveal = $privacySettings['allow_contact_reveal'] ?? true;

    // تطبيق إعدادات الخصوصية
    $hasPhone = $contactPhone && $showPhone;
    $hasWhatsApp = $contactWhatsapp && $showWhatsapp;
    $hasEmail = $contactEmail && $showEmail;

    // تحضير أرقام الهاتف والواتساب
    $phoneNumber = $hasPhone ? $contactPhone : '';
    $whatsappNumber = $hasWhatsApp ? $contactWhatsapp : ($hasPhone ? $phoneNumber : '');
    $emailAddress = $hasEmail ? $contactEmail : '';

    // تنظيف رقم الواتساب (إزالة المسافات والرموز)
    if ($whatsappNumber) {
        $whatsappNumber = preg_replace('/[^0-9+]/', '', $whatsappNumber);
        
        // إضافة رمز الدولة إذا لم يكن موجود (افتراضي: اليمن +967)
        if (!str_starts_with($whatsappNumber, '+')) {
            if (str_starts_with($whatsappNumber, '967')) {
                $whatsappNumber = '+' . $whatsappNumber;
            } elseif (str_starts_with($whatsappNumber, '0')) {
                $whatsappNumber = '+967' . substr($whatsappNumber, 1);
            } else {
                $whatsappNumber = '+967' . $whatsappNumber;
            }
        }
    }

    // التحقق من وجود أي معلومات تواصل
    $hasAnyContact = $hasPhone || $hasWhatsApp || $hasEmail;

    // إعدادات العرض
    $defaultConfig = [
        'showPhone' => true,
        'showWhatsApp' => true,
        'showEmail' => true,
        'showCopyButtons' => true,
        'showLabels' => false,
        'compactMode' => false,
        'iconSize' => 'md',
        'buttonStyle' => 'outline',
        'alignment' => 'start'
    ];
    
    $config = array_merge($defaultConfig, $config);
?>

<?php if($hasAnyContact): ?>
    <div class="contact-section <?php echo e($config['compactMode'] ? 'compact' : ''); ?>" 
         data-ad-id="<?php echo e($ad->id); ?>"
         data-contact-reveal="<?php echo e($allowContactReveal ? 'true' : 'false'); ?>">
        
        <?php if($showIcons): ?>
            <div class="contact-icons d-flex align-items-center justify-content-<?php echo e($config['alignment']); ?> gap-2">
                
                
                <?php if($hasPhone && $config['showPhone']): ?>
                    <div class="contact-item phone-contact" data-contact-type="phone">
                        <?php if($showReveal && !$currentUser): ?>
                            
                            <button class="btn btn-<?php echo e($config['buttonStyle']); ?>-primary btn-sm contact-reveal-btn"
                                    data-contact-type="phone"
                                    data-ad-id="<?php echo e($ad->id); ?>"
                                    title="<?php echo e(__('Click to reveal phone number')); ?>">
                                <i class="fas fa-phone"></i>
                                <?php if($config['showLabels']): ?>
                                    <span class="ms-1"><?php echo e(__('Phone')); ?></span>
                                <?php endif; ?>
                            </button>
                        <?php else: ?>
                            
                            <a href="tel:<?php echo e($phoneNumber); ?>" 
                               class="btn btn-<?php echo e($config['buttonStyle']); ?>-success btn-sm"
                               title="<?php echo e(__('Call')); ?>: <?php echo e(EncryptionService::maskPhone($phoneNumber)); ?>">
                                <i class="fas fa-phone"></i>
                                <?php if($config['showLabels']): ?>
                                    <span class="ms-1"><?php echo e(__('Call')); ?></span>
                                <?php endif; ?>
                            </a>
                            
                            <?php if($config['showCopyButtons']): ?>
                                <button class="btn btn-outline-secondary btn-sm ms-1 copy-contact-btn"
                                        data-contact="<?php echo e($phoneNumber); ?>"
                                        data-contact-type="phone"
                                        title="<?php echo e(__('Copy phone number')); ?>">
                                    <i class="fas fa-copy"></i>
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                
                <?php if($hasWhatsApp && $config['showWhatsApp']): ?>
                    <div class="contact-item whatsapp-contact" data-contact-type="whatsapp">
                        <?php if($showReveal && !$currentUser): ?>
                            
                            <button class="btn btn-<?php echo e($config['buttonStyle']); ?>-success btn-sm contact-reveal-btn"
                                    data-contact-type="whatsapp"
                                    data-ad-id="<?php echo e($ad->id); ?>"
                                    title="<?php echo e(__('Click to reveal WhatsApp number')); ?>">
                                <i class="fab fa-whatsapp"></i>
                                <?php if($config['showLabels']): ?>
                                    <span class="ms-1"><?php echo e(__('WhatsApp')); ?></span>
                                <?php endif; ?>
                            </button>
                        <?php else: ?>
                            
                            <a href="https://wa.me/<?php echo e($whatsappNumber); ?>?text=<?php echo e(urlencode(__('Hello, I am interested in your ad: ') . $ad->title)); ?>" 
                               target="_blank"
                               class="btn btn-<?php echo e($config['buttonStyle']); ?>-success btn-sm"
                               title="<?php echo e(__('WhatsApp')); ?>: <?php echo e(EncryptionService::maskPhone($whatsappNumber)); ?>">
                                <i class="fab fa-whatsapp"></i>
                                <?php if($config['showLabels']): ?>
                                    <span class="ms-1"><?php echo e(__('WhatsApp')); ?></span>
                                <?php endif; ?>
                            </a>
                            
                            <?php if($config['showCopyButtons']): ?>
                                <button class="btn btn-outline-secondary btn-sm ms-1 copy-contact-btn"
                                        data-contact="<?php echo e($whatsappNumber); ?>"
                                        data-contact-type="whatsapp"
                                        title="<?php echo e(__('Copy WhatsApp number')); ?>">
                                    <i class="fas fa-copy"></i>
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                
                <?php if($hasEmail && $config['showEmail']): ?>
                    <div class="contact-item email-contact" data-contact-type="email">
                        <?php if($showReveal && !$currentUser): ?>
                            
                            <button class="btn btn-<?php echo e($config['buttonStyle']); ?>-info btn-sm contact-reveal-btn"
                                    data-contact-type="email"
                                    data-ad-id="<?php echo e($ad->id); ?>"
                                    title="<?php echo e(__('Click to reveal email address')); ?>">
                                <i class="fas fa-envelope"></i>
                                <?php if($config['showLabels']): ?>
                                    <span class="ms-1"><?php echo e(__('Email')); ?></span>
                                <?php endif; ?>
                            </button>
                        <?php else: ?>
                            
                            <a href="mailto:<?php echo e($emailAddress); ?>?subject=<?php echo e(urlencode(__('Inquiry about: ') . $ad->title)); ?>" 
                               class="btn btn-<?php echo e($config['buttonStyle']); ?>-info btn-sm"
                               title="<?php echo e(__('Email')); ?>: <?php echo e(EncryptionService::maskEmail($emailAddress)); ?>">
                                <i class="fas fa-envelope"></i>
                                <?php if($config['showLabels']): ?>
                                    <span class="ms-1"><?php echo e(__('Email')); ?></span>
                                <?php endif; ?>
                            </a>
                            
                            <?php if($config['showCopyButtons']): ?>
                                <button class="btn btn-outline-secondary btn-sm ms-1 copy-contact-btn"
                                        data-contact="<?php echo e($emailAddress); ?>"
                                        data-contact-type="email"
                                        title="<?php echo e(__('Copy email address')); ?>">
                                    <i class="fas fa-copy"></i>
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if($showDetails): ?>
            <div class="contact-details mt-2">
                <div class="contact-details-content">
                    <?php if($hasPhone && $config['showPhone']): ?>
                        <div class="contact-detail-item">
                            <strong><?php echo e(__('Phone')); ?>:</strong>
                            <span class="contact-value"><?php echo e(EncryptionService::maskPhone($phoneNumber)); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($hasWhatsApp && $config['showWhatsApp']): ?>
                        <div class="contact-detail-item">
                            <strong><?php echo e(__('WhatsApp')); ?>:</strong>
                            <span class="contact-value"><?php echo e(EncryptionService::maskPhone($whatsappNumber)); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($hasEmail && $config['showEmail']): ?>
                        <div class="contact-detail-item">
                            <strong><?php echo e(__('Email')); ?>:</strong>
                            <span class="contact-value"><?php echo e(EncryptionService::maskEmail($emailAddress)); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php else: ?>
    
    <div class="no-contact-info text-muted">
        <small>
            <i class="fas fa-info-circle me-1"></i>
            <?php echo e(__('Contact information not available')); ?>

        </small>
    </div>
<?php endif; ?>


<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // نسخ معلومات التواصل
    document.querySelectorAll('.copy-contact-btn').forEach(button => {
        button.addEventListener('click', function() {
            const contact = this.dataset.contact;
            const type = this.dataset.contactType;
            
            navigator.clipboard.writeText(contact).then(() => {
                // إظهار رسالة نجاح
                this.innerHTML = '<i class="fas fa-check"></i>';
                this.classList.add('btn-success');
                
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-copy"></i>';
                    this.classList.remove('btn-success');
                }, 2000);
            });
        });
    });

    // كشف معلومات التواصل للزوار
    document.querySelectorAll('.contact-reveal-btn').forEach(button => {
        button.addEventListener('click', function() {
            const adId = this.dataset.adId;
            const contactType = this.dataset.contactType;
            
            // تسجيل كشف التواصل
            fetch(`/ads/${adId}/reveal-contact`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    contact_type: contactType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // استبدال الزر بمعلومات التواصل الفعلية
                    this.outerHTML = data.contact_html;
                }
            });
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/partials/contact.blade.php ENDPATH**/ ?>