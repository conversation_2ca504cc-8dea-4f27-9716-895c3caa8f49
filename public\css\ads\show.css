/*
 * ملف CSS محسن لصفحة تفاصيل الإعلان
 * تم إنشاؤه كجزء من خطة تحسين تجربة المستخدم العامة
 * يحتوي على تحسينات للاستجابة والأداء والتفاعل
 */

/* ===== متغيرات الألوان والقياسات ===== */
:root {
    --ad-primary: #3b82f6;
    --ad-secondary: #64748b;
    --ad-success: #10b981;
    --ad-warning: #f59e0b;
    --ad-danger: #ef4444;
    --ad-info: #06b6d4;
    --ad-light: #f8fafc;
    --ad-dark: #1e293b;
    --ad-border: #e2e8f0;
    --ad-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --ad-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --ad-radius: 12px;
    --ad-radius-lg: 16px;
    --ad-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== تحسينات عامة للصفحة ===== */
.ad-details-page {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.ad-details-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* ===== تحسينات بطاقات المحتوى ===== */
.content-card {
    background: white;
    border-radius: var(--ad-radius-lg);
    box-shadow: var(--ad-shadow);
    border: 1px solid var(--ad-border);
    transition: var(--ad-transition);
    overflow: hidden;
    margin-bottom: 2rem;
}

.content-card:hover {
    box-shadow: var(--ad-shadow-lg);
    transform: translateY(-2px);
}

.content-card-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--ad-border);
    background: linear-gradient(135deg, var(--ad-light) 0%, white 100%);
}

.content-card-body {
    padding: 2rem;
}

.content-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--ad-dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.content-card-title i {
    color: var(--ad-primary);
    font-size: 1.1rem;
}

/* ===== تحسينات الصور ===== */
.ad-image-gallery {
    position: relative;
    border-radius: var(--ad-radius);
    overflow: hidden;
    box-shadow: var(--ad-shadow);
    margin-bottom: 2rem;
}

.ad-main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--ad-transition);
}

.ad-main-image:hover {
    transform: scale(1.02);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 2rem;
    transform: translateY(100%);
    transition: var(--ad-transition);
}

.ad-image-gallery:hover .image-overlay {
    transform: translateY(0);
}

.image-thumbnails {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    overflow-x: auto;
    padding: 0.5rem 0;
}

.thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--ad-transition);
    border: 2px solid transparent;
}

.thumbnail:hover,
.thumbnail.active {
    border-color: var(--ad-primary);
    transform: scale(1.05);
}

/* ===== تحسينات النصوص والمحتوى ===== */
.ad-title {
    font-size: 2rem;
    font-weight: 800;
    color: var(--ad-dark);
    line-height: 1.2;
    margin-bottom: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ad-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--ad-secondary);
    margin-bottom: 2rem;
}

.ad-description p {
    margin-bottom: 1rem;
}

.ad-description p:last-child {
    margin-bottom: 0;
}

/* ===== تحسينات الأزرار والتفاعل ===== */
.action-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--ad-radius);
    font-weight: 600;
    text-decoration: none;
    transition: var(--ad-transition);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-button:hover::before {
    left: 100%;
}

.btn-primary-custom {
    background: linear-gradient(135deg, var(--ad-primary), #2563eb);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
}

.btn-primary-custom:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.5);
}

.btn-success-custom {
    background: linear-gradient(135deg, var(--ad-success), #059669);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
}

.btn-success-custom:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.5);
}

.btn-outline-custom {
    background: transparent;
    border: 2px solid var(--ad-border);
    color: var(--ad-secondary);
}

.btn-outline-custom:hover {
    background: var(--ad-light);
    border-color: var(--ad-primary);
    color: var(--ad-primary);
}

/* ===== تحسينات الشريط الجانبي ===== */
.sidebar-sticky {
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

.sidebar-card {
    background: white;
    border-radius: var(--ad-radius);
    box-shadow: var(--ad-shadow);
    border: 1px solid var(--ad-border);
    margin-bottom: 1.5rem;
    transition: var(--ad-transition);
}

.sidebar-card:hover {
    box-shadow: var(--ad-shadow-lg);
}

.sidebar-card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--ad-border);
    background: linear-gradient(135deg, var(--ad-light) 0%, white 100%);
}

.sidebar-card-body {
    padding: 1.5rem;
}

.sidebar-card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--ad-dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* ===== تحسينات المعلومات والإحصائيات ===== */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--ad-light);
    border-radius: var(--ad-radius);
    border: 1px solid var(--ad-border);
    transition: var(--ad-transition);
}

.info-item:hover {
    background: white;
    box-shadow: var(--ad-shadow);
    transform: translateY(-1px);
}

.info-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    color: var(--ad-primary);
    font-size: 1.1rem;
    box-shadow: var(--ad-shadow);
}

.info-content {
    flex: 1;
}

.info-label {
    font-size: 0.9rem;
    color: var(--ad-secondary);
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--ad-dark);
}

/* ===== تحسينات التحميل والأداء ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== تحسينات الاستجابة للأجهزة المحمولة ===== */
@media (max-width: 1200px) {
    .ad-details-container {
        padding: 0 0.75rem;
    }
    
    .content-card-body {
        padding: 1.5rem;
    }
    
    .sidebar-sticky {
        position: static;
        max-height: none;
    }
}

@media (max-width: 768px) {
    .ad-details-page {
        padding: 1rem 0;
    }
    
    .ad-details-container {
        padding: 0 0.5rem;
    }
    
    .ad-title {
        font-size: 1.5rem;
    }
    
    .ad-main-image {
        height: 250px;
    }
    
    .content-card-header,
    .content-card-body {
        padding: 1rem;
    }
    
    .sidebar-card-header,
    .sidebar-card-body {
        padding: 1rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .action-button {
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }
    
    .image-overlay {
        position: static;
        transform: none;
        background: rgba(0, 0, 0, 0.8);
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .ad-title {
        font-size: 1.25rem;
    }
    
    .ad-main-image {
        height: 200px;
    }
    
    .content-card-header,
    .content-card-body,
    .sidebar-card-header,
    .sidebar-card-body {
        padding: 0.75rem;
    }
    
    .info-item {
        padding: 0.75rem;
    }
    
    .action-button {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .ad-details-page {
        background: white;
        padding: 0;
    }
    
    .content-card,
    .sidebar-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
    
    .action-button,
    .image-overlay {
        display: none !important;
    }
    
    .ad-main-image {
        height: auto;
        max-height: 300px;
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid var(--ad-primary);
    outline-offset: 2px;
}

/* ===== تحسينات الوضع المظلم (معطل مؤقتاً) ===== */
/* تم إلغاء تفعيل الوضع المظلم مؤقتاً - سيتم تفعيله لاحقاً عند الطلب */
/*
@media (prefers-color-scheme: dark) {
    :root {
        --ad-light: #1e293b;
        --ad-dark: #f1f5f9;
        --ad-border: #334155;
        --ad-secondary: #94a3b8;
    }

    .ad-details-page {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    }

    .content-card,
    .sidebar-card {
        background: #1e293b;
        color: #f1f5f9;
    }

    .content-card-header,
    .sidebar-card-header {
        background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    }
}
*/
