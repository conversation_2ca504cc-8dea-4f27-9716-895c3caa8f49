/*
 * ===== أنماط خاصة بالصفحات =====
 * ملف CSS مخصص لجمع الأنماط المضمنة من ملفات Blade
 * تم إنشاؤه كجزء من خطة تنظيف CSS المضمن وتحسين الصيانة
 * 
 * الهدف: فصل CSS عن HTML لتحسين الأداء وسهولة الصيانة
 * التاريخ: تم الإنشاء أثناء تنفيذ خطة إصلاح التعارضات
 */

/* ===== أنماط صفحة المفضلة ===== */

/* 
 * تحسينات إضافية لصفحة المفضلة
 * تطبق على البطاقات في صفحة المفضلة لتحسين التفاعل
 */
.favorites-page .favorite-card {
    transition: all 0.3s ease;  /* انتقال سلس لجميع التأثيرات */
}

/* 
 * أيقونة القلب الفارغة في رسالة "لا توجد مفضلة"
 * تظهر عندما لا يكون لدى المستخدم أي إعلانات مفضلة
 */
.empty-favorites-icon {
    font-size: 4rem;           /* حجم كبير للأيقونة */
    opacity: 0.3;             /* شفافية خفيفة للمظهر الباهت */
    color: #6c757d;           /* لون رمادي متوسط */
}

/* ===== أنماط صفحة البحث ===== */

/* 
 * الفلاتر المتقدمة في صفحة البحث
 * مخفية افتراضياً وتظهر عند النقر على زر "فلاتر متقدمة"
 */
.advanced-filters-hidden {
    display: none;            /* مخفية افتراضياً */
}

/* 
 * حاوي الفلاتر المتقدمة
 * يحتوي على خلفية مميزة وحدود دائرية
 */
.advanced-filters {
    background: #f8f9fa;      /* خلفية رمادية فاتحة */
    border-radius: 10px;      /* زوايا دائرية */
    padding: 1.5rem;          /* حشو داخلي مريح */
    margin-top: 1rem;         /* مسافة علوية */
    border: 1px solid #e9ecef; /* حدود رمادية خفيفة */
}

/* 
 * عنوان الفلاتر المتقدمة
 * يحتوي على أيقونة وتنسيق خاص
 */
.advanced-filters h6 {
    color: #495057;           /* لون رمادي داكن */
    font-weight: 600;         /* خط عريض */
    margin-bottom: 1rem;      /* مسافة سفلية */
}

/* 
 * أيقونة الفلتر في عنوان الفلاتر المتقدمة
 */
.advanced-filters .fa-filter {
    color: #007bff;           /* لون أزرق أساسي */
    margin-left: 0.5rem;      /* مسافة يسارية */
}

/* ===== أنماط عامة للصفحات ===== */

/* 
 * تحسينات للنماذج والعناصر التفاعلية
 * تطبق على جميع الصفحات لضمان التناسق
 */
.form-select-auto-width {
    width: auto;              /* عرض تلقائي حسب المحتوى */
    min-width: 120px;         /* حد أدنى للعرض */
}

/* 
 * تحسينات للأزرار الصغيرة
 * تستخدم في أماكن مختلفة عبر الموقع
 */
.btn-sm-auto {
    padding: 0.25rem 0.75rem; /* حشو مناسب للأزرار الصغيرة */
    font-size: 0.875rem;      /* حجم خط مناسب */
}

/* ===== تحسينات الاستجابة ===== */

/* 
 * تحسينات للهواتف المحمولة
 * نقطة الكسر موحدة: 575.98px
 */
@media (max-width: 575.98px) {
    /* تقليل حجم الأيقونة الفارغة على الهواتف */
    .empty-favorites-icon {
        font-size: 3rem;       /* حجم أصغر للهواتف */
    }
    
    /* تقليل الحشو في الفلاتر المتقدمة */
    .advanced-filters {
        padding: 1rem;         /* حشو أقل للهواتف */
        margin-top: 0.75rem;   /* مسافة علوية أقل */
    }
    
    /* تحسين عرض النماذج على الهواتف */
    .form-select-auto-width {
        width: 100%;           /* عرض كامل على الهواتف */
        min-width: auto;       /* إلغاء الحد الأدنى */
    }
}

/* 
 * تحسينات للأجهزة اللوحية
 * نقطة الكسر موحدة: 767.98px
 */
@media (max-width: 767.98px) {
    /* تحسين الفلاتر المتقدمة للأجهزة اللوحية */
    .advanced-filters {
        padding: 1.25rem;      /* حشو متوسط للأجهزة اللوحية */
    }
    
    /* تحسين الأزرار للأجهزة اللوحية */
    .btn-sm-auto {
        padding: 0.375rem 1rem; /* حشو أكبر قليلاً للمس الأسهل */
    }
}

/* ===== تحسينات إمكانية الوصول ===== */

/* 
 * تحسينات للمستخدمين الذين يفضلون تقليل الحركة
 * يطبق عندما يكون لدى المستخدم إعداد "تقليل الحركة" مفعل
 */
@media (prefers-reduced-motion: reduce) {
    .favorites-page .favorite-card {
        transition: none;      /* إلغاء الانتقالات للمستخدمين الحساسين للحركة */
    }
}

/*
 * تحسينات للوضع المظلم (معطل مؤقتاً)
 * تم إلغاء تفعيل الوضع المظلم مؤقتاً - سيتم تفعيله لاحقاً عند الطلب
 */
/*
@media (prefers-color-scheme: dark) {
    .advanced-filters {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .advanced-filters h6 {
        color: #e2e8f0;
    }

    .empty-favorites-icon {
        color: #a0aec0;
    }
}
*/

/* ===== تحسينات الطباعة ===== */

/* 
 * إخفاء العناصر غير الضرورية عند الطباعة
 */
@media print {
    .advanced-filters,
    .empty-favorites-icon {
        display: none;         /* إخفاء عند الطباعة */
    }
}
