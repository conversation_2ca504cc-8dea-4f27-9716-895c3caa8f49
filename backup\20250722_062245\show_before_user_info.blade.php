@extends('layouts.app')

@section('title', $ad->title . ' - ' . __('site_name'))
@section('description', Str::limit($ad->description, 160))

@section('content')
<!-- القسم الرئيسي لتفاصيل الإعلان -->
<section class="ad-details-header py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="ad-breadcrumb text-white mb-3">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb bg-transparent p-0 m-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('home') }}" class="text-white-50">{{ __('Home') }}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('categories.index') }}" class="text-white-50">{{ __('Categories') }}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('ads.category', $ad->category->slug) }}" class="text-white-50">{{ $ad->category->name }}</a>
                            </li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ Str::limit($ad->title, 30) }}</li>
                        </ol>
                    </nav>
                </div>
                <h1 class="text-white arabic-text fw-bold mb-2">
                    {{ $ad->title }}
                </h1>
                <div class="ad-meta text-white-50">
                    <span class="me-3">
                        <i class="{{ $ad->category->icon }} me-1"></i>
                        {{ $ad->category->name }}
                    </span>
                    @if($ad->location)
                        <span class="me-3">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ $ad->location }}
                        </span>
                    @endif
                    <span>
                        <i class="fas fa-eye me-1"></i>
                        {{ $ad->views_count }} {{ __('views') }}
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-end">
                <div class="ad-actions">
                    <button class="btn btn-warning btn-lg me-2" onclick="shareAd('{{ url()->current() }}', '{{ $ad->title }}')">
                        <i class="fas fa-share-alt me-2"></i>
                        {{ __('Share') }}
                    </button>
                    <a href="{{ route('ads.category', $ad->category->slug) }}" class="btn btn-outline-light">
                        <i class="fas fa-arrow-right me-2"></i>
                        {{ __('Back to Category') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الإعلان -->
<section class="ad-content py-5">
    <div class="container">
        <div class="row">
            <!-- العمود الرئيسي -->
            <div class="col-lg-8">
                <!-- صورة الإعلان -->
                <div class="ad-image-container mb-4">
                    @if($ad->image_url)
                        <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}" class="ad-main-image">
                    @else
                        <div class="ad-placeholder-image">
                            <i class="{{ $ad->category->icon }} fa-5x text-muted"></i>
                            <p class="text-muted mt-3 arabic-text">{{ __('No image available') }}</p>
                        </div>
                    @endif
                </div>

                <!-- وصف الإعلان -->
                <div class="ad-description mb-4">
                    <h3 class="arabic-text fw-bold mb-3">{{ __('Description') }}</h3>
                    <div class="description-content arabic-text">
                        {!! nl2br(e($ad->description)) !!}
                    </div>
                </div>

                <!-- التنقل بين الإعلانات -->
                @if($previousAd || $nextAd)
                    <div class="ad-navigation mb-4">
                        <div class="row g-3">
                            @if($previousAd)
                                <div class="col-6">
                                    <a href="{{ route('ads.show', [$previousAd->category->slug, $previousAd->slug]) }}"
                                       class="btn btn-outline-primary w-100 text-start">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        <div class="nav-content">
                                            <small class="text-muted arabic-text">{{ __('Previous Ad') }}</small>
                                            <div class="nav-title arabic-text">{{ Str::limit($previousAd->title, 30) }}</div>
                                        </div>
                                    </a>
                                </div>
                            @endif

                            @if($nextAd)
                                <div class="col-6">
                                    <a href="{{ route('ads.show', [$nextAd->category->slug, $nextAd->slug]) }}"
                                       class="btn btn-outline-primary w-100 text-end">
                                        <div class="nav-content">
                                            <small class="text-muted arabic-text">{{ __('Next Ad') }}</small>
                                            <div class="nav-title arabic-text">{{ Str::limit($nextAd->title, 30) }}</div>
                                        </div>
                                        <i class="fas fa-arrow-left ms-2"></i>
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- معلومات إضافية -->
                <div class="ad-additional-info">
                    <h4 class="arabic-text fw-bold mb-3">{{ __('Additional Information') }}</h4>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-calendar text-primary me-2"></i>
                                <strong>{{ __('Published') }}:</strong>
                                <span class="arabic-text">{{ $ad->created_at->format('Y/m/d') }}</span>
                            </div>
                        </div>
                        @if($ad->expires_at)
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-hourglass-half text-warning me-2"></i>
                                    <strong>{{ __('Expires') }}:</strong>
                                    <span class="arabic-text">{{ $ad->expires_at->format('Y/m/d') }}</span>
                                </div>
                            </div>
                        @endif
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-eye text-info me-2"></i>
                                <strong>{{ __('Views') }}:</strong>
                                <span>{{ $ad->views_count }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-tag text-success me-2"></i>
                                <strong>{{ __('Category') }}:</strong>
                                <span class="arabic-text">{{ $ad->category->name }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <!-- معلومات الأسعار المتقدمة -->
                <div class="pricing-card mb-4">
                    <h4 class="arabic-text fw-bold mb-3">
                        <i class="fas fa-tag text-success me-2"></i>
                        {{ __('Price Information') }}
                    </h4>
                    <div class="pricing-content">
                        @include('components.price-display', [
                            'ad' => $ad,
                            'compact' => false,
                            'detailed' => true
                        ])
                    </div>
                </div>

                <!-- معلومات التواصل المتقدمة -->
                @if($ad->phone || $ad->email || $ad->contact_info)
                    <div class="contact-card mb-4">
                        <h4 class="arabic-text fw-bold mb-3">
                            <i class="fas fa-phone text-primary me-2"></i>
                            {{ __('Contact Information') }}
                        </h4>
                        <div class="contact-content">
                            @include('components.ad-card.partials.contact', [
                                'ad' => $ad,
                                'detailed' => true,
                                'showReveal' => true
                            ])
                        </div>
                    </div>
                @endif

                <!-- إحصائيات سريعة -->
                <div class="stats-card mb-4">
                    <h5 class="arabic-text fw-bold mb-3">{{ __('Quick Stats') }}</h5>
                    <div class="stats-list">
                        <div class="stat-item">
                            <i class="fas fa-eye text-info"></i>
                            <span>{{ $ad->views_count }} {{ __('views') }}</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-calendar text-success"></i>
                            <span class="arabic-text">{{ $ad->created_at->diffForHumans() }}</span>
                        </div>
                        @if($ad->location)
                            <div class="stat-item">
                                <i class="fas fa-map-marker-alt text-danger"></i>
                                <span class="arabic-text">{{ $ad->location }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- مشاركة -->
                <div class="share-card">
                    <h5 class="arabic-text fw-bold mb-3">{{ __('Share this ad') }}</h5>
                    <div class="share-buttons">
                        <button class="btn btn-primary btn-sm me-2 mb-2" onclick="shareToFacebook()">
                            <i class="fab fa-facebook-f me-1"></i>
                            {{ __('facebook') }}
                        </button>
                        <button class="btn btn-info btn-sm me-2 mb-2" onclick="shareToTwitter()">
                            <i class="fab fa-twitter me-1"></i>
                            {{ __('twitter') }}
                        </button>
                        <button class="btn btn-success btn-sm me-2 mb-2" onclick="shareToWhatsApp()">
                            <i class="fab fa-whatsapp me-1"></i>
                            {{ __('WhatsApp') }}
                        </button>
                        <button class="btn btn-secondary btn-sm mb-2" onclick="copyLink()">
                            <i class="fas fa-link me-1"></i>
                            {{ __('Copy Link') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- إعلانات مشابهة -->
@if($relatedAds->count() > 0)
<section class="related-ads py-5 bg-light">
    <div class="container">
        <h3 class="arabic-text fw-bold text-center mb-5">{{ __('Related Ads') }}</h3>
        <div class="row">
            @foreach($relatedAds as $relatedAd)
                @include('components.ad-card.index', [
                    'ad' => $relatedAd,
                    'variant' => 'compact',
                    'columns' => 3,
                    'showFavorites' => true,
                    'descriptionLength' => 80,
                    'showPricing' => true,
                    'showMeta' => false,
                    'showActions' => true,
                    'showExpiry' => false
                ])
            @endforeach
        </div>
        <div class="text-center mt-4">
            <a href="{{ route('ads.category', $ad->category->slug) }}" class="btn btn-primary">
                <i class="fas fa-eye me-2"></i>
                {{ __('View All in') }} {{ $ad->category->name }}
            </a>
        </div>
    </div>
</section>
@endif
@endsection

@push('styles')
<style>
/* تنسيق رأس الإعلان */
.ad-details-header {
    position: relative;
    overflow: hidden;
}

.ad-details-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: rgba(255, 255, 255, 0.5);
}

/* صورة الإعلان */
.ad-image-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.ad-main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.ad-placeholder-image {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
}

/* وصف الإعلان */
.description-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    line-height: 1.8;
    font-size: 1.1rem;
}

/* التنقل بين الإعلانات */
.ad-navigation .btn {
    border-radius: 15px;
    padding: 1rem;
    height: auto;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.ad-navigation .btn:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
}

.nav-content {
    display: flex;
    flex-direction: column;
    align-items: inherit;
}

.nav-title {
    font-weight: bold;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* معلومات إضافية */
.ad-additional-info {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.info-item {
    padding: 0.75rem;
    background: rgba(0, 123, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

/* بطاقات الشريط الجانبي */
.pricing-card,
.contact-card,
.stats-card,
.share-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.pricing-content {
    background: rgba(16, 185, 129, 0.02);
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid rgba(16, 185, 129, 0.1);
}

.contact-content {
    background: rgba(0, 123, 255, 0.05);
    padding: 1rem;
    border-radius: 10px;
}

.stats-list .stat-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.stats-list .stat-item:last-child {
    border-bottom: none;
}

.stats-list .stat-item i {
    width: 20px;
    margin-right: 0.5rem;
}

.share-buttons .btn {
    border-radius: 50px;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .ad-details-header {
        text-align: center;
    }
    
    .ad-actions {
        margin-top: 1rem;
        text-align: center !important;
    }
    
    .ad-actions .btn {
        display: block;
        margin: 0.25rem 0;
        width: 100%;
    }
    
    .ad-main-image,
    .ad-placeholder-image {
        height: 250px;
    }
    
    .description-content {
        padding: 1.5rem;
        font-size: 1rem;
    }
    
    .ad-additional-info {
        padding: 1.5rem;
    }
    
    .pricing-card,
    .contact-card,
    .stats-card,
    .share-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .breadcrumb {
        font-size: 0.8rem;
    }
    
    .ad-main-image,
    .ad-placeholder-image {
        height: 200px;
    }
    
    .description-content {
        padding: 1rem;
    }
    
    .share-buttons .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// دالة نسخ النص
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('{{ __("Copied to clipboard") }}', 'success');
    }).catch(function() {
        showToast('{{ __("Failed to copy") }}', 'error');
    });
}

// دالة نسخ الرابط
function copyLink() {
    copyToClipboard(window.location.href);
}

// دالة المشاركة العامة
function shareAd(url, title) {
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        copyLink();
    }
}

// مشاركة فيسبوك
function shareToFacebook() {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
}

// مشاركة تويتر
function shareToTwitter() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent('{{ $ad->title }}');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
}

// مشاركة واتساب
function shareToWhatsApp() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent('{{ $ad->title }} - {{ $ad->category->name }}');
    window.open(`https://wa.me/?text=${text} ${url}`, '_blank');
}

// استخدام نظام Toast الموحد - تم حذف الدالة المكررة
// الدالة متوفرة عالمياً من components/toast.blade.php
</script>
@endpush
