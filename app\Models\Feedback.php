<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * نموذج التقييمات وآراء العملاء
 * يحتوي على تقييمات وآراء المستخدمين حول الموقع والخدمات
 */
class Feedback extends Model
{
    /**
     * اسم الجدول في قاعدة البيانات
     */
    protected $table = 'feedbacks';

    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'name',     // اسم العميل
        'email',    // البريد الإلكتروني
        'message',  // رسالة التقييم
        'rating',   // التقييم بالنجوم (1-5)
        'status',   // حالة التقييم (pending, approved, rejected)
        'phone',    // رقم الهاتف
        'company',  // اسم الشركة أو المؤسسة
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'rating' => 'integer', // تحويل التقييم إلى رقم صحيح
    ];

    /**
     * البحث في التقييمات المعتمدة فقط
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * البحث في التقييمات المعلقة (في انتظار الموافقة)
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * البحث حسب التقييم (عدد النجوم)
     */
    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * الحصول على التقييمات عالية الجودة (4 نجوم فأكثر)
     */
    public function scopeHighRated($query)
    {
        return $query->where('rating', '>=', 4);
    }

    /**
     * الحصول على نص التقييم بالنجوم
     */
    public function getStarsAttribute(): string
    {
        return str_repeat('★', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }

    /**
     * الحصول على لون التقييم حسب عدد النجوم
     */
    public function getRatingColorAttribute(): string
    {
        return match($this->rating) {
            5 => 'success',      // أخضر للتقييم الممتاز
            4 => 'primary',      // أزرق للتقييم الجيد
            3 => 'warning',      // أصفر للتقييم المتوسط
            2 => 'orange',       // برتقالي للتقييم الضعيف
            1 => 'danger',       // أحمر للتقييم السيء
            default => 'secondary' // رمادي للافتراضي
        };
    }
}
