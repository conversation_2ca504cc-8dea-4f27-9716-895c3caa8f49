@extends('layouts.app')

@section('title', $category->name . ' - ' . __('ads') . ' - ' . __('site_name'))
@section('description', $category->description)

@section('content')
<!-- القسم الرئيسي -->
<section class="category-ads-header py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="category-info text-white">
                    <div class="category-icon-title mb-3">
                        <i class="{{ $category->icon }} fa-3x text-warning me-3"></i>
                        <div class="category-text">
                            <h1 class="arabic-text fw-bold mb-2">
                                {{ $category->name }}
                            </h1>
                            <p class="arabic-text mb-0 opacity-75">
                                {{ $category->description }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-end">
                <div class="category-stats text-white">
                    <div class="stat-item d-inline-block me-3">
                        <i class="fas fa-bullhorn"></i>
                        <span class="fw-bold">{{ $ads->total() }}</span>
                        <small class="arabic-text d-block">{{ __('ads') }}</small>
                    </div>
                    <div class="stat-item d-inline-block">
                        <i class="fas fa-eye"></i>
                        <span class="fw-bold">{{ $ads->sum('views_count') }}</span>
                        <small class="arabic-text d-block">{{ __('views') }}</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أزرار التنقل -->
        <div class="navigation-buttons mt-4">
            <a href="{{ route('categories.index') }}" class="btn btn-outline-light btn-sm arabic-text me-2">
                <i class="fas fa-arrow-right me-2"></i>
                {{ __('Back to Categories') }}
            </a>
            <a href="{{ route('ads.index') }}" class="btn btn-outline-light btn-sm arabic-text">
                <i class="fas fa-bullhorn me-2"></i>
                {{ __('All Ads') }}
            </a>
        </div>
    </div>
</section>

<!-- قسم البحث والتصفية -->
<section class="search-filters py-4 bg-light">
    <div class="container">
        <form method="GET" action="{{ route('ads.category', $category->slug) }}" class="search-form">
            <div class="row g-3">
                <!-- شريط البحث -->
                <div class="col-lg-6 col-md-8">
                    <div class="search-input">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" 
                               name="search" 
                               class="form-control arabic-text" 
                               placeholder="{{ __('Search in') }} {{ $category->name }}..."
                               value="{{ request('search') }}">
                    </div>
                </div>
                
                <!-- فلتر الموقع -->
                <div class="col-lg-3 col-md-4">
                    <input type="text" 
                           name="location" 
                           class="form-control arabic-text" 
                           placeholder="{{ __('Location') }}"
                           value="{{ request('location') }}">
                </div>
                
                <!-- زر البحث -->
                <div class="col-lg-3 col-md-12">
                    <div class="search-buttons">
                        <button type="submit" class="btn btn-primary w-100 arabic-text">
                            <i class="fas fa-search me-2"></i>
                            {{ __('Search') }}
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- فلاتر إضافية -->
            <div class="row g-3 mt-2">
                <div class="col-lg-4 col-md-6">
                    <select name="sort" class="form-select arabic-text" onchange="this.form.submit()">
                        <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>
                            {{ __('Newest First') }}
                        </option>
                        <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>
                            {{ __('Oldest First') }}
                        </option>
                        <option value="most_viewed" {{ request('sort') == 'most_viewed' ? 'selected' : '' }}>
                            {{ __('Most Viewed') }}
                        </option>
                    </select>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    @if(request()->hasAny(['search', 'location', 'sort']))
                        <a href="{{ route('ads.category', $category->slug) }}" class="btn btn-outline-secondary w-100 arabic-text">
                            <i class="fas fa-times me-2"></i>
                            {{ __('Clear Filters') }}
                        </a>
                    @endif
                </div>
            </div>
        </form>
    </div>
</section>

<!-- قسم النتائج -->
<section class="ads-results py-5">
    <div class="container">
        <!-- معلومات النتائج -->
        <div class="results-info mb-4">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h5 class="arabic-text mb-0">
                        @if(request('search') || request('location'))
                            {{ __('Search Results in') }} {{ $category->name }}
                            @if(request('search'))
                                {{ __('for') }}: "{{ request('search') }}"
                            @endif
                        @else
                            {{ __('All ads in') }} {{ $category->name }}
                        @endif
                    </h5>
                    <small class="text-muted arabic-text">
                        {{ __('Showing') }} {{ $ads->firstItem() ?? 0 }} - {{ $ads->lastItem() ?? 0 }} 
                        {{ __('of') }} {{ $ads->total() }} {{ __('results') }}
                    </small>
                </div>
                <div class="col-lg-6 text-end">
                    <div class="view-options">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="setView('grid')">
                                <i class="fas fa-th"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setView('list')">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض الإعلانات -->
        @if($ads->count() > 0)
            <div class="ads-grid" id="ads-container">
                <div class="row" id="ads-row">
                    @foreach($ads as $ad)
                        @include('components.ad-card.index', [
                            'ad' => $ad,
                            'variant' => 'default',
                            'showFavorites' => true,
                            'descriptionLength' => 100,
                            'showPricing' => true,
                            'showMeta' => true,
                            'showActions' => true,
                            'showExpiry' => true
                        ])
                    @endforeach
                </div>
            </div>
            
            <!-- التصفح (Pagination) -->
            <div class="pagination-wrapper mt-5">
                <div class="d-flex justify-content-center">
                    {{ $ads->links() }}
                </div>
            </div>
        @else
            <!-- رسالة عدم وجود نتائج -->
            <div class="no-results text-center py-5">
                <div class="empty-state">
                    <i class="{{ $category->icon }} fa-5x text-muted mb-4"></i>
                    <h3 class="arabic-text text-muted mb-3">
                        @if(request()->hasAny(['search', 'location']))
                            {{ __('No ads found in') }} {{ $category->name }}
                        @else
                            {{ __('No ads in this category yet') }}
                        @endif
                    </h3>
                    <p class="text-muted arabic-text mb-4">
                        @if(request()->hasAny(['search', 'location']))
                            {{ __('Try adjusting your search criteria') }}
                        @else
                            {{ __('Ads will appear here when added to this category') }}
                        @endif
                    </p>
                    <div class="empty-actions">
                        @if(request()->hasAny(['search', 'location']))
                            <a href="{{ route('ads.category', $category->slug) }}" class="btn btn-primary arabic-text me-2">
                                <i class="fas fa-refresh me-2"></i>
                                {{ __('View All in Category') }}
                            </a>
                        @endif
                        <a href="{{ route('categories.index') }}" class="btn btn-outline-primary arabic-text">
                            <i class="fas fa-th-large me-2"></i>
                            {{ __('Browse Other Categories') }}
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- قسم التصنيفات الأخرى -->
<section class="other-categories py-5 bg-light">
    <div class="container">
        <h4 class="arabic-text fw-bold text-center mb-4">{{ __('Browse Other Categories') }}</h4>
        <div class="row g-3">
            @php
                $otherCategories = \App\Models\Category::active()
                    ->where('id', '!=', $category->id)
                    ->withCount(['ads' => function($query) {
                        $query->active()->notExpired();
                    }])
                    ->orderBy('name_ar')
                    ->get();
            @endphp
            
            @foreach($otherCategories as $otherCategory)
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="{{ route('ads.category', $otherCategory->slug) }}" class="text-decoration-none">
                        <div class="other-category-card text-center p-3">
                            <i class="{{ $otherCategory->icon }} fa-2x text-primary mb-2"></i>
                            <h6 class="arabic-text fw-bold mb-1">{{ $otherCategory->name }}</h6>
                            <small class="text-muted">{{ $otherCategory->ads_count ?? 0 }} {{ __('ads') }}</small>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق رأس التصنيف */
.category-ads-header {
    position: relative;
    overflow: hidden;
}

.category-ads-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.category-icon-title {
    display: flex;
    align-items: center;
}

.category-text h1 {
    font-size: 2.5rem;
    margin: 0;
}

.category-stats .stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    text-align: center;
}

/* تنسيق البحث */
.search-input {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 2;
}

.search-input .form-control {
    padding-left: 45px;
    border-radius: 50px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.search-input .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-form .form-select,
.search-form .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

/* تنسيق النتائج */
.results-info {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* حالة فارغة */
.empty-state {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* التصنيفات الأخرى */
.other-category-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.other-category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .category-ads-header {
        text-align: center;
    }
    
    .category-icon-title {
        flex-direction: column;
        text-align: center;
    }
    
    .category-icon-title i {
        margin-bottom: 1rem;
        margin-right: 0 !important;
    }
    
    .category-text h1 {
        font-size: 2rem;
    }
    
    .category-stats {
        margin-top: 1rem;
        text-align: center !important;
    }
    
    .navigation-buttons {
        text-align: center;
    }
    
    .navigation-buttons .btn {
        display: block;
        margin: 0.25rem 0;
        width: 100%;
    }
    
    .results-info .row {
        text-align: center;
    }
    
    .view-options {
        margin-top: 1rem;
        text-align: center !important;
    }
}

@media (max-width: 576px) {
    .category-text h1 {
        font-size: 1.5rem;
    }
    
    .category-stats .stat-item {
        padding: 0.75rem;
        margin: 0.25rem;
    }
    
    .search-input .form-control {
        padding-left: 40px;
        font-size: 0.9rem;
    }
    
    .search-icon {
        left: 12px;
        font-size: 0.9rem;
    }
    
    .empty-state {
        padding: 2rem 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// تغيير طريقة العرض
function setView(viewType) {
    const container = document.getElementById('ads-container');
    const row = document.getElementById('ads-row');
    const buttons = document.querySelectorAll('.view-options .btn');
    
    // إزالة الفئة النشطة من جميع الأزرار
    buttons.forEach(btn => btn.classList.remove('active'));
    
    if (viewType === 'grid') {
        row.className = 'row';
        buttons[0].classList.add('active');
    } else if (viewType === 'list') {
        row.className = 'row row-cols-1';
        buttons[1].classList.add('active');
    }
    
    // حفظ التفضيل في localStorage
    localStorage.setItem('ads_view_preference', viewType);
}

// استرجاع تفضيل العرض عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const savedView = localStorage.getItem('ads_view_preference');
    if (savedView) {
        setView(savedView);
    }
});
</script>
@endpush
