<?php

namespace App\Services;

use Illuminate\Support\Facades\Hash;

/**
 * خدمة تشفير كلمات المرور الموحدة
 * توفر واجهة موحدة لتشفير كلمات المرور في المشروع
 * مع إعدادات أمان محسنة
 */
class PasswordHashingService
{
    /**
     * تشفير كلمة المرور مع إعدادات أمان محسنة
     */
    public static function hash(string $password): string
    {
        // استخدام Argon2ID مع إعدادات محسنة للأمان
        return Hash::make($password, [
            'memory' => 1024,  // 1MB memory
            'time' => 2,       // 2 iterations
            'threads' => 2,    // 2 threads
        ]);
    }

    /**
     * التحقق من كلمة المرور
     */
    public static function verify(string $password, string $hashedPassword): bool
    {
        return Hash::check($password, $hashedPassword);
    }

    /**
     * التحقق من الحاجة لإعادة تشفير كلمة المرور
     * (عند تحديث إعدادات الأمان)
     */
    public static function needsRehash(string $hashedPassword): bool
    {
        return Hash::needsRehash($hashedPassword, [
            'memory' => 1024,
            'time' => 2,
            'threads' => 2,
        ]);
    }
}
