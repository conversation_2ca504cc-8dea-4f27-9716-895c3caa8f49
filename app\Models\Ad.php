<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use App\Services\CacheService;
use App\Services\EncryptionService;
use App\Models\Interaction;

/**
 * نموذج الإعلانات
 * يحتوي على جميع الإعلانات المنشورة في الموقع
 */
class Ad extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية - محدثة للهيكل الجديد
     */
    protected $fillable = [
        // المعلومات الأساسية
        'user_id',
        'category_id',
        'title_ar',
        'title_en',
        'description_ar',
        'description_en',
        'slug',

        // الصور (JSON array)
        'images',
        'main_image',

        // معلومات السعر الموحدة
        'price',
        'original_price',
        'currency',
        'price_type',
        'is_negotiable',
        'price_notes',

        // معلومات التواصل (مشفرة)
        'contact_phone',
        'contact_email',
        'contact_whatsapp',

        // الموقع
        'location',
        'city',
        'country',

        // الحالة والإعدادات
        'status',
        'is_featured',
        'is_urgent',
        'expires_at',
        'rejection_reason',

        // الإحصائيات (محسوبة)
        'views_count',
        'contact_reveals_count',
        'favorites_count',
        'rating_average',
        'rating_count',
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة - محدثة للهيكل الجديد
     */
    protected $casts = [
        // الصور
        'images' => 'array',

        // الأسعار
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'is_negotiable' => 'boolean',

        // التواريخ
        'expires_at' => 'date',

        // الحالة والإعدادات
        'is_featured' => 'boolean',
        'is_urgent' => 'boolean',

        // الإحصائيات
        'views_count' => 'integer',
        'contact_reveals_count' => 'integer',
        'favorites_count' => 'integer',
        'rating_average' => 'decimal:2',
        'rating_count' => 'integer',
    ];

    /**
     * الأحداث التي تحدث عند إنشاء إعلان جديد
     */
    protected static function boot()
    {
        parent::boot();

        // إنشاء slug تلقائياً عند إنشاء إعلان جديد
        static::creating(function ($ad) {
            if (empty($ad->slug)) {
                $ad->slug = Str::slug($ad->title_en ?: $ad->title_ar) . '-' . time();
            }
        });

        // مسح الكاش عند إنشاء أو تحديث أو حذف إعلان
        static::created(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });

        static::updated(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });

        static::deleted(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });
    }

    /**
     * علاقة متعدد إلى واحد مع التصنيفات
     * إعلان واحد ينتمي إلى تصنيف واحد
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * علاقة متعدد إلى واحد مع المستخدمين
     * إعلان واحد ينتمي إلى مستخدم واحد
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * الحصول على عنوان الإعلان حسب اللغة الحالية
     */
    public function getTitleAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->title_ar : $this->title_en;
    }

    /**
     * الحصول على وصف الإعلان حسب اللغة الحالية
     */
    public function getDescriptionAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : $this->description_en;
    }

    /**
     * البحث في الإعلانات النشطة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * البحث في الإعلانات غير المنتهية الصلاحية
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * البحث في الإعلانات المعلقة (في انتظار الموافقة)
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * البحث في الإعلانات المميزة
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * البحث في إعلانات مستخدم معين
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * زيادة عدد المشاهدات
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * تشفير رقم الهاتف عند الحفظ
     */
    public function setPhoneAttribute($value)
    {
        $this->attributes['phone'] = EncryptionService::encryptPhone($value);
    }

    /**
     * فك تشفير رقم الهاتف عند الاستدعاء
     */
    public function getPhoneAttribute($value)
    {
        return EncryptionService::decryptPhone($value);
    }

    /**
     * تشفير البريد الإلكتروني عند الحفظ
     */
    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = EncryptionService::encryptEmail($value);
    }

    /**
     * فك تشفير البريد الإلكتروني عند الاستدعاء
     */
    public function getEmailAttribute($value)
    {
        return EncryptionService::decryptEmail($value);
    }

    /**
     * الحصول على رقم الهاتف مخفي للعرض
     */
    public function getMaskedPhoneAttribute()
    {
        return EncryptionService::maskPhone($this->phone);
    }

    /**
     * الحصول على البريد الإلكتروني مخفي للعرض
     */
    public function getMaskedEmailAttribute()
    {
        return EncryptionService::maskEmail($this->email);
    }

    /**
     * علاقة التقييمات (من النظام الجديد)
     */
    public function reviews()
    {
        return $this->hasMany(Interaction::class)->where('type', 'rating');
    }

    /**
     * التقييمات المعتمدة فقط (من النظام الجديد)
     */
    public function approvedReviews()
    {
        return $this->hasMany(Interaction::class)->where('type', 'rating');
    }

    /**
     * الحصول على متوسط التقييم (من نظام التقييمات الجديد)
     */
    public function getAverageRatingAttribute(): float
    {
        return Rating::getAverageRating($this->id);
    }

    /**
     * الحصول على السعر المنسق مع العملة (بدون نص قابل للتفاوض)
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->is_free) {
            return 'مجاني';
        }

        if ($this->price_type === 'on_request') {
            return 'السعر عند الطلب';
        }

        if (!$this->price) {
            return 'غير محدد';
        }

        $currencySymbols = [
            'YER' => 'ر.ي',
            'USD' => '$',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م'
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;
        $price = number_format($this->price, 0);

        return $price . ' ' . $symbol;
    }

    /**
     * الحصول على مسار الصورة للعرض (للتوافق مع الكود القديم)
     * يستخدم main_image بدلاً من image
     */
    public function getImageUrlAttribute(): ?string
    {
        // استخدام main_image بدلاً من image للتوافق مع الهيكل الجديد
        return $this->getMainImageUrlAttribute();
    }



    /**
     * الحصول على السعر الأصلي المنسق
     */
    public function getFormattedOriginalPriceAttribute(): string
    {
        if (!$this->original_price) {
            return '';
        }

        $currencySymbols = [
            'YER' => 'ر.ي',
            'USD' => '$',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م'
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;
        return number_format($this->original_price, 0) . ' ' . $symbol;
    }

    /**
     * الحصول على السعر المنسق مع رمز عملة صغير
     */
    public function getFormattedPriceWithSmallCurrency(): string
    {
        if ($this->is_free) {
            return 'مجاني';
        }

        if ($this->price_type === 'on_request') {
            return 'السعر عند الطلب';
        }

        if (!$this->price) {
            return 'غير محدد';
        }

        $currencySymbols = [
            'YER' => 'ر.ي',
            'USD' => '$',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م'
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;
        $price = number_format($this->price, 0);

        return $price . ' <span class="currency-small">' . $symbol . '</span>';
    }

    /**
     * الحصول على السعر الأصلي المنسق مع رمز عملة صغير
     */
    public function getFormattedOriginalPriceWithSmallCurrency(): string
    {
        if (!$this->original_price) {
            return '';
        }

        $currencySymbols = [
            'YER' => 'ر.ي',
            'USD' => '$',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م'
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;
        $price = number_format($this->original_price, 0);

        return $price . ' <span class="currency-small">' . $symbol . '</span>';
    }

    /**
     * التحقق من وجود خصم
     */
    public function hasDiscount(): bool
    {
        return $this->original_price && $this->price && $this->original_price > $this->price;
    }

    /**
     * حساب نسبة الخصم
     */
    public function getCalculatedDiscountPercentageAttribute(): float
    {
        if (!$this->hasDiscount()) {
            return 0;
        }

        return round((($this->original_price - $this->price) / $this->original_price) * 100, 0);
    }

    /**
     * حساب مبلغ التوفير
     */
    public function getSavingsAmountAttribute(): float
    {
        if (!$this->hasDiscount()) {
            return 0;
        }

        return $this->original_price - $this->price;
    }

    /**
     * التحقق من انتهاء صلاحية الخصم
     */
    public function isDiscountExpired(): bool
    {
        return $this->discount_expires_at && $this->discount_expires_at->isPast();
    }

    /**
     * تسجيل عرض تفاصيل الأسعار المتقدمة
     */
    public function logAdvancedPriceView(): void
    {
        Log::info('عرض تفاصيل الأسعار المتقدمة', [
            'ad_id' => $this->id,
            'ad_title' => $this->title,
            'price' => $this->price,
            'original_price' => $this->original_price,
            'has_discount' => $this->hasDiscount(),
            'discount_percentage' => $this->calculated_discount_percentage,
            'savings_amount' => $this->savings_amount,
            'is_negotiable' => $this->is_negotiable,
            'is_free' => $this->is_free,
            'currency' => $this->currency,
            'price_type' => $this->price_type,
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);
    }

    /**
     * تسجيل كشف رقم الهاتف
     */
    public function logPhoneReveal(): void
    {
        Log::info('كشف رقم الهاتف', [
            'ad_id' => $this->id,
            'ad_title' => $this->title,
            'has_phone' => !empty($this->phone),
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);

        // زيادة عداد كشف معلومات التواصل
        $this->increment('contact_reveals_count');
    }

    /**
     * تسجيل كشف البريد الإلكتروني
     */
    public function logEmailReveal(): void
    {
        Log::info('كشف البريد الإلكتروني', [
            'ad_id' => $this->id,
            'ad_title' => $this->title,
            'has_email' => !empty($this->email),
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);

        // زيادة عداد كشف معلومات التواصل
        $this->increment('contact_reveals_count');
    }

    /**
     * الحصول على عدد التقييمات (من النظام الجديد)
     */
    public function getReviewsCountAttribute(): int
    {
        return $this->interactions()->where('type', 'rating')->count();
    }

    /**
     * الحصول على توزيع التقييمات (من نظام التقييمات الجديد)
     */
    public function getRatingDistributionAttribute(): array
    {
        return Rating::getRatingDistribution($this->id);
    }

    /**
     * التحقق من وجود تقييم من مستخدم معين (من النظام الجديد)
     */
    public function hasUserReviewed(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $userId ? $this->interactions()
            ->where('user_id', $userId)
            ->where('type', 'rating')
            ->exists() : false;
    }

    /**
     * الحصول على تقييم المستخدم (من النظام الجديد)
     */
    public function getUserReview(?int $userId = null): ?Interaction
    {
        $userId = $userId ?? auth()->id();
        return $userId ? $this->interactions()
            ->where('user_id', $userId)
            ->where('type', 'rating')
            ->first() : null;
    }

    // تم نقل علاقة المفضلة إلى جدول interactions الموحد

    // تم نقل علاقة التقييمات إلى جدول interactions الموحد

    // تم نقل علاقة التعليقات إلى جدول interactions الموحد

    /**
     * علاقة مع التعليقات المعتمدة فقط (من النظام الجديد)
     */
    public function approvedComments()
    {
        return $this->hasMany(Interaction::class)->where('type', 'comment');
    }

    /**
     * علاقة مع المستخدمين الذين أضافوا الإعلان للمفضلة من جدول interactions
     */
    public function favoritedByUsers()
    {
        return $this->hasManyThrough(
            User::class,
            Interaction::class,
            'ad_id',
            'id',
            'id',
            'user_id'
        )->where('interactions.type', 'favorite')
         ->orderBy('interactions.created_at', 'desc');
    }

    /**
     * التحقق من إضافة المستخدم للإعلان في المفضلة - محدث للنظام الجديد
     */
    public function isFavoritedBy(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        if (!$userId) return false;

        return Interaction::where([
            'user_id' => $userId,
            'ad_id' => $this->id,
            'type' => 'favorite'
        ])->exists();
    }

    /**
     * الحصول على عدد المرات التي تم إضافة الإعلان للمفضلة - محدث للنظام الجديد
     */
    public function getFavoritesCountAttribute(): int
    {
        return Interaction::where([
            'ad_id' => $this->id,
            'type' => 'favorite'
        ])->count();
    }

    /**
     * إضافة الإعلان إلى مفضلة المستخدم الحالي - محدث للنظام الجديد
     */
    public function addToUserFavorites(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        if (!$userId) return false;

        try {
            Interaction::addFavorite($userId, $this->id);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * إزالة الإعلان من مفضلة المستخدم الحالي - محدث للنظام الجديد
     */
    public function removeFromUserFavorites(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        if (!$userId) return false;

        try {
            Interaction::removeFavorite($userId, $this->id);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * تبديل حالة الإعلان في مفضلة المستخدم الحالي - محدث للنظام الجديد
     */
    public function toggleUserFavorite(?int $userId = null): array
    {
        $userId = $userId ?? auth()->id();
        if (!$userId) {
            return [
                'success' => false,
                'action' => 'error',
                'is_favorited' => false,
                'message' => 'يجب تسجيل الدخول أولاً'
            ];
        }

        try {
            $isFavorited = $this->isFavoritedBy($userId);

            if ($isFavorited) {
                Interaction::removeFavorite($userId, $this->id);
                return [
                    'success' => true,
                    'action' => 'removed',
                    'is_favorited' => false,
                    'message' => 'تم إزالة الإعلان من المفضلة'
                ];
            } else {
                Interaction::addFavorite($userId, $this->id);
                return [
                    'success' => true,
                    'action' => 'added',
                    'is_favorited' => true,
                    'message' => 'تم إضافة الإعلان للمفضلة'
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'action' => 'error',
                'is_favorited' => false,
                'message' => 'حدث خطأ في العملية'
            ];
        }
    }

    /**
     * الحصول على عدد التقييمات - محدث للنظام الجديد
     */
    public function getRatingsCountAttribute(): int
    {
        return Interaction::where([
            'ad_id' => $this->id,
            'type' => 'rating'
        ])->count();
    }

    /**
     * الحصول على إحصائيات التقييم الشاملة
     */
    public function getRatingStatsAttribute(): array
    {
        return Rating::getAdRatingStats($this->id);
    }

    /**
     * التحقق من تقييم المستخدم للإعلان
     */
    public function isRatedBy(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Rating::hasUserRated($userId, $this->id) : false;
    }

    /**
     * الحصول على تقييم المستخدم للإعلان
     */
    public function getUserRating(?int $userId = null): ?Rating
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Rating::getUserRating($userId, $this->id) : null;
    }

    /**
     * إضافة أو تحديث تقييم المستخدم
     */
    public function addOrUpdateUserRating(array $data, ?int $userId = null): ?Rating
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Rating::addOrUpdateRating($userId, $this->id, $data) : null;
    }

    /**
     * الحصول على عدد التعليقات (من النظام الجديد)
     */
    public function getCommentsCountAttribute(): int
    {
        return $this->interactions()->where('type', 'comment')->count();
    }

    /**
     * الحصول على التعليقات مع الردود
     */
    public function getCommentsWithReplies(int $perPage = 10)
    {
        return Comment::getCommentsWithReplies($this->id, $perPage);
    }

    /**
     * إضافة تعليق جديد
     */
    public function addComment(array $data, ?int $userId = null): ?Comment
    {
        $userId = $userId ?? auth()->id();
        if (!$userId) return null;

        $data['user_id'] = $userId;
        $data['ad_id'] = $this->id;

        return Comment::addComment($data);
    }

    /**
     * التحقق من كون الإعلان جديد (أقل من 3 أيام)
     */
    public function isNew(): bool
    {
        return $this->created_at->diffInDays(now()) <= 3;
    }

    /**
     * التحقق من انتهاء صلاحية الإعلان قريباً (خلال 7 أيام)
     */
    public function expiresSoon(): bool
    {
        return $this->expires_at &&
               $this->expires_at->isFuture() &&
               $this->expires_at->diffInDays(now()) <= 7;
    }

    /**
     * التحقق من انتهاء الخصم قريباً (خلال 3 أيام)
     */
    public function discountExpiresSoon(): bool
    {
        return $this->discount_expires_at &&
               $this->discount_expires_at->isFuture() &&
               $this->discount_expires_at->diffInDays(now()) <= 3;
    }

    /**
     * التحقق من كون الإعلان شائع (أكثر من 1000 مشاهدة)
     */
    public function isPopular(): bool
    {
        return $this->views_count > 1000;
    }

    /**
     * الحصول على جميع الشارات المتاحة للإعلان
     */
    public function getAvailableBadges(): array
    {
        $badges = [];

        // شارات الحالة
        if ($this->isNew()) {
            $badges['status'][] = [
                'type' => 'new',
                'label' => 'جديد',
                'icon' => 'fas fa-star',
                'color' => 'success'
            ];
        }

        if ($this->is_featured) {
            $badges['status'][] = [
                'type' => 'featured',
                'label' => 'مميز',
                'icon' => 'fas fa-crown',
                'color' => 'warning'
            ];
        }

        if ($this->status === 'pending') {
            $badges['status'][] = [
                'type' => 'pending',
                'label' => 'في الانتظار',
                'icon' => 'fas fa-clock',
                'color' => 'secondary'
            ];
        }

        // شارات الميزات
        if ($this->is_free) {
            $badges['features'][] = [
                'type' => 'free',
                'label' => 'مجاني',
                'icon' => 'fas fa-gift',
                'color' => 'info'
            ];
        }

        if ($this->is_negotiable) {
            $badges['features'][] = [
                'type' => 'negotiable',
                'label' => 'قابل للتفاوض',
                'icon' => 'fas fa-handshake',
                'color' => 'purple'
            ];
        }

        if ($this->is_limited_offer) {
            $badges['features'][] = [
                'type' => 'limited',
                'label' => 'عرض محدود',
                'icon' => 'fas fa-hourglass-half',
                'color' => 'pink'
            ];
        }

        if ($this->hasDiscount()) {
            $badges['features'][] = [
                'type' => 'discount',
                'label' => $this->calculated_discount_percentage . '% خصم',
                'icon' => 'fas fa-percentage',
                'color' => 'orange'
            ];
        }

        // شارات التحذير
        if ($this->expiresSoon()) {
            $badges['warnings'][] = [
                'type' => 'expires_soon',
                'label' => 'ينتهي قريباً',
                'icon' => 'fas fa-clock',
                'color' => 'warning'
            ];
        }

        if ($this->discountExpiresSoon()) {
            $badges['warnings'][] = [
                'type' => 'discount_expires',
                'label' => 'ينتهي الخصم قريباً',
                'icon' => 'fas fa-fire',
                'color' => 'danger'
            ];
        }

        // شارات الإنجاز
        if ($this->isPopular()) {
            $badges['achievements'][] = [
                'type' => 'popular',
                'label' => 'شائع',
                'icon' => 'fas fa-eye',
                'color' => 'primary'
            ];
        }

        return $badges;
    }

    /**
     * التحقق من وجود تداخل في الشارات
     */
    public function hasBadgeConflicts(): bool
    {
        // التحقق من التداخلات المنطقية
        $conflicts = [];

        // تداخل: مجاني + قابل للتفاوض
        if ($this->is_free && $this->is_negotiable) {
            $conflicts[] = 'free_negotiable';
        }

        // تداخل: خصم + مجاني
        if ($this->hasDiscount() && $this->is_free) {
            $conflicts[] = 'discount_free';
        }

        return !empty($conflicts);
    }

    /**
     * الحصول على الشارة الأولى حسب الأولوية
     */
    public function getPrimaryBadge(): ?array
    {
        $badges = $this->getAvailableBadges();
        $allBadges = [];

        foreach ($badges as $group) {
            $allBadges = array_merge($allBadges, $group);
        }

        if (empty($allBadges)) {
            return null;
        }

        // ترتيب حسب الأولوية (الحالة الحرجة أولاً)
        $priority = [
            'rejected' => 10,
            'expired' => 9,
            'pending' => 8,
            'featured' => 7,
            'discount' => 6,
            'free' => 5,
            'limited' => 4,
            'expires_soon' => 4,
            'negotiable' => 3,
            'new' => 2,
            'popular' => 1
        ];

        usort($allBadges, function($a, $b) use ($priority) {
            $aPriority = $priority[$a['type']] ?? 0;
            $bPriority = $priority[$b['type']] ?? 0;
            return $bPriority <=> $aPriority;
        });

        return $allBadges[0];
    }

    /**
     * تسجيل عرض الشارات المتقدمة
     */
    public function logAdvancedBadgesView(): void
    {
        Log::info('عرض شارات الحالة والميزات المتقدمة', [
            'ad_id' => $this->id,
            'ad_title' => $this->title,
            'available_badges' => $this->getAvailableBadges(),
            'status' => $this->status,
            'is_featured' => $this->is_featured,
            'is_new' => $this->isNew(),
            'is_popular' => $this->isPopular(),
            'expires_soon' => $this->expiresSoon(),
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);
    }

    /**
     * العلاقات الجديدة للهيكل المحسن
     */

    /**
     * علاقة مع التفاعلات (مفضلة، تقييمات، تعليقات، مشاهدات)
     */
    public function interactions()
    {
        return $this->hasMany(Interaction::class);
    }

    /**
     * الحصول على المفضلة
     */
    public function favorites()
    {
        return $this->hasMany(Interaction::class)->where('type', 'favorite');
    }

    /**
     * الحصول على التقييمات
     */
    public function ratings()
    {
        return $this->hasMany(Interaction::class)->where('type', 'rating');
    }

    /**
     * الحصول على التعليقات
     */
    public function comments()
    {
        return $this->hasMany(Interaction::class)->where('type', 'comment');
    }

    /**
     * الحصول على المشاهدات
     */
    public function views()
    {
        return $this->hasMany(Interaction::class)->where('type', 'view');
    }

    /**
     * تحديث الإحصائيات من جدول التفاعلات
     */
    public function updateStatsFromInteractions()
    {
        $stats = Interaction::getAdStats($this->id);
        $averageRating = Interaction::getAverageRating($this->id);

        $this->update([
            'favorites_count' => $stats['favorites_count'],
            'views_count' => $stats['views_count'],
            'contact_reveals_count' => $stats['contact_reveals_count'],
            'rating_average' => $averageRating,
            'rating_count' => $stats['ratings_count'],
        ]);
    }

    /**
     * تشفير معلومات التواصل
     */
    public function setContactPhoneAttribute($value)
    {
        $this->attributes['contact_phone'] = $value ? EncryptionService::encryptText($value) : null;
    }

    public function setContactEmailAttribute($value)
    {
        $this->attributes['contact_email'] = $value ? EncryptionService::encryptText($value) : null;
    }

    public function setContactWhatsappAttribute($value)
    {
        $this->attributes['contact_whatsapp'] = $value ? EncryptionService::encryptText($value) : null;
    }

    /**
     * فك تشفير معلومات التواصل
     */
    public function getContactPhoneAttribute($value)
    {
        return $value ? EncryptionService::decryptText($value) : null;
    }

    public function getContactEmailAttribute($value)
    {
        return $value ? EncryptionService::decryptText($value) : null;
    }

    public function getContactWhatsappAttribute($value)
    {
        return $value ? EncryptionService::decryptText($value) : null;
    }

    /**
     * الحصول على الصورة الرئيسية
     */
    public function getMainImageUrlAttribute()
    {
        if ($this->main_image) {
            return asset('storage/' . $this->main_image);
        }

        if ($this->images && count($this->images) > 0) {
            return asset('storage/' . $this->images[0]);
        }

        // إرجاع null لعرض أيقونة التصنيف الافتراضية في الواجهة
        return null;
    }

    /**
     * الحصول على جميع الصور
     */
    public function getImageUrlsAttribute()
    {
        if (!$this->images) {
            return [$this->main_image_url];
        }

        return array_map(function($image) {
            return asset('storage/' . $image);
        }, $this->images);
    }
}
