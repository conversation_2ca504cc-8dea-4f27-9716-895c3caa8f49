@extends('layouts.app')

@section('title', __('Dashboard') . ' - ' . __('site_name'))

@section('content')
<div class="container py-4">
    <!-- ترحيب بالمستخدم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1 arabic-text" data-animation="fadeInLeft">
                        {{ __('Welcome :name', ['name' => auth()->user()->name]) }} - {{ __('Manage My Ads') }}
                    </h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Manage all your ads from here') }}</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('dashboard.ads.create') }}" class="btn btn-primary arabic-text">
                        <i class="fas fa-plus me-2"></i>
                        {{ __('Add New Ad') }}
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle arabic-text" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell me-2"></i>
                            {{ __('Announcements') }}
                            @if($unreadNotifications > 0)
                                <span class="badge bg-danger ms-1">{{ $unreadNotifications }}</span>
                            @endif
                        </button>
                        <div class="dropdown-menu dropdown-menu-end notification-dropdown" style="width: 350px;">
                            <div class="dropdown-header d-flex justify-content-between align-items-center">
                                <span class="arabic-text">{{ __('Recent Announcements') }}</span>
                                @if($unreadNotifications > 0)
                                    <button class="btn btn-sm btn-link p-0 arabic-text" onclick="markAllAsRead()">
                                        {{ __('Mark all as read') }}
                                    </button>
                                @endif
                            </div>
                            <div class="dropdown-divider"></div>

                            @forelse($notifications as $notification)
                                <div class="dropdown-item notification-item {{ $notification->isRead() ? '' : 'unread' }}"
                                     data-notification-id="{{ $notification->id }}">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 me-3">
                                            <i class="{{ $notification->type_icon }} text-{{ $notification->type_color }}"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ $notification->title }}</h6>
                                            <p class="mb-1 small text-muted">{{ Str::limit($notification->message, 80) }}</p>
                                            <small class="text-muted">{{ \Carbon\Carbon::parse($notification->created_at)->diffForHumans() }}</small>
                                        </div>
                                        @if(!$notification->isRead())
                                            <div class="flex-shrink-0">
                                                <span class="badge bg-primary rounded-pill"></span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @empty
                                <div class="dropdown-item text-center text-muted arabic-text">
                                    {{ __('No announcements') }}
                                </div>
                            @endforelse

                            <div class="dropdown-divider"></div>
                            <div class="dropdown-item text-center">
                                <a href="{{ route('dashboard.notifications.index') }}" class="btn btn-sm btn-outline-primary arabic-text">
                                    {{ __('View all notifications') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 hover-lift" data-animation="fadeInUp">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-center align-items-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-bullhorn fa-2x text-primary"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ number_format($stats['total']) }}</h3>
                    <p class="text-muted mb-0 arabic-text">{{ __('Total Ads') }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 hover-lift" data-animation="fadeInUp">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-center align-items-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ number_format($stats['active']) }}</h3>
                    <p class="text-muted mb-0 arabic-text">{{ __('Active Ads') }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 hover-lift" data-animation="fadeInUp">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-center align-items-center mb-3">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ number_format($stats['pending']) }}</h3>
                    <p class="text-muted mb-0 arabic-text">{{ __('Pending Review') }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 hover-lift" data-animation="fadeInUp">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-center align-items-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-eye fa-2x text-info"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ number_format($stats['total_views']) }}</h3>
                    <p class="text-muted mb-0 arabic-text">{{ __('Total Views') }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الإعلانات الحديثة -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm" data-animation="fadeInLeft">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        {{ __('Your Recent Ads') }}
                    </h5>
                    <a href="{{ route('dashboard.ads.index') }}" class="btn btn-sm btn-outline-primary arabic-text">
                        {{ __('View All') }}
                    </a>
                </div>
                <div class="card-body">
                    @forelse($recentAds as $ad)
                        <div class="d-flex align-items-center mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                            <div class="flex-shrink-0 me-3">
                                @if($ad->image)
                                    <img src="{{ asset('storage/' . $ad->image) }}"
                                         alt="{{ $ad->title }}"
                                         class="rounded"
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="{{ route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]) }}"
                                       class="text-decoration-none">
                                        {{ $ad->title }}
                                    </a>
                                </h6>
                                <p class="text-muted mb-1 small arabic-text">{{ $ad->category->name ?? __('Not specified') }}</p>
                                <div class="d-flex align-items-center gap-3">
                                    <span class="badge bg-{{ $ad->status === 'active' ? 'success' : ($ad->status === 'pending' ? 'warning' : 'danger') }}">
                                        {{ $ad->status === 'active' ? __('Active') : ($ad->status === 'pending' ? __('Pending') : __('Rejected')) }}
                                    </span>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>
                                        {{ number_format($ad->views_count) }} {{ __('views') }}
                                    </small>
                                    <small class="text-muted">
                                        {{ \Carbon\Carbon::parse($ad->created_at)->diffForHumans() }}
                                    </small>
                                </div>

                                <!-- تاريخ انتهاء الصلاحية مع تحذير القرب -->
                                @if($ad->expires_at)
                                    <div class="expiry-info mt-2 d-flex align-items-center gap-2">
                                        <small class="text-muted arabic-text">
                                            <i class="fas fa-hourglass-half me-1"></i>
                                            ينتهي في: {{ $ad->expires_at->format('Y/m/d') }}
                                        </small>
                                        {{-- شارة "ينتهي قريباً" مخفية --}}
                                        {{--
                                        @if($ad->expires_at->diffInDays(now()) <= 7)
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-clock me-1"></i>
                                                ينتهي قريباً
                                            </span>
                                        @endif
                                        --}}
                                    </div>
                                @endif
                            </div>
                            <div class="flex-shrink-0">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ route('dashboard.ads.edit', $ad) }}">
                                                <i class="fas fa-edit me-2"></i>{{ __('Edit') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item"
                                               href="{{ route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]) }}">
                                                <i class="fas fa-eye me-2"></i>{{ __('View') }}
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <button class="dropdown-item text-danger"
                                                    onclick="deleteAd({{ $ad->id }})">
                                                <i class="fas fa-trash me-2"></i>{{ __('Delete') }}
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted arabic-text">{{ __('No ads yet') }}</h5>
                            <p class="text-muted arabic-text">{{ __('Start by creating your first ad') }}</p>
                            <a href="{{ route('dashboard.ads.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                {{ __('Create New Ad') }}
                            </a>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- الروابط السريعة -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm" data-animation="fadeInRight">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        {{ __('Quick Links') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('dashboard.ads.create') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>
                            {{ __('Create New Ad') }}
                        </a>
                        <a href="{{ route('dashboard.ads.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>
                            {{ __('Manage My Ads') }}
                        </a>
                        <a href="{{ route('dashboard.profile') }}" class="btn btn-outline-info">
                            <i class="fas fa-user me-2"></i>
                            {{ __('Profile') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- نصائح سريعة -->
            <div class="card border-0 shadow-sm mt-4" data-animation="fadeInRight">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2 text-warning"></i>
                        {{ __('Tips for Better Ads') }}
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2 arabic-text">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Use a clear and detailed title') }}
                        </li>
                        <li class="mb-2 arabic-text">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Add high quality images') }}
                        </li>
                        <li class="mb-2 arabic-text">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Write a comprehensive service description') }}
                        </li>
                        <li class="mb-0 arabic-text">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Specify location accurately') }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.notification-dropdown {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border: none;
    white-space: normal;
}

.notification-item.unread {
    background-color: rgba(0, 123, 255, 0.05);
    border-left: 3px solid #007bff;
}

.notification-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}
</style>
@endpush

@push('scripts')
<script>
// وضع علامة مقروء على جميع الإشعارات
function markAllAsRead() {
    fetch('{{ route('dashboard.notifications.mark-all-read') }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("An error occurred") }}');
        }
    })
    .catch(() => {
        alert('{{ __("Connection error occurred") }}');
    });
}

// حذف إعلان
function deleteAd(adId) {
    if (confirm('{{ __("Are you sure you want to delete this ad?") }}')) {
        fetch(`{{ route('dashboard.ads.delete', '') }}/${adId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ __("Error deleting ad") }}');
            }
        })
        .catch(() => {
            alert('{{ __("Connection error occurred") }}');
        });
    }
}

// وضع علامة مقروء على إشعار عند النقر عليه
document.querySelectorAll('.notification-item').forEach(item => {
    item.addEventListener('click', function() {
        const notificationId = this.dataset.notificationId;
        if (this.classList.contains('unread')) {
            fetch(`/dashboard/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.classList.remove('unread');
                }
            });
        }
    });
});
</script>
@endpush

@push('styles')
<style>
/* معلومات انتهاء الصلاحية */
.expiry-info {
    background: rgba(255, 193, 7, 0.1);
    border-radius: 8px;
    padding: 0.5rem;
    border: 1px solid rgba(255, 193, 7, 0.2);
}
</style>
@endpush

@endsection
