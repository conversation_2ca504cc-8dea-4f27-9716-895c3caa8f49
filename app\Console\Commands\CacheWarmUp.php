<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;
use App\Services\AdManagementService;

/**
 * أمر تسخين الكاش
 * يقوم بتحميل البيانات المهمة في الكاش مسبقاً لتحسين الأداء
 */
class CacheWarmUp extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'cache:warm-up {--type=all : نوع الكاش (ads|categories|all)}';

    /**
     * وصف الأمر
     */
    protected $description = 'تسخين الكاش بتحميل البيانات المهمة مسبقاً';

    /**
     * تنفيذ الأمر
     */
    public function handle()
    {
        $type = $this->option('type');
        $this->info("🔥 بدء تسخين كاش: {$type}");

        try {
            switch ($type) {
                case 'ads':
                    $this->warmAdsCache();
                    break;
                case 'categories':
                    $this->warmCategoriesCache();
                    break;
                case 'all':
                default:
                    $this->warmAllCache();
                    break;
            }

            $this->info('✅ تم تسخين الكاش بنجاح!');
            $this->showCacheInfo();

        } catch (\Exception $e) {
            $this->error('❌ خطأ في تسخين الكاش: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * تسخين كاش الإعلانات
     */
    private function warmAdsCache()
    {
        $this->info('📊 تحميل الإعلانات المميزة...');
        AdManagementService::getFeaturedAds();

        $this->info('📈 تحميل الإعلانات الشائعة...');
        AdManagementService::getPopularAds();

        $this->line('✅ تم تسخين كاش الإعلانات');
    }

    /**
     * تسخين كاش التصنيفات
     */
    private function warmCategoriesCache()
    {
        $this->info('📂 تحميل التصنيفات...');
        CacheService::getActiveCategories();

        $this->line('✅ تم تسخين كاش التصنيفات');
    }

    /**
     * تسخين جميع الكاش
     */
    private function warmAllCache()
    {
        $this->warmAdsCache();
        $this->warmCategoriesCache();

        $this->info('📊 تحميل الإحصائيات...');
        CacheService::getSiteStats();
    }

    /**
     * عرض معلومات الكاش
     */
    private function showCacheInfo()
    {
        $this->info('📊 معلومات الكاش:');
        
        $cacheInfo = CacheService::getCacheInfo();
        
        foreach ($cacheInfo as $key => $info) {
            $status = $info['exists'] ? '✅' : '❌';
            $size = $info['exists'] ? number_format($info['size']) . ' bytes' : 'غير موجود';
            
            $this->line("  {$status} {$key}: {$size}");
        }
    }
}
