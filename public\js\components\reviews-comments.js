/**
 * ملف JavaScript للتفاعل مع التقييمات والتعليقات
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على وظائف التفاعل مع التقييمات والتعليقات والردود
 */

// مدير التقييمات
const ReviewsManager = {
    // إعدادات التطبيق
    config: {
        maxRating: 5,
        minRating: 1,
        apiTimeout: 10000,
        logLevel: 'info'
    },

    // حالة التطبيق
    state: {
        currentRating: 0,
        isSubmitting: false,
        activeReviewId: null
    },

    /**
     * تهيئة مدير التقييمات
     */
    init() {
        this.log('info', 'تهيئة مدير التقييمات');
        this.initializeStarRating();
        this.setupEventListeners();
    },

    /**
     * تهيئة تقييم النجوم
     */
    initializeStarRating() {
        const stars = document.querySelectorAll('.star-input');
        stars.forEach((star, index) => {
            star.addEventListener('mouseenter', () => this.highlightStars(index + 1));
            star.addEventListener('mouseleave', () => this.highlightStars(this.state.currentRating));
        });
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مراقبة تغيير النموذج
        const reviewForm = document.getElementById('reviewForm');
        if (reviewForm) {
            reviewForm.addEventListener('input', this.validateForm.bind(this));
        }
    },

    /**
     * إظهار نموذج إضافة تقييم
     */
    showAddReviewForm() {
        const form = document.getElementById('addReviewForm');
        if (form) {
            form.style.display = 'block';
            form.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // التركيز على أول نجمة
            const firstStar = form.querySelector('.star-input');
            if (firstStar) {
                firstStar.focus();
            }
        }
        
        this.log('info', 'إظهار نموذج إضافة تقييم');
    },

    /**
     * إخفاء نموذج إضافة تقييم
     */
    hideAddReviewForm() {
        const form = document.getElementById('addReviewForm');
        if (form) {
            form.style.display = 'none';
        }
        
        this.resetForm();
        this.log('info', 'إخفاء نموذج إضافة تقييم');
    },

    /**
     * تعيين التقييم
     */
    setRating(rating) {
        this.state.currentRating = rating;
        this.highlightStars(rating);
        
        const ratingInput = document.getElementById('ratingInput');
        if (ratingInput) {
            ratingInput.value = rating;
        }
        
        this.log('info', 'تعيين التقييم', { rating });
    },

    /**
     * إضاءة النجوم
     */
    highlightStars(rating) {
        const stars = document.querySelectorAll('.star-input');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });
    },

    /**
     * إرسال التقييم
     */
    async submitReview(event) {
        event.preventDefault();
        
        if (this.state.isSubmitting) {
            return;
        }
        
        this.state.isSubmitting = true;
        
        try {
            const form = event.target;
            const formData = new FormData(form);
            
            // التحقق من صحة البيانات
            if (!this.validateReviewData(formData)) {
                return;
            }
            
            this.log('info', 'إرسال تقييم جديد', {
                rating: formData.get('rating'),
                title: formData.get('title'),
                ad_id: formData.get('ad_id')
            });
            
            // إظهار مؤشر التحميل
            this.showLoadingState(true);
            
            // محاكاة إرسال البيانات (يمكن استبدالها بـ API حقيقي)
            await this.simulateApiCall();
            
            // إظهار رسالة نجاح
            this.showToast('تم إرسال التقييم بنجاح! سيتم مراجعته قريباً.', 'success');
            
            // إعادة تعيين النموذج
            this.resetForm();
            this.hideAddReviewForm();
            
        } catch (error) {
            this.log('error', 'خطأ في إرسال التقييم', error);
            this.showToast('حدث خطأ في إرسال التقييم. يرجى المحاولة مرة أخرى.', 'error');
        } finally {
            this.state.isSubmitting = false;
            this.showLoadingState(false);
        }
    },

    /**
     * حذف التقييم
     */
    async deleteReview(reviewId) {
        if (!confirm('هل أنت متأكد من حذف هذا التقييم؟')) {
            return;
        }
        
        try {
            this.log('info', 'حذف تقييم', { reviewId });
            
            // محاكاة حذف التقييم
            await this.simulateApiCall();
            
            // إزالة التقييم من الواجهة
            const reviewElement = document.querySelector(`[data-review-id="${reviewId}"]`);
            if (reviewElement) {
                reviewElement.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    reviewElement.remove();
                }, 300);
            }
            
            this.showToast('تم حذف التقييم بنجاح.', 'success');
            
        } catch (error) {
            this.log('error', 'خطأ في حذف التقييم', error);
            this.showToast('حدث خطأ في حذف التقييم.', 'error');
        }
    },

    /**
     * تبديل حالة "مفيد"
     */
    async toggleHelpful(reviewId) {
        try {
            this.log('info', 'تبديل حالة مفيد', { reviewId });
            
            const button = document.querySelector(`[onclick*="toggleHelpful(${reviewId})"]`);
            if (button) {
                button.disabled = true;
                
                // محاكاة تحديث الحالة
                await this.simulateApiCall(500);
                
                // تحديث العداد (محاكاة)
                const countMatch = button.textContent.match(/\((\d+)\)/);
                if (countMatch) {
                    const currentCount = parseInt(countMatch[1]);
                    const newCount = currentCount + 1;
                    button.innerHTML = button.innerHTML.replace(/\(\d+\)/, `(${newCount})`);
                }
                
                button.disabled = false;
                this.showToast('شكراً لك على تقييم هذه المراجعة!', 'success');
            }
            
        } catch (error) {
            this.log('error', 'خطأ في تبديل حالة مفيد', error);
            this.showToast('حدث خطأ في العملية.', 'error');
        }
    },

    /**
     * الإبلاغ عن تقييم
     */
    reportReview(reviewId) {
        this.log('info', 'الإبلاغ عن تقييم', { reviewId });
        this.showToast('تم إرسال البلاغ. سيتم مراجعته من قبل الإدارة.', 'warning');
    },

    /**
     * تحميل المزيد من التقييمات
     */
    async loadMoreReviews() {
        try {
            this.log('info', 'تحميل المزيد من التقييمات');
            
            // محاكاة تحميل البيانات
            await this.simulateApiCall(1000);
            
            this.showToast('تم تحميل المزيد من التقييمات.', 'info');
            
        } catch (error) {
            this.log('error', 'خطأ في تحميل التقييمات', error);
            this.showToast('حدث خطأ في تحميل التقييمات.', 'error');
        }
    },

    /**
     * التحقق من صحة بيانات التقييم
     */
    validateReviewData(formData) {
        const rating = parseInt(formData.get('rating'));
        
        if (!rating || rating < this.config.minRating || rating > this.config.maxRating) {
            this.showToast('يرجى اختيار تقييم صحيح.', 'warning');
            return false;
        }
        
        return true;
    },

    /**
     * التحقق من صحة النموذج
     */
    validateForm() {
        const form = document.getElementById('reviewForm');
        if (!form) return;
        
        const rating = document.getElementById('ratingInput').value;
        const submitButton = form.querySelector('button[type="submit"]');
        
        if (submitButton) {
            submitButton.disabled = !rating;
        }
    },

    /**
     * إعادة تعيين النموذج
     */
    resetForm() {
        this.state.currentRating = 0;
        this.highlightStars(0);
        
        const form = document.getElementById('reviewForm');
        if (form) {
            form.reset();
        }
        
        const ratingInput = document.getElementById('ratingInput');
        if (ratingInput) {
            ratingInput.value = '';
        }
    },

    /**
     * إظهار حالة التحميل
     */
    showLoadingState(isLoading) {
        const submitButton = document.querySelector('#reviewForm button[type="submit"]');
        if (submitButton) {
            if (isLoading) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإرسال...';
            } else {
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-save me-1"></i>إرسال التقييم';
            }
        }
    },

    /**
     * محاكاة استدعاء API
     */
    async simulateApiCall(delay = 1000) {
        return new Promise(resolve => {
            setTimeout(resolve, delay);
        });
    },

    /**
     * إظهار رسالة Toast
     */
    showToast(message, type = 'info') {
        // استخدام نظام Toast الموحد إذا كان متوفراً
        if (typeof showToast === 'function') {
            showToast(message, type);
            return;
        }

        // إنشاء Toast مخصص
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    },

    /**
     * تسجيل الأحداث
     */
    log(level, message, data = null) {
        if (this.config.logLevel === 'none') return;

        const logData = {
            timestamp: new Date().toISOString(),
            component: 'ReviewsManager',
            message,
            data
        };

        switch (level) {
            case 'error':
                console.error('[ReviewsManager]', message, data);
                break;
            case 'warn':
                console.warn('[ReviewsManager]', message, data);
                break;
            case 'info':
            default:
                console.log('[ReviewsManager]', message, data);
                break;
        }
    }
};

// مدير التعليقات
const CommentsManager = {
    // إعدادات التطبيق
    config: {
        maxCommentLength: 1000,
        maxReplyLength: 500,
        apiTimeout: 10000,
        logLevel: 'info'
    },

    // حالة التطبيق
    state: {
        isSubmitting: false,
        activeCommentId: null,
        replyToCommentId: null
    },

    /**
     * تهيئة مدير التعليقات
     */
    init() {
        this.log('info', 'تهيئة مدير التعليقات');
        this.setupEventListeners();
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مراقبة تغيير النماذج
        const commentForm = document.getElementById('commentForm');
        if (commentForm) {
            commentForm.addEventListener('input', this.validateCommentForm.bind(this));
        }
    },

    /**
     * إظهار نموذج إضافة تعليق
     */
    showAddCommentForm() {
        const form = document.getElementById('addCommentForm');
        if (form) {
            form.style.display = 'block';
            form.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // التركيز على حقل النص
            const textarea = form.querySelector('textarea');
            if (textarea) {
                textarea.focus();
            }
        }
        
        this.log('info', 'إظهار نموذج إضافة تعليق');
    },

    /**
     * إخفاء نموذج إضافة تعليق
     */
    hideAddCommentForm() {
        const form = document.getElementById('addCommentForm');
        if (form) {
            form.style.display = 'none';
        }
        
        this.resetCommentForm();
        this.log('info', 'إخفاء نموذج إضافة تعليق');
    },

    /**
     * إرسال تعليق
     */
    async submitComment(event) {
        event.preventDefault();
        
        if (this.state.isSubmitting) {
            return;
        }
        
        this.state.isSubmitting = true;
        
        try {
            const form = event.target;
            const formData = new FormData(form);
            
            // التحقق من صحة البيانات
            if (!this.validateCommentData(formData)) {
                return;
            }
            
            this.log('info', 'إرسال تعليق جديد', {
                content: formData.get('content').substring(0, 50) + '...',
                ad_id: formData.get('ad_id')
            });
            
            // إظهار مؤشر التحميل
            this.showCommentLoadingState(true);
            
            // محاكاة إرسال البيانات
            await this.simulateApiCall();
            
            // إظهار رسالة نجاح
            this.showToast('تم إرسال التعليق بنجاح!', 'success');
            
            // إعادة تعيين النموذج
            this.resetCommentForm();
            this.hideAddCommentForm();
            
        } catch (error) {
            this.log('error', 'خطأ في إرسال التعليق', error);
            this.showToast('حدث خطأ في إرسال التعليق. يرجى المحاولة مرة أخرى.', 'error');
        } finally {
            this.state.isSubmitting = false;
            this.showCommentLoadingState(false);
        }
    },

    /**
     * إظهار نموذج الرد
     */
    showReplyForm(commentId) {
        // إخفاء جميع نماذج الرد الأخرى
        document.querySelectorAll('.reply-form').forEach(form => {
            form.style.display = 'none';
        });
        
        const replyForm = document.getElementById(`replyForm${commentId}`);
        if (replyForm) {
            replyForm.style.display = 'block';
            
            // التركيز على حقل النص
            const textarea = replyForm.querySelector('textarea');
            if (textarea) {
                textarea.focus();
            }
        }
        
        this.state.replyToCommentId = commentId;
        this.log('info', 'إظهار نموذج الرد', { commentId });
    },

    /**
     * إخفاء نموذج الرد
     */
    hideReplyForm(commentId) {
        const replyForm = document.getElementById(`replyForm${commentId}`);
        if (replyForm) {
            replyForm.style.display = 'none';
            replyForm.querySelector('form').reset();
        }
        
        this.state.replyToCommentId = null;
        this.log('info', 'إخفاء نموذج الرد', { commentId });
    },

    /**
     * إرسال رد
     */
    async submitReply(event, commentId) {
        event.preventDefault();
        
        if (this.state.isSubmitting) {
            return;
        }
        
        this.state.isSubmitting = true;
        
        try {
            const form = event.target;
            const formData = new FormData(form);
            
            this.log('info', 'إرسال رد جديد', {
                commentId,
                content: formData.get('content').substring(0, 50) + '...'
            });
            
            // محاكاة إرسال البيانات
            await this.simulateApiCall();
            
            // إظهار رسالة نجاح
            this.showToast('تم إرسال الرد بنجاح!', 'success');
            
            // إخفاء نموذج الرد
            this.hideReplyForm(commentId);
            
        } catch (error) {
            this.log('error', 'خطأ في إرسال الرد', error);
            this.showToast('حدث خطأ في إرسال الرد.', 'error');
        } finally {
            this.state.isSubmitting = false;
        }
    },

    /**
     * تعديل تعليق
     */
    editComment(commentId) {
        this.log('info', 'تعديل تعليق', { commentId });
        this.showToast('ميزة التعديل قيد التطوير.', 'info');
    },

    /**
     * حذف تعليق
     */
    async deleteComment(commentId) {
        if (!confirm('هل أنت متأكد من حذف هذا التعليق؟')) {
            return;
        }
        
        try {
            this.log('info', 'حذف تعليق', { commentId });
            
            // محاكاة حذف التعليق
            await this.simulateApiCall();
            
            // إزالة التعليق من الواجهة
            const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);
            if (commentElement) {
                commentElement.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    commentElement.remove();
                }, 300);
            }
            
            this.showToast('تم حذف التعليق بنجاح.', 'success');
            
        } catch (error) {
            this.log('error', 'خطأ في حذف التعليق', error);
            this.showToast('حدث خطأ في حذف التعليق.', 'error');
        }
    },

    /**
     * تبديل الإعجاب
     */
    async toggleLike(commentId) {
        try {
            this.log('info', 'تبديل الإعجاب', { commentId });
            
            const button = document.querySelector(`[onclick*="toggleLike(${commentId})"]`);
            if (button) {
                button.disabled = true;
                
                // محاكاة تحديث الحالة
                await this.simulateApiCall(500);
                
                // تحديث العداد (محاكاة)
                const countMatch = button.textContent.match(/\((\d+)\)/);
                if (countMatch) {
                    const currentCount = parseInt(countMatch[1]);
                    const newCount = currentCount + 1;
                    button.innerHTML = button.innerHTML.replace(/\(\d+\)/, `(${newCount})`);
                }
                
                button.disabled = false;
                this.showToast('شكراً لك!', 'success');
            }
            
        } catch (error) {
            this.log('error', 'خطأ في تبديل الإعجاب', error);
            this.showToast('حدث خطأ في العملية.', 'error');
        }
    },

    /**
     * الإبلاغ عن تعليق
     */
    reportComment(commentId) {
        this.log('info', 'الإبلاغ عن تعليق', { commentId });
        this.showToast('تم إرسال البلاغ. سيتم مراجعته من قبل الإدارة.', 'warning');
    },

    /**
     * تحميل المزيد من التعليقات
     */
    async loadMoreComments() {
        try {
            this.log('info', 'تحميل المزيد من التعليقات');
            
            // محاكاة تحميل البيانات
            await this.simulateApiCall(1000);
            
            this.showToast('تم تحميل المزيد من التعليقات.', 'info');
            
        } catch (error) {
            this.log('error', 'خطأ في تحميل التعليقات', error);
            this.showToast('حدث خطأ في تحميل التعليقات.', 'error');
        }
    },

    /**
     * التحقق من صحة بيانات التعليق
     */
    validateCommentData(formData) {
        const content = formData.get('content');
        
        if (!content || content.trim().length === 0) {
            this.showToast('يرجى كتابة محتوى التعليق.', 'warning');
            return false;
        }
        
        if (content.length > this.config.maxCommentLength) {
            this.showToast(`التعليق طويل جداً. الحد الأقصى ${this.config.maxCommentLength} حرف.`, 'warning');
            return false;
        }
        
        return true;
    },

    /**
     * التحقق من صحة نموذج التعليق
     */
    validateCommentForm() {
        const form = document.getElementById('commentForm');
        if (!form) return;
        
        const content = form.querySelector('textarea[name="content"]').value;
        const submitButton = form.querySelector('button[type="submit"]');
        
        if (submitButton) {
            submitButton.disabled = !content.trim();
        }
    },

    /**
     * إعادة تعيين نموذج التعليق
     */
    resetCommentForm() {
        const form = document.getElementById('commentForm');
        if (form) {
            form.reset();
        }
    },

    /**
     * إظهار حالة التحميل للتعليق
     */
    showCommentLoadingState(isLoading) {
        const submitButton = document.querySelector('#commentForm button[type="submit"]');
        if (submitButton) {
            if (isLoading) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإرسال...';
            } else {
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-save me-1"></i>إرسال التعليق';
            }
        }
    },

    /**
     * محاكاة استدعاء API
     */
    async simulateApiCall(delay = 1000) {
        return new Promise(resolve => {
            setTimeout(resolve, delay);
        });
    },

    /**
     * إظهار رسالة Toast
     */
    showToast(message, type = 'info') {
        // استخدام نظام Toast الموحد إذا كان متوفراً
        if (typeof showToast === 'function') {
            showToast(message, type);
            return;
        }

        // إنشاء Toast مخصص
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    },

    /**
     * تسجيل الأحداث
     */
    log(level, message, data = null) {
        if (this.config.logLevel === 'none') return;

        const logData = {
            timestamp: new Date().toISOString(),
            component: 'CommentsManager',
            message,
            data
        };

        switch (level) {
            case 'error':
                console.error('[CommentsManager]', message, data);
                break;
            case 'warn':
                console.warn('[CommentsManager]', message, data);
                break;
            case 'info':
            default:
                console.log('[CommentsManager]', message, data);
                break;
        }
    }
};

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    ReviewsManager.init();
    CommentsManager.init();
});

// تصدير للاستخدام العام
window.ReviewsManager = ReviewsManager;
window.CommentsManager = CommentsManager;
