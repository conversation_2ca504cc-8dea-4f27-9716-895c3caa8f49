<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * خدمة تنظيف وتطهير المدخلات
 * تحمي من هجمات XSS وتنظف البيانات المدخلة
 */
class InputSanitizationService
{
    /**
     * العلامات المسموحة في النصوص الغنية
     */
    const ALLOWED_HTML_TAGS = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';

    /**
     * أنماط XSS الخطيرة
     */
    const XSS_PATTERNS = [
        // JavaScript في العلامات
        '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/data:text\/html/i',
        
        // أحداث JavaScript
        '/on\w+\s*=/i',
        '/onclick/i',
        '/onload/i',
        '/onerror/i',
        '/onmouseover/i',
        
        // علامات خطيرة
        '/<iframe\b/i',
        '/<object\b/i',
        '/<embed\b/i',
        '/<applet\b/i',
        '/<meta\b/i',
        '/<link\b/i',
        '/<style\b/i',
        '/<base\b/i',
        
        // محاولات تشفير
        '/&#x/i',
        '/&#\d/i',
        '/%3C/i',
        '/%3E/i',
    ];

    /**
     * تنظيف النص العادي
     */
    public static function sanitizeText(string $input): string
    {
        // إزالة المسافات الزائدة
        $input = trim($input);
        
        // إزالة جميع علامات HTML
        $input = strip_tags($input);
        
        // تحويل الأحرف الخاصة إلى HTML entities
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        // إزالة الأحرف غير المرئية
        $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);
        
        return $input;
    }

    /**
     * تنظيف النص الغني (مع السماح ببعض علامات HTML)
     */
    public static function sanitizeRichText(string $input): string
    {
        // إزالة المسافات الزائدة
        $input = trim($input);
        
        // فحص وجود محاولات XSS
        if (self::containsXss($input)) {
            Log::warning('محاولة XSS مكتشفة في النص الغني', [
                'input' => substr($input, 0, 200),
                'ip' => request()->ip(),
            ]);
            
            // إزالة جميع علامات HTML في حالة وجود XSS
            return self::sanitizeText($input);
        }
        
        // السماح بعلامات HTML محددة فقط
        $input = strip_tags($input, self::ALLOWED_HTML_TAGS);
        
        // تنظيف الخصائص الخطيرة من العلامات المسموحة
        $input = self::removeHtmlAttributes($input);
        
        return $input;
    }

    /**
     * تنظيف البريد الإلكتروني
     */
    public static function sanitizeEmail(string $email): string
    {
        $email = trim(strtolower($email));
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        
        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return '';
        }
        
        return $email;
    }

    /**
     * تنظيف رقم الهاتف
     */
    public static function sanitizePhone(string $phone): string
    {
        // إزالة جميع الأحرف غير الرقمية عدا + و - و مسافة
        $phone = preg_replace('/[^\d+\-\s]/', '', $phone);
        
        // إزالة المسافات الزائدة
        $phone = trim($phone);
        
        return $phone;
    }

    /**
     * تنظيف URL
     */
    public static function sanitizeUrl(string $url): string
    {
        $url = trim($url);
        
        // فحص البروتوكولات المسموحة
        if (!preg_match('/^https?:\/\//', $url)) {
            return '';
        }
        
        // تنظيف URL
        $url = filter_var($url, FILTER_SANITIZE_URL);
        
        // التحقق من صحة URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return '';
        }
        
        return $url;
    }

    /**
     * تنظيف اسم الملف
     */
    public static function sanitizeFilename(string $filename): string
    {
        // إزالة المسار
        $filename = basename($filename);
        
        // إزالة الأحرف الخطيرة
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // منع الأسماء المحجوزة
        $reservedNames = ['con', 'prn', 'aux', 'nul'];
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        
        if (in_array(strtolower($nameWithoutExt), $reservedNames)) {
            $filename = 'file_' . $filename;
        }
        
        return $filename;
    }

    /**
     * فحص وجود محاولات XSS
     */
    private static function containsXss(string $input): bool
    {
        foreach (self::XSS_PATTERNS as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * إزالة الخصائص الخطيرة من علامات HTML
     */
    private static function removeHtmlAttributes(string $input): string
    {
        // إزالة جميع الخصائص عدا class و id
        $input = preg_replace('/<(\w+)[^>]*?(class="[^"]*"|id="[^"]*")?[^>]*?>/', '<$1 $2>', $input);
        
        // إزالة الخصائص الخطيرة تحديداً
        $dangerousAttributes = [
            'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
            'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
            'style', 'javascript', 'vbscript', 'data'
        ];
        
        foreach ($dangerousAttributes as $attr) {
            $input = preg_replace('/' . $attr . '\s*=\s*["\'][^"\']*["\']/i', '', $input);
        }
        
        return $input;
    }

    /**
     * تنظيف مصفوفة من البيانات
     */
    public static function sanitizeArray(array $data, array $rules = []): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = self::sanitizeArray($value, $rules[$key] ?? []);
            } else {
                $rule = $rules[$key] ?? 'text';
                $sanitized[$key] = self::sanitizeByRule($value, $rule);
            }
        }
        
        return $sanitized;
    }

    /**
     * تنظيف القيمة حسب القاعدة المحددة
     */
    private static function sanitizeByRule(string $value, string $rule): string
    {
        return match ($rule) {
            'text' => self::sanitizeText($value),
            'rich_text' => self::sanitizeRichText($value),
            'email' => self::sanitizeEmail($value),
            'phone' => self::sanitizePhone($value),
            'url' => self::sanitizeUrl($value),
            'filename' => self::sanitizeFilename($value),
            default => self::sanitizeText($value),
        };
    }

    /**
     * تنظيف بيانات النموذج
     */
    public static function sanitizeFormData(array $data): array
    {
        $rules = [
            'title_ar' => 'text',
            'title_en' => 'text',
            'description_ar' => 'rich_text',
            'description_en' => 'rich_text',
            'email' => 'email',
            'phone' => 'phone',
            'website' => 'url',
            'location' => 'text',
            'name' => 'text',
            'message' => 'rich_text',
        ];
        
        return self::sanitizeArray($data, $rules);
    }

    /**
     * فحص شامل للأمان
     */
    public static function securityCheck(string $input): array
    {
        $issues = [];
        
        // فحص XSS
        if (self::containsXss($input)) {
            $issues[] = 'محاولة XSS مكتشفة';
        }
        
        // فحص طول النص
        if (strlen($input) > 10000) {
            $issues[] = 'النص طويل جداً';
        }
        
        // فحص الأحرف غير المرئية
        if (preg_match('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', $input)) {
            $issues[] = 'أحرف غير مرئية مكتشفة';
        }
        
        return [
            'safe' => empty($issues),
            'issues' => $issues
        ];
    }
}
