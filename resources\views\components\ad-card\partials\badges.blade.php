{{--
    نظام شارات الإعلانات المحسّن والمنظم
    تم تحسينه لتجنب التكرارات والتداخلات
    يعرض الشارات حسب الأولوية والأهمية
--}}
@props(['ad', 'detailed' => false, 'position' => 'overlay'])

{{-- تحديد أولويات الشارات وتجنب التداخلات --}}
@php
    // تحديد الشارات المؤهلة للعرض حسب الأولوية
    $priorityBadges = [];

    // 1. شارات الحالة الحرجة (أولوية عالية)
    if ($ad->status === 'rejected') {
        $priorityBadges['critical'] = 'rejected';
    } elseif ($ad->status === 'expired') {
        $priorityBadges['critical'] = 'expired';
    } elseif ($ad->status === 'pending') {
        $priorityBadges['critical'] = 'pending';
    }

    // 2. شارات الميزات المميزة (أولوية متوسطة-عالية)
    if ($ad->is_featured ?? false) {
        $priorityBadges['featured'] = true;
    }

    // 3. شارات الأسعار (مبسطة)
    if ($ad->is_free) {
        $priorityBadges['free'] = true;
    }

    // إزالة شارة "قابل للتفاوض" - غير ضرورية

    // إزالة شارات التوقيت والتحذيرات لتبسيط التصميم

    // 6. شارات الإنجاز
    $achievements = [];
    if ($ad->created_at->diffInDays(now()) <= 3) {
        $achievements['new'] = true;
    }
    if ($ad->views_count > 1000) {
        $achievements['popular'] = true;
    }

    // تسجيل البيانات للمراجعة (في الوضع المفصل فقط)
    if ($detailed) {
        Log::info('عرض نظام الشارات المبسّط', [
            'ad_id' => $ad->id,
            'priority_badges' => $priorityBadges,
            'achievements' => $achievements,
            'user_id' => auth()->id(),
            'timestamp' => now()
        ]);
    }
@endphp

<div class="badges-container {{ $position === 'inline' ? 'badges-inline' : 'badges-overlay' }} {{ $detailed ? 'badges-detailed' : 'badges-compact' }}">

    {{-- شارات الحالة الحرجة (أولوية عالية) --}}
    @if(isset($priorityBadges['critical']))
        <div class="critical-badges-group">
            @if($priorityBadges['critical'] === 'rejected')
                <div class="status-badge rejected-badge" title="تم رفض الإعلان">
                    <i class="fas fa-times-circle me-1"></i>
                    <span class="arabic-text">{{ __('Rejected') }}</span>
                </div>
            @elseif($priorityBadges['critical'] === 'expired')
                <div class="status-badge expired-badge" title="انتهت صلاحية الإعلان">
                    <i class="fas fa-calendar-times me-1"></i>
                    <span class="arabic-text">{{ __('Expired') }}</span>
                </div>
            @elseif($priorityBadges['critical'] === 'pending')
                <div class="status-badge pending-badge" title="في انتظار مراجعة الإدارة">
                    <i class="fas fa-hourglass-half me-1"></i>
                    <span class="arabic-text">{{ __('Under Review') }}</span>
                </div>
            @endif
        </div>
    @endif

    {{-- شارات الميزات المميزة --}}
    @if(isset($priorityBadges['featured']))
        <div class="featured-badges-group">
            <div class="status-badge featured-badge" title="إعلان مميز ومدفوع">
                <i class="fas fa-crown me-1"></i>
                <span class="arabic-text">{{ __('Featured') }}</span>
            </div>
        </div>
    @endif

    {{-- شارات الأسعار والعروض (مبسطة) --}}
    <div class="price-badges-group">
        @if(isset($priorityBadges['free']))
            <div class="feature-badge free-badge" title="مجاني تماماً">
                <i class="fas fa-gift me-1"></i>
                <span class="arabic-text">{{ __('Free') }}</span>
            </div>
        @endif
    </div>

    {{-- تم حذف شارات الميزات الإضافية لتبسيط التصميم --}}

    {{-- إزالة شارات التحذيرات الزمنية (غير ضرورية) --}}
    {{-- تم حذف شارات "Discount Ending" و "Ending Soon" لتبسيط التصميم --}}

    {{-- شارات الإنجاز والحالة الإيجابية --}}
    @if(!empty($achievements) && !isset($priorityBadges['critical']))
        <div class="achievement-badges-group">
            @if(isset($achievements['new']))
                <div class="achievement-badge new-badge" title="إعلان جديد منشور خلال آخر 3 أيام">
                    <i class="fas fa-star me-1"></i>
                    <span class="arabic-text">{{ __('New') }}</span>
                </div>
            @endif

            @if(isset($achievements['popular']) && $detailed)
                <div class="achievement-badge popular-badge" title="إعلان شائع - {{ number_format($ad->views_count) }} مشاهدة">
                    <i class="fas fa-trending-up me-1"></i>
                    <span class="arabic-text">{{ __('Trending') }}</span>
                </div>
            @endif
        </div>
    @endif
</div>

{{-- تعليق توضيحي للمطورين --}}
{{--
    نظام الشارات المحسّن:
    1. يتجنب التكرارات والتداخلات
    2. يعرض الشارات حسب الأولوية والأهمية
    3. يحد من عدد الشارات المعروضة لتجنب الإرباك
    4. يستخدم أيقونات مختلفة لتجنب التشابه البصري
    5. يوفر وضع مفصل لعرض المزيد من الشارات عند الحاجة
--}}


