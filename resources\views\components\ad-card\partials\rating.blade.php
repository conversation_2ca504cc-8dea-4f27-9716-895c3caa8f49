{{-- قسم التقييم والنجوم --}}
@if($config['showRating'] ?? true)
    <div class="rating-section mb-2">
        @php
            // استخدام النظام الجديد للتقييمات
            $averageRating = $ad->rating_average ?? 0;
            $ratingsCount = $ad->ratings_count ?? 0;
            $userRating = null;

            // الحصول على تقييم المستخدم الحالي من النظام الجديد
            if (auth()->check()) {
                $userInteraction = \App\Models\Interaction::where([
                    'user_id' => auth()->id(),
                    'ad_id' => $ad->id,
                    'type' => 'rating'
                ])->first();

                if ($userInteraction && isset($userInteraction->data['rating'])) {
                    $userRating = (object) [
                        'rating' => $userInteraction->data['rating'],
                        'comment' => $userInteraction->data['comment'] ?? null
                    ];
                }
            }
        @endphp

        <div class="d-flex align-items-center justify-content-between">
            <!-- عرض النجوم والمتوسط -->
            <div class="rating-display d-flex align-items-center">
                <!-- النجوم -->
                <div class="stars-display me-2" data-rating="{{ $averageRating }}">
                    @for($i = 1; $i <= 5; $i++)
                        @if($i <= floor($averageRating))
                            <i class="fas fa-star text-warning"></i>
                        @elseif($i - 0.5 <= $averageRating)
                            <i class="fas fa-star-half-alt text-warning"></i>
                        @else
                            <i class="far fa-star text-muted"></i>
                        @endif
                    @endfor
                </div>

                <!-- المتوسط والعدد -->
                <div class="rating-info">
                    <span class="rating-average fw-bold text-dark">{{ number_format($averageRating, 1) }}</span>
                    <small class="text-muted">
                        ({{ $ratingsCount }} {{ $ratingsCount == 1 ? __('review') : __('reviews') }})
                    </small>
                </div>
            </div>

            <!-- زر إضافة تقييم -->
            @auth
                <div class="rating-action">
                    @if($userRating)
                        <!-- المستخدم قيّم مسبقاً -->
                        <button class="btn btn-outline-success btn-sm" 
                                onclick="showUserRating({{ $ad->id }})"
                                title="{{ __('You rated this ad') }}">
                            <i class="fas fa-check me-1"></i>
                            {{ $userRating->rating }}
                        </button>
                    @else
                        <!-- المستخدم لم يقيّم بعد -->
                        <button class="btn btn-outline-primary btn-sm" 
                                onclick="openRatingModal({{ $ad->id }})"
                                title="{{ __('Rate this ad') }}">
                            <i class="fas fa-star me-1"></i>
                            {{ __('Rate') }}
                        </button>
                    @endif
                </div>
            @else
                <!-- غير مسجل الدخول -->
                <div class="rating-action">
                    <small class="text-muted">
                        <a href="{{ route('login') }}" class="text-decoration-none">
                            {{ __('Login to rate') }}
                        </a>
                    </small>
                </div>
            @endauth
        </div>

        <!-- شريط التقييم المفصل (اختياري) -->
        @if($config['showDetailedRating'] ?? false)
            <div class="rating-breakdown mt-2">
                @php $distribution = $ad->rating_distribution; @endphp
                @for($i = 5; $i >= 1; $i--)
                    <div class="rating-bar d-flex align-items-center mb-1">
                        <span class="rating-label me-2">{{ $i }}</span>
                        <i class="fas fa-star text-warning me-1"></i>
                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                            @php
                                $percentage = $ratingsCount > 0 ? ($distribution[$i] / $ratingsCount) * 100 : 0;
                            @endphp
                            <div class="progress-bar bg-warning" 
                                 style="width: {{ $percentage }}%"></div>
                        </div>
                        <small class="text-muted">{{ $distribution[$i] }}</small>
                    </div>
                @endfor
            </div>
        @endif
    </div>
@endif

{{-- Modal التقييم --}}
@auth
<div class="modal fade" id="ratingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Rate this ad') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="ratingForm">
                    <input type="hidden" id="ratingAdId" name="ad_id">
                    
                    <!-- النجوم التفاعلية -->
                    <div class="mb-3">
                        <label class="form-label">{{ __('Your rating') }}</label>
                        <div class="interactive-stars" id="interactiveStars">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="far fa-star star-interactive" 
                                   data-rating="{{ $i }}"
                                   onclick="setRating({{ $i }})"></i>
                            @endfor
                        </div>
                        <input type="hidden" id="selectedRating" name="rating" required>
                        <small class="text-muted">{{ __('Click on stars to rate') }}</small>
                    </div>

                    <!-- التعليق -->
                    <div class="mb-3">
                        <label for="ratingComment" class="form-label">{{ __('Comment') }} ({{ __('optional') }})</label>
                        <textarea class="form-control" 
                                  id="ratingComment" 
                                  name="comment" 
                                  rows="3" 
                                  placeholder="{{ __('Share your experience with this ad...') }}"></textarea>
                    </div>

                    <!-- تقييمات فرعية -->
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">{{ __('Quality') }}</label>
                            <select class="form-select form-select-sm" name="quality_rating">
                                <option value="">{{ __('Select') }}</option>
                                <option value="excellent">{{ __('Excellent') }}</option>
                                <option value="good">{{ __('Good') }}</option>
                                <option value="average">{{ __('Average') }}</option>
                                <option value="poor">{{ __('Poor') }}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{{ __('Price') }}</label>
                            <select class="form-select form-select-sm" name="price_rating">
                                <option value="">{{ __('Select') }}</option>
                                <option value="excellent">{{ __('Excellent') }}</option>
                                <option value="good">{{ __('Good') }}</option>
                                <option value="average">{{ __('Average') }}</option>
                                <option value="poor">{{ __('Poor') }}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{{ __('Service') }}</label>
                            <select class="form-select form-select-sm" name="service_rating">
                                <option value="">{{ __('Select') }}</option>
                                <option value="excellent">{{ __('Excellent') }}</option>
                                <option value="good">{{ __('Good') }}</option>
                                <option value="average">{{ __('Average') }}</option>
                                <option value="poor">{{ __('Poor') }}</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="submitRating()">{{ __('Submit Rating') }}</button>
            </div>
        </div>
    </div>
</div>
@endauth

<style>
/* تنسيق النجوم */
.stars-display i {
    font-size: 1rem;
    margin-right: 2px;
}

.interactive-stars {
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

.star-interactive {
    cursor: pointer;
    margin-right: 5px;
    transition: all 0.2s ease;
    color: #ddd;
}

.star-interactive:hover,
.star-interactive.active {
    color: #ffc107;
    transform: scale(1.1);
}

.rating-breakdown .progress {
    height: 8px;
    background-color: #f8f9fa;
}

.rating-breakdown .rating-label {
    min-width: 15px;
    font-size: 0.875rem;
}

.rating-action .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}
</style>

<script>
// متغيرات التقييم
let currentAdId = null;
let selectedRating = 0;

// فتح modal التقييم
function openRatingModal(adId) {
    currentAdId = adId;
    document.getElementById('ratingAdId').value = adId;
    resetRatingForm();
    
    const modal = new bootstrap.Modal(document.getElementById('ratingModal'));
    modal.show();
}

// إعادة تعيين النموذج
function resetRatingForm() {
    selectedRating = 0;
    document.getElementById('selectedRating').value = '';
    document.getElementById('ratingComment').value = '';
    
    // إعادة تعيين النجوم
    document.querySelectorAll('.star-interactive').forEach(star => {
        star.classList.remove('active');
        star.classList.remove('fas');
        star.classList.add('far');
    });
    
    // إعادة تعيين التقييمات الفرعية
    document.querySelectorAll('select[name$="_rating"]').forEach(select => {
        select.value = '';
    });
}

// تعيين التقييم
function setRating(rating) {
    selectedRating = rating;
    document.getElementById('selectedRating').value = rating;
    
    // تحديث النجوم
    document.querySelectorAll('.star-interactive').forEach((star, index) => {
        if (index < rating) {
            star.classList.remove('far');
            star.classList.add('fas', 'active');
        } else {
            star.classList.remove('fas', 'active');
            star.classList.add('far');
        }
    });
}

// إرسال التقييم
function submitRating() {
    if (selectedRating === 0) {
        alert('{{ __("Please select a rating") }}');
        return;
    }
    
    const formData = new FormData(document.getElementById('ratingForm'));
    
    fetch('{{ route("interactions.rating.add", ":adId") }}'.replace(':adId', currentAdId), {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق المودال
            bootstrap.Modal.getInstance(document.getElementById('ratingModal')).hide();
            
            // تحديث واجهة التقييم
            updateRatingDisplay(currentAdId, data.rating);
            
            // إظهار رسالة نجاح
            if (typeof showToast === 'function') {
                showToast(data.message, 'success');
            } else {
                alert(data.message);
            }
        } else {
            alert(data.message || '{{ __("Error submitting rating") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("Unexpected error occurred") }}');
    });
}

// تحديث عرض التقييم
function updateRatingDisplay(adId, ratingData) {
    // يمكن إضافة منطق تحديث الواجهة هنا
    location.reload(); // حل مؤقت
}

// عرض تقييم المستخدم
function showUserRating(adId) {
    // يمكن إضافة منطق عرض تفاصيل التقييم
    alert('{{ __("You have already rated this ad") }}');
}
</script>
