
<div class="card-footer bg-transparent border-0 pt-0">
    <?php if($config['showActions']): ?>
        <!-- أزرار الإجراءات -->
        <div class="action-buttons d-flex justify-content-between align-items-center">
            <div class="left-actions">
                <a href="<?php echo e(route('ads.show', [$ad->category->slug ?? 'general', $ad->slug])); ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye me-1"></i>
                    <?php echo e(__('View Details')); ?>

                </a>
            </div>

            <div class="right-actions">
                <!-- زر المفضلة -->
                <?php if($config['showFavorites'] ?? true): ?>
                    <?php if(auth()->guard()->check()): ?>
                        <div class="favorite-icon" data-ad-id="<?php echo e($ad->id); ?>"
                             <?php if(isset($favorite)): ?> data-favorite-id="<?php echo e($favorite->id); ?>" <?php endif; ?>>
                            <button class="btn btn-outline-danger btn-sm btn-favorite me-1"
                                    title="<?php echo e(__('Add to Favorites')); ?>">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- زر المقارنة -->
                <button class="btn btn-outline-info btn-sm compare-btn"
                        data-ad-id="<?php echo e($ad->id); ?>"
                        onclick="toggleCompare(<?php echo e($ad->id); ?>)"
                        title="<?php echo e(__('Add to Compare')); ?>">
                    <i class="fas fa-balance-scale"></i>
                </button>

                <!-- زر المشاركة -->
                <button class="btn btn-outline-secondary btn-sm ms-1"
                        onclick="shareAd('<?php echo e(route('ads.show', [$ad->category->slug ?? 'general', $ad->slug])); ?>', '<?php echo e($ad->title); ?>')"
                        title="<?php echo e(__('Share')); ?>">
                    <i class="fas fa-share-alt"></i>
                </button>

                
            </div>
        </div>
    <?php endif; ?>

    <?php if($config['showExpiry'] || ($config['variant'] === 'favorites')): ?>
        <!-- معلومات انتهاء الصلاحية ومعلومات المفضلة -->
        <div class="footer-info mt-2">
            <?php if($config['showExpiry'] && $ad->expires_at): ?>
                <?php
                    $daysLeft = $ad->expires_at->diffInDays(now());
                    $isExpiringSoon = $daysLeft <= 7;
                ?>

                <small class="text-<?php echo e($isExpiringSoon ? 'danger' : 'muted'); ?> d-block">
                    <i class="fas fa-calendar-times me-1"></i>
                    <?php if($daysLeft > 0): ?>
                        <?php echo e(__('Expires in :days days', ['days' => $daysLeft])); ?>

                    <?php else: ?>
                        <?php echo e(__('Expired')); ?>

                    <?php endif; ?>
                </small>
            <?php endif; ?>

            
            <?php if($config['variant'] === 'favorites' && isset($config['favoriteId'])): ?>
                <?php
                    // الحصول على تاريخ إضافة المفضلة الحقيقي من جدول interactions
                    $favorite = \App\Models\Interaction::find($config['favoriteId']);
                    $addedDate = $favorite ? $favorite->created_at : now();
                ?>
                <small class="text-success d-block mt-1">
                    <i class="fas fa-heart text-danger me-1"></i>
                    <?php echo e(__('Added to favorites')); ?> <?php echo e($addedDate->diffForHumans()); ?>

                </small>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div><?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/partials/footer.blade.php ENDPATH**/ ?>