{{-- مكون إحصائيات التفاعل والمفضلة المتقدم --}}
@props(['ad', 'detailed' => true, 'showCharts' => true])

{{-- إضافة Logging لعرض إحصائيات التفاعل --}}
@php
    if ($detailed) {
        Log::info('عرض إحصائيات التفاعل والمفضلة المتقدمة', [
            'ad_id' => $ad->id,
            'ad_title' => $ad->title,
            'views_count' => $ad->views_count,
            'favorites_count' => $ad->favorites_count,
            'ratings_count' => $ad->ratings_count,
            'comments_count' => $ad->comments_count,
            'contact_reveals_count' => $ad->contact_reveals_count,
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'timestamp' => now()
        ]);
    }
    
    // جمع الإحصائيات
    $stats = [
        'views' => $ad->views_count ?? 0,
        'favorites' => $ad->favorites_count ?? 0,
        'ratings' => $ad->ratings_count ?? 0,
        'comments' => $ad->comments_count ?? 0,
        'contact_reveals' => $ad->contact_reveals_count ?? 0,
        'average_rating' => $ad->average_rating ?? 0,
    ];
    
    // حساب إجمالي التفاعلات
    $totalInteractions = $stats['favorites'] + $stats['ratings'] + $stats['comments'] + $stats['contact_reveals'];
    
    // حساب معدل التفاعل (نسبة التفاعل إلى المشاهدات)
    $engagementRate = $stats['views'] > 0 ? round(($totalInteractions / $stats['views']) * 100, 1) : 0;
    
    // الحصول على المستخدمين الذين أضافوا الإعلان للمفضلة (آخر 5)
    $recentFavorites = $ad->favoritedByUsers()->limit(5)->get();
    
    // إحصائيات زمنية (آخر 7 أيام)
    $weeklyStats = [
        'views_trend' => 'up', // يمكن حسابها من قاعدة البيانات
        'favorites_trend' => 'up',
        'engagement_trend' => 'stable'
    ];
@endphp

<div class="interaction-stats-section {{ $detailed ? 'stats-detailed' : 'stats-compact' }}">
    {{-- رأس قسم الإحصائيات --}}
    <div class="stats-header">
        <h4 class="stats-title">
            <i class="fas fa-chart-line text-success me-2"></i>
            {{ __('Interaction Statistics') }}
        </h4>
        <div class="stats-summary">
            <span class="engagement-rate">
                <i class="fas fa-percentage me-1"></i>
                {{ $engagementRate }}% {{ __('Engagement Rate') }}
            </span>
        </div>
    </div>

    {{-- الإحصائيات الرئيسية --}}
    <div class="main-stats">
        <div class="row">
            {{-- المشاهدات --}}
            <div class="col-md-3 col-6">
                <div class="stat-card views-card">
                    <div class="stat-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($stats['views']) }}</div>
                        <div class="stat-label">{{ __('Views') }}</div>
                        <div class="stat-trend trend-{{ $weeklyStats['views_trend'] }}">
                            <i class="fas fa-arrow-{{ $weeklyStats['views_trend'] === 'up' ? 'up' : ($weeklyStats['views_trend'] === 'down' ? 'down' : 'right') }}"></i>
                            <span>{{ __('This Week') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            {{-- المفضلة --}}
            <div class="col-md-3 col-6">
                <div class="stat-card favorites-card">
                    <div class="stat-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($stats['favorites']) }}</div>
                        <div class="stat-label">{{ __('Favorites') }}</div>
                        <div class="stat-trend trend-{{ $weeklyStats['favorites_trend'] }}">
                            <i class="fas fa-arrow-{{ $weeklyStats['favorites_trend'] === 'up' ? 'up' : ($weeklyStats['favorites_trend'] === 'down' ? 'down' : 'right') }}"></i>
                            <span>{{ __('This Week') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            {{-- التقييمات --}}
            <div class="col-md-3 col-6">
                <div class="stat-card ratings-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($stats['ratings']) }}</div>
                        <div class="stat-label">{{ __('Ratings') }}</div>
                        <div class="stat-average">
                            @if($stats['average_rating'] > 0)
                                <div class="rating-display">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $stats['average_rating'] ? 'text-warning' : 'text-muted' }}"></i>
                                    @endfor
                                    <span class="ms-1">{{ number_format($stats['average_rating'], 1) }}</span>
                                </div>
                            @else
                                <span class="text-muted">{{ __('No ratings yet') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            {{-- التعليقات --}}
            <div class="col-md-3 col-6">
                <div class="stat-card comments-card">
                    <div class="stat-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($stats['comments']) }}</div>
                        <div class="stat-label">{{ __('Comments') }}</div>
                        <div class="stat-trend trend-{{ $weeklyStats['engagement_trend'] }}">
                            <i class="fas fa-arrow-{{ $weeklyStats['engagement_trend'] === 'up' ? 'up' : ($weeklyStats['engagement_trend'] === 'down' ? 'down' : 'right') }}"></i>
                            <span>{{ __('Active') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- إحصائيات إضافية --}}
    <div class="additional-stats">
        <div class="row">
            {{-- كشف معلومات التواصل --}}
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-icon-small">
                        <i class="fas fa-phone text-primary"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-value">{{ number_format($stats['contact_reveals']) }}</div>
                        <div class="stat-name">{{ __('Contact Reveals') }}</div>
                    </div>
                </div>
            </div>

            {{-- معدل التفاعل --}}
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-icon-small">
                        <i class="fas fa-chart-pie text-info"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-value">{{ $engagementRate }}%</div>
                        <div class="stat-name">{{ __('Engagement Rate') }}</div>
                    </div>
                </div>
            </div>

            {{-- إجمالي التفاعلات --}}
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-icon-small">
                        <i class="fas fa-users text-success"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-value">{{ number_format($totalInteractions) }}</div>
                        <div class="stat-name">{{ __('Total Interactions') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- الرسم البياني البسيط --}}
    @if($showCharts && $detailed)
        <div class="stats-chart">
            <h5 class="chart-title">
                <i class="fas fa-chart-bar me-2"></i>
                {{ __('Interaction Breakdown') }}
            </h5>
            <div class="chart-container">
                <div class="chart-bars">
                    @php
                        $maxValue = max($stats['views'], $stats['favorites'], $stats['ratings'], $stats['comments']);
                        $maxValue = $maxValue > 0 ? $maxValue : 1;
                    @endphp
                    
                    <div class="chart-bar">
                        <div class="bar-fill views-bar" style="height: {{ ($stats['views'] / $maxValue) * 100 }}%"></div>
                        <div class="bar-label">{{ __('Views') }}</div>
                        <div class="bar-value">{{ number_format($stats['views']) }}</div>
                    </div>
                    
                    <div class="chart-bar">
                        <div class="bar-fill favorites-bar" style="height: {{ ($stats['favorites'] / $maxValue) * 100 }}%"></div>
                        <div class="bar-label">{{ __('Favorites') }}</div>
                        <div class="bar-value">{{ number_format($stats['favorites']) }}</div>
                    </div>
                    
                    <div class="chart-bar">
                        <div class="bar-fill ratings-bar" style="height: {{ ($stats['ratings'] / $maxValue) * 100 }}%"></div>
                        <div class="bar-label">{{ __('Ratings') }}</div>
                        <div class="bar-value">{{ number_format($stats['ratings']) }}</div>
                    </div>
                    
                    <div class="chart-bar">
                        <div class="bar-fill comments-bar" style="height: {{ ($stats['comments'] / $maxValue) * 100 }}%"></div>
                        <div class="bar-label">{{ __('Comments') }}</div>
                        <div class="bar-value">{{ number_format($stats['comments']) }}</div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- المستخدمون الذين أضافوا للمفضلة --}}
    @if($recentFavorites->count() > 0 && $detailed)
        <div class="recent-favorites">
            <h5 class="section-title">
                <i class="fas fa-heart text-danger me-2"></i>
                {{ __('Recently Added to Favorites') }}
            </h5>
            <div class="favorites-users">
                @foreach($recentFavorites as $user)
                    <div class="favorite-user">
                        <div class="user-avatar-xs">
                            @if($user->avatar)
                                <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" class="avatar-xs">
                            @else
                                <div class="avatar-placeholder-xs">
                                    <i class="fas fa-user"></i>
                                </div>
                            @endif
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ $user->name }}</div>
                            <div class="favorite-date">{{ $user->pivot->created_at->diffForHumans() }}</div>
                        </div>
                    </div>
                @endforeach
                
                @if($stats['favorites'] > 5)
                    <div class="more-favorites">
                        <span class="more-count">+{{ $stats['favorites'] - 5 }}</span>
                        <span class="more-text">{{ __('more users') }}</span>
                    </div>
                @endif
            </div>
        </div>
    @endif

    {{-- أزرار الإجراءات --}}
    @if($detailed)
        <div class="stats-actions">
            <div class="d-flex justify-content-between align-items-center">
                <div class="action-buttons">
                    @auth
                        <button class="btn btn-outline-primary btn-sm" onclick="InteractionStats.exportStats({{ $ad->id }})">
                            <i class="fas fa-download me-1"></i>
                            {{ __('Export Stats') }}
                        </button>
                        
                        @if(auth()->id() === $ad->user_id)
                            <button class="btn btn-outline-info btn-sm ms-2" onclick="InteractionStats.viewDetailedAnalytics({{ $ad->id }})">
                                <i class="fas fa-chart-line me-1"></i>
                                {{ __('Detailed Analytics') }}
                            </button>
                        @endif
                    @endauth
                </div>
                
                <div class="last-updated">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        {{ __('Updated') }} {{ now()->diffForHumans() }}
                    </small>
                </div>
            </div>
        </div>
    @endif
</div>
