<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Models\Interaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/**
 * Controller المفضلة - محدث للهيكل الجديد
 * يستخدم جدول interactions بدلاً من favorites
 */
class FavoriteController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * عرض قائمة المفضلة للمستخدم الحالي
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // تحديد عدد العناصر في الصفحة
        $perPage = $request->get('per_page', 12);
        $perPage = in_array($perPage, [12, 24, 48]) ? $perPage : 12;

        // الحصول على التفاعلات من نوع favorite مع الإعلانات
        $favoritesQuery = Interaction::where([
            'user_id' => $user->id,
            'type' => 'favorite'
        ])
        ->with(['ad' => function($query) {
            $query->with(['category', 'user'])
                  ->where('status', 'active')
                  ->where(function($q) {
                      $q->whereNull('expires_at')
                        ->orWhere('expires_at', '>', now());
                  });
        }])
        ->orderBy('created_at', 'desc');

        // تطبيق الفلاتر
        if ($request->filled('category')) {
            $favoritesQuery->whereHas('ad', function($query) use ($request) {
                $query->where('category_id', $request->category);
            });
        }

        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $favoritesQuery->whereHas('ad', function($query) use ($searchTerm) {
                $query->where(function($q) use ($searchTerm) {
                    $q->where('title_ar', 'like', "%{$searchTerm}%")
                      ->orWhere('title_en', 'like', "%{$searchTerm}%")
                      ->orWhere('description_ar', 'like', "%{$searchTerm}%")
                      ->orWhere('description_en', 'like', "%{$searchTerm}%");
                });
            });
        }

        $favorites = $favoritesQuery->paginate($perPage);

        // فلترة الإعلانات المحذوفة أو المنتهية الصلاحية
        $favorites->getCollection()->transform(function ($favorite) {
            if (!$favorite->ad || $favorite->ad->status !== 'active') {
                return null;
            }
            return $favorite;
        })->filter();

        // إحصائيات المفضلة
        $stats = [
            'total_favorites' => Interaction::where([
                'user_id' => $user->id,
                'type' => 'favorite'
            ])->count(),
            'active_favorites' => $favorites->total(),
            'categories_count' => Interaction::where([
                'user_id' => $user->id,
                'type' => 'favorite'
            ])
            ->whereHas('ad', function($query) {
                $query->where('status', 'active');
            })
            ->distinct('ad_id')
            ->count(),
        ];

        // الحصول على التصنيفات للفلتر
        $categories = \App\Models\Category::active()
            ->whereHas('ads', function($query) use ($user) {
                $query->whereIn('id', function($subQuery) use ($user) {
                    $subQuery->select('ad_id')
                             ->from('interactions')
                             ->where('user_id', $user->id)
                             ->where('type', 'favorite');
                });
            })
            ->get();

        return view('favorites.index', compact('favorites', 'stats', 'categories', 'perPage'));
    }

    /**
     * إضافة إعلان للمفضلة
     */
    public function store(Request $request, $adId)
    {
        try {
            $ad = Ad::findOrFail($adId);
            $user = Auth::user();

            // التحقق من عدم وجود الإعلان في المفضلة مسبقاً
            $existingFavorite = Interaction::where([
                'user_id' => $user->id,
                'ad_id' => $adId,
                'type' => 'favorite'
            ])->first();

            if ($existingFavorite) {
                return response()->json([
                    'success' => false,
                    'message' => 'الإعلان موجود في المفضلة مسبقاً'
                ]);
            }

            // إضافة للمفضلة
            Interaction::addFavorite($user->id, $adId);

            // تحديث عداد المفضلة في الإعلان
            $ad->increment('favorites_count');

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة الإعلان للمفضلة',
                'favorites_count' => $ad->fresh()->favorites_count
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في العملية'
            ], 500);
        }
    }

    /**
     * إزالة إعلان من المفضلة
     */
    public function destroy($favoriteId)
    {
        try {
            $user = Auth::user();
            
            // البحث عن التفاعل
            $favorite = Interaction::where([
                'id' => $favoriteId,
                'user_id' => $user->id,
                'type' => 'favorite'
            ])->first();

            if (!$favorite) {
                return response()->json([
                    'success' => false,
                    'message' => 'الإعلان غير موجود في المفضلة'
                ]);
            }

            $adId = $favorite->ad_id;
            $favorite->delete();

            // تحديث عداد المفضلة في الإعلان
            $ad = Ad::find($adId);
            if ($ad) {
                $ad->decrement('favorites_count');
            }

            return response()->json([
                'success' => true,
                'message' => 'تم إزالة الإعلان من المفضلة'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في العملية'
            ], 500);
        }
    }

    /**
     * مسح جميع المفضلة
     */
    public function clear()
    {
        try {
            $user = Auth::user();
            
            $deletedCount = Interaction::where([
                'user_id' => $user->id,
                'type' => 'favorite'
            ])->delete();

            return response()->json([
                'success' => true,
                'message' => "تم مسح {$deletedCount} إعلان من المفضلة"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في العملية'
            ], 500);
        }
    }
}
