/**
 * نظام المفضلة - JavaScript
 * يدير التفاعل مع نظام المفضلة عبر AJAX
 */

// متغيرات عامة
let isProcessing = false;
let lastRequestTime = 0;
const REQUEST_DEBOUNCE_TIME = 500; // نصف ثانية بين الطلبات
const CSRF_TOKEN = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

// تهيئة نظام المفضلة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeFavorites();
});

/**
 * تهيئة نظام المفضلة
 */
function initializeFavorites() {
    // إضافة مستمع الأحداث لجميع أزرار المفضلة مع حماية من التكرار
    document.querySelectorAll('.btn-favorite').forEach(function(button) {
        // التحقق من عدم وجود event listener مسبقاً
        if (!button.hasAttribute('data-favorites-initialized')) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const favoriteIcon = this.closest('.favorite-icon');
                const adId = favoriteIcon.dataset.adId;

                // في صفحة المفضلة، لا نستخدم النظام العادي
                // لأن هناك دوال مخصصة في الصفحة
                if (window.location.pathname.includes('/favorites')) {
                    // لا نفعل شيء هنا، الصفحة تدير الأحداث بنفسها
                    return;
                } else {
                    // الاستخدام العادي للتبديل
                    toggleFavorite(adId, this);
                }
            });

            // وضع علامة للإشارة إلى أن الزر تم تهيئته
            button.setAttribute('data-favorites-initialized', 'true');
        }
    });

    // تحديث حالة المفضلة للإعلانات المعروضة
    updateFavoritesStatus();
}

/**
 * تبديل حالة المفضلة
 */
function toggleFavorite(adId, button) {
    // التحقق من تسجيل الدخول
    if (!isUserAuthenticated()) {
        showToast(window.translations?.must_login_to_add_favorites || 'يجب تسجيل الدخول أولاً لإضافة الإعلانات إلى المفضلة', 'warning');
        return;
    }

    // منع التكرار والطلبات السريعة المتتالية
    const currentTime = Date.now();
    if (isProcessing || (currentTime - lastRequestTime) < REQUEST_DEBOUNCE_TIME) {
        return;
    }

    isProcessing = true;
    lastRequestTime = currentTime;
    
    // إظهار حالة التحميل
    setButtonLoading(button, true);
    
    // إرسال طلب AJAX
    fetch('/dashboard/api/favorites/toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': CSRF_TOKEN,
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            ad_id: parseInt(adId)
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // تحديث حالة الزر
            updateButtonState(button, data.is_favorited);
            
            // إظهار رسالة نجاح
            showToast(data.message, 'success');
            
            // تحديث عداد المفضلة
            updateFavoritesCount(data.favorites_count);
            
            // تحديث الإحصائيات إذا كانت موجودة
            updateFavoritesStats();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في تبديل المفضلة:', error);
        showToast(window.translations?.unexpected_error_occurred || 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى', 'error');
    })
    .finally(() => {
        // إعادة تفعيل الزر
        setButtonLoading(button, false);
        isProcessing = false;
    });
}

/**
 * إضافة إعلان إلى المفضلة
 */
function addToFavorites(adId, callback) {
    if (!isUserAuthenticated()) {
        showToast(window.translations?.must_login_first || 'يجب تسجيل الدخول أولاً', 'warning');
        return;
    }

    fetch('/dashboard/favorites', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': CSRF_TOKEN,
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            ad_id: parseInt(adId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            updateFavoritesCount(data.favorites_count);
        } else {
            showToast(data.message, 'error');
        }
        
        if (callback) callback(data);
    })
    .catch(error => {
        console.error('خطأ في إضافة المفضلة:', error);
        showToast(window.translations?.unexpected_error || 'حدث خطأ غير متوقع', 'error');
        if (callback) callback({ success: false });
    });
}

/**
 * إزالة إعلان من المفضلة
 */
function removeFromFavorites(adId, callback) {
    fetch(`/dashboard/favorites/${adId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': CSRF_TOKEN,
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            updateFavoritesCount(data.favorites_count);
        } else {
            showToast(data.message, 'error');
        }
        
        if (callback) callback(data);
    })
    .catch(error => {
        console.error('خطأ في إزالة المفضلة:', error);
        showToast(window.translations?.unexpected_error || 'حدث خطأ غير متوقع', 'error');
        if (callback) callback({ success: false });
    });
}

/**
 * التحقق من حالة الإعلان في المفضلة
 */
function checkFavoriteStatus(adId, callback) {
    fetch(`/dashboard/favorites/check/${adId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (callback) callback(data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من حالة المفضلة:', error);
        if (callback) callback({ success: false, is_favorited: false });
    });
}

/**
 * تحديث حالة الزر
 */
function updateButtonState(button, isFavorited) {
    if (isFavorited) {
        button.classList.add('favorited');
        button.title = window.translations?.remove_from_favorites || 'إزالة من المفضلة';
    } else {
        button.classList.remove('favorited');
        button.title = window.translations?.add_to_favorites || 'إضافة إلى المفضلة';
    }
}

/**
 * تعيين حالة التحميل للزر
 */
function setButtonLoading(button, isLoading) {
    if (isLoading) {
        button.classList.add('loading');
        button.setAttribute('data-processing', 'true');
        button.style.pointerEvents = 'none';
    } else {
        button.classList.remove('loading');
        button.removeAttribute('data-processing');
        button.style.pointerEvents = 'auto';
    }
}

/**
 * تحديث عداد المفضلة في جميع أنحاء الصفحة
 */
function updateFavoritesCount(count) {
    const counters = document.querySelectorAll('.favorites-count');
    counters.forEach(counter => {
        counter.textContent = count;
        
        // إخفاء العداد إذا كان الرقم صفر
        if (count === 0) {
            const badge = counter.closest('.badge');
            if (badge) {
                badge.style.display = 'none';
            }
        } else {
            const badge = counter.closest('.badge');
            if (badge) {
                badge.style.display = 'flex';
            }
        }
    });
}

/**
 * تحديث حالة المفضلة للإعلانات المعروضة - محسن
 */
function updateFavoritesStatus() {
    if (!isUserAuthenticated()) {
        return;
    }

    const favoriteButtons = document.querySelectorAll('.btn-favorite');
    const adIds = [];

    favoriteButtons.forEach(button => {
        const adId = button.closest('.favorite-icon')?.dataset.adId;
        if (adId && !adIds.includes(adId)) {
            adIds.push(adId);
        }
    });

    if (adIds.length === 0) {
        return;
    }

    // طلب واحد للحصول على جميع حالات المفضلة
    fetch('/favorites/check-multiple', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': CSRF_TOKEN,
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            ad_ids: adIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            favoriteButtons.forEach(button => {
                const adId = button.closest('.favorite-icon')?.dataset.adId;
                if (adId && data.favorites[adId] !== undefined) {
                    updateButtonState(button, data.favorites[adId]);
                }
            });
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث حالة المفضلة:', error);
        // في حالة الخطأ، استخدم الطريقة القديمة كبديل
        favoriteButtons.forEach(button => {
            const adId = button.closest('.favorite-icon')?.dataset.adId;
            if (adId) {
                checkFavoriteStatus(adId, (data) => {
                    if (data.success) {
                        updateButtonState(button, data.is_favorited);
                    }
                });
            }
        });
    });
}

/**
 * تحديث إحصائيات المفضلة
 */
function updateFavoritesStats() {
    if (!isUserAuthenticated()) {
        return;
    }

    fetch('/favorites/stats', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث الإحصائيات في الصفحة
            const statsElements = document.querySelectorAll('[data-favorites-stat]');
            statsElements.forEach(element => {
                const statType = element.dataset.favoritesStat;
                if (data.stats[statType] !== undefined) {
                    element.textContent = data.stats[statType];
                }
            });
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث إحصائيات المفضلة:', error);
    });
}

/**
 * التحقق من تسجيل دخول المستخدم
 */
function isUserAuthenticated() {
    return document.querySelector('meta[name="user-authenticated"]') !== null;
}

/**
 * استخدام نظام Toast الموحد
 * الدالة متوفرة عالمياً من components/toast.blade.php
 * تم حذف الدالة المكررة لتجنب التعارض
 */

/**
 * مسح جميع المفضلة (للاستخدام المستقبلي)
 */
function clearAllFavorites(callback) {
    if (!confirm(window.translations?.confirm_clear_all_favorites || 'هل أنت متأكد من مسح جميع الإعلانات المفضلة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    // يمكن إضافة endpoint لمسح جميع المفضلة لاحقاً
    showToast(window.translations?.feature_coming_soon || 'هذه الميزة ستكون متاحة قريباً', 'info');
}

/**
 * إزالة مفضلة من صفحة المفضلة مع تحديث الواجهة
 */
function removeFavoriteFromPage(adId, favoriteId, button) {
    if (!confirm(window.translations?.confirm_remove_favorite || 'هل أنت متأكد من إزالة هذا الإعلان من المفضلة؟')) {
        return;
    }

    const card = document.querySelector(`[data-favorite-id="${favoriteId}"]`);
    if (card) {
        card.style.opacity = '0.6';
        card.style.pointerEvents = 'none';
    }

    // استخدام دالة الحذف الموحدة
    removeFromFavorites(adId, function(data) {
        if (data.success) {
            // إزالة البطاقة من الواجهة بتأثير سلس
            if (card) {
                card.style.transition = 'all 0.4s ease';
                card.style.opacity = '0';
                card.style.transform = 'scale(0.8) translateY(-20px)';

                setTimeout(() => {
                    card.remove();

                    // تحديث العداد
                    updateFavoritesCount(data.favorites_count);

                    // إظهار رسالة فارغة إذا لم تعد هناك مفضلة
                    if (data.favorites_count === 0) {
                        showEmptyFavoritesMessage();
                    }
                }, 400);
            }
        } else {
            // إعادة تفعيل البطاقة في حالة الخطأ
            if (card) {
                card.style.opacity = '1';
                card.style.pointerEvents = 'auto';
            }
        }
    });
}

/**
 * إظهار رسالة المفضلة الفارغة
 */
function showEmptyFavoritesMessage() {
    const favoritesGrid = document.querySelector('#favorites-grid');
    if (favoritesGrid) {
        favoritesGrid.innerHTML = `
            <div class="col-12">
                <div class="empty-favorites text-center py-5">
                    <div class="empty-icon mb-4">
                        <i class="fas fa-heart text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                    </div>
                    <h4 class="text-muted mb-3">لا توجد إعلانات مفضلة</h4>
                    <p class="text-muted mb-4">لم تقم بإضافة أي إعلانات إلى المفضلة بعد</p>
                    <a href="/ads" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        تصفح الإعلانات
                    </a>
                </div>
            </div>
        `;
    }
}

/**
 * إعادة تهيئة المفضلة للعناصر الجديدة
 * مفيدة عند إضافة محتوى جديد للصفحة ديناميكياً
 */
function reinitializeFavorites() {
    initializeFavorites();
}

// تصدير الدوال للاستخدام العام
window.FavoritesSystem = {
    toggle: toggleFavorite,
    add: addToFavorites,
    remove: removeFromFavorites,
    removeFromPage: removeFavoriteFromPage,
    check: checkFavoriteStatus,
    updateCount: updateFavoritesCount,
    clearAll: clearAllFavorites,
    showToast: showToast,
    reinitialize: reinitializeFavorites,
    showEmptyMessage: showEmptyFavoritesMessage
};
