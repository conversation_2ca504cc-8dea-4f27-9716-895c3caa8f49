<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware لفرض استخدام HTTPS
 * يعيد توجيه جميع طلبات HTTP إلى HTTPS في بيئة الإنتاج
 */
class ForceHttps
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // فرض HTTPS فقط إذا كان مفعل في الإعدادات وليس في الاستضافة المشتركة
        if (config('app.force_https', false) &&
            config('app.env') === 'production' &&
            !$request->isSecure()) {

            // التحقق من وجود Load Balancer أو Proxy
            $forwardedProto = $request->header('X-Forwarded-Proto');
            $forwardedPort = $request->header('X-Forwarded-Port');

            // إذا كان هناك proxy يستخدم HTTPS
            if ($forwardedProto === 'https' || $forwardedPort === '443') {
                return $next($request);
            }

            // إعادة توجيه إلى HTTPS فقط إذا كان مدعوماً
            if (config('app.supports_https', false)) {
                $httpsUrl = 'https://' . $request->getHttpHost() . $request->getRequestUri();
                return redirect($httpsUrl, 301);
            }
        }

        return $next($request);
    }
}
