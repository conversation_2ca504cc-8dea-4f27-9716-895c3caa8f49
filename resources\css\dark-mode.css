/* ===== الوضع المظلم (Dark Mode) ===== */

/* متغيرات الألوان للوضع المظلم */
:root {
    /* الألوان الأساسية للوضع العادي */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* الوضع المظلم */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --border-color: #404040;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --accent-color: #4dabf7;
    --success-color: #51cf66;
    --warning-color: #ffd43b;
    --danger-color: #ff6b6b;
    --info-color: #22b8cf;
}

/* تطبيق الألوان على العناصر الأساسية */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* الخلفيات */
.bg-white {
    background-color: var(--bg-primary) !important;
}

.bg-light {
    background-color: var(--bg-secondary) !important;
}

.bg-secondary {
    background-color: var(--bg-tertiary) !important;
}

/* النصوص */
.text-dark {
    color: var(--text-primary) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

/* الحدود */
.border {
    border-color: var(--border-color) !important;
}

.border-top,
.border-bottom,
.border-left,
.border-right {
    border-color: var(--border-color) !important;
}

/* البطاقات */
.card {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

.card-header {
    background-color: var(--bg-tertiary);
    border-bottom-color: var(--border-color);
}

.card-footer {
    background-color: var(--bg-tertiary);
    border-top-color: var(--border-color);
}

/* النماذج */
.form-control {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.form-control:focus {
    background-color: var(--bg-secondary);
    border-color: var(--accent-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(70, 171, 247, 0.25);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-select {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* الأزرار */
.btn-outline-primary {
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-outline-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* الجداول */
.table {
    color: var(--text-primary);
}

.table-striped > tbody > tr:nth-of-type(odd) > td,
.table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: var(--bg-secondary);
}

.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
    background-color: var(--bg-tertiary);
}

/* شريط التنقل */
.navbar-light {
    background-color: var(--bg-secondary) !important;
}

.navbar-light .navbar-brand,
.navbar-light .navbar-nav .nav-link {
    color: var(--text-primary) !important;
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

.navbar-light .navbar-toggler {
    border-color: var(--border-color);
}

.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* القوائم المنسدلة */
.dropdown-menu {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

.dropdown-item {
    color: var(--text-primary);
}

.dropdown-item:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.dropdown-divider {
    border-top-color: var(--border-color);
}

/* التنبيهات */
.alert-primary {
    background-color: rgba(70, 171, 247, 0.1);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.alert-success {
    background-color: rgba(81, 207, 102, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(255, 212, 59, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.alert-danger {
    background-color: rgba(255, 107, 107, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

/* الشارات */
.badge-primary {
    background-color: var(--accent-color) !important;
}

.badge-success {
    background-color: var(--success-color) !important;
}

.badge-warning {
    background-color: var(--warning-color) !important;
}

.badge-danger {
    background-color: var(--danger-color) !important;
}

/* الروابط */
a {
    color: var(--accent-color);
}

a:hover {
    color: var(--accent-color);
    opacity: 0.8;
}

/* التذييل */
footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* زر تبديل الوضع المظلم */
.theme-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--accent-color);
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px var(--shadow-color);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px var(--shadow-color);
}

/* تأثيرات الانتقال */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* تحسينات للوضع المظلم */
[data-theme="dark"] img {
    opacity: 0.9;
}

[data-theme="dark"] .text-black {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .bg-dark {
    background-color: var(--bg-primary) !important;
}

/* تخصيص scrollbar للوضع المظلم */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
