<?php

namespace Tests\Unit;

use Tests\TestCase;

class HelperFunctionsTest extends TestCase
{
    /**
     * اختبار أن التطبيق يعمل في البيئة الصحيحة
     */
    public function test_application_environment()
    {
        $this->assertTrue(app()->environment(['local', 'testing']));
    }

    /**
     * اختبار أن الإعدادات الأساسية صحيحة
     */
    public function test_basic_configuration()
    {
        $this->assertEquals('MyAdsSite', config('app.name'));
        $this->assertEquals('ar', config('app.locale'));
        $this->assertEquals('Asia/Riyadh', config('app.timezone'));
    }

    /**
     * اختبار أن قاعدة البيانات متصلة
     */
    public function test_database_connection()
    {
        try {
            \DB::connection()->getPdo();
            $this->assertTrue(true);
        } catch (\Exception $e) {
            $this->markTestSkipped('Database connection not available');
        }
    }

    /**
     * اختبار أن الـ cache يعمل
     */
    public function test_cache_functionality()
    {
        $key = 'test_cache_key';
        $value = 'test_cache_value';

        cache()->put($key, $value, 60);

        $this->assertEquals($value, cache()->get($key));

        cache()->forget($key);
    }

    /**
     * اختبار أن الـ session يعمل
     */
    public function test_session_functionality()
    {
        $key = 'test_session_key';
        $value = 'test_session_value';

        session([$key => $value]);

        $this->assertEquals($value, session($key));

        session()->forget($key);
    }

    /**
     * اختبار أن الـ validation يعمل
     */
    public function test_validation_functionality()
    {
        $validator = validator(['email' => 'invalid-email'], [
            'email' => 'required|email'
        ]);

        $this->assertTrue($validator->fails());

        $validator = validator(['email' => '<EMAIL>'], [
            'email' => 'required|email'
        ]);

        $this->assertTrue($validator->passes());
    }

    /**
     * اختبار أن الـ localization يعمل
     */
    public function test_localization_functionality()
    {
        app()->setLocale('ar');
        $this->assertEquals('ar', app()->getLocale());

        app()->setLocale('en');
        $this->assertEquals('en', app()->getLocale());

        // إعادة تعيين للعربية
        app()->setLocale('ar');
    }

    /**
     * اختبار أن الـ encryption يعمل
     */
    public function test_encryption_functionality()
    {
        $original = 'test string';
        $encrypted = encrypt($original);
        $decrypted = decrypt($encrypted);

        $this->assertEquals($original, $decrypted);
        $this->assertNotEquals($original, $encrypted);
    }

    /**
     * اختبار أن الـ hashing يعمل
     */
    public function test_hashing_functionality()
    {
        $password = 'test_password';
        $hashed = \Hash::make($password);

        $this->assertTrue(\Hash::check($password, $hashed));
        $this->assertFalse(\Hash::check('wrong_password', $hashed));
    }

    /**
     * اختبار أن الـ URL generation يعمل
     */
    public function test_url_generation()
    {
        $url = url('/test');
        $this->assertStringContainsString('/test', $url);

        try {
            $route = route('home');
            $this->assertIsString($route);
        } catch (\Exception $e) {
            // إذا لم يكن route موجود، نتخطى الاختبار
            $this->markTestSkipped('Home route not defined');
        }
    }
}
