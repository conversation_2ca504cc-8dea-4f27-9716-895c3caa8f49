/**
 * ملف JavaScript للتفاعل مع معلومات صاحب الإعلان
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على وظائف التفاعل مع المستخدمين والإبلاغ والرسائل
 */

// متغيرات عامة
const AdvertiserInteractions = {
    // إعدادات التطبيق
    config: {
        apiTimeout: 10000,
        retryAttempts: 3,
        logLevel: 'info'
    },

    // حالة التطبيق
    state: {
        isInitialized: false,
        activeModals: new Set(),
        pendingRequests: new Map()
    },

    /**
     * تهيئة نظام التفاعل مع المستخدمين
     */
    init() {
        if (this.state.isInitialized) {
            console.warn('AdvertiserInteractions already initialized');
            return;
        }

        try {
            this.log('info', 'تهيئة نظام التفاعل مع المستخدمين');
            
            this.setupEventListeners();
            this.initializeCSRFToken();
            this.initializeModals();
            
            this.state.isInitialized = true;
            this.log('info', 'تم تهيئة نظام التفاعل مع المستخدمين بنجاح');
            
        } catch (error) {
            this.log('error', 'خطأ في تهيئة نظام التفاعل مع المستخدمين', error);
        }
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مراقبة أزرار الرسائل المباشرة
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick*="openDirectMessage"]')) {
                e.preventDefault();
                const button = e.target.closest('button');
                const userId = this.extractUserIdFromOnclick(button.getAttribute('onclick'));
                if (userId) {
                    this.openDirectMessage(userId);
                }
            }
        });

        // مراقبة أزرار الإبلاغ
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick*="reportUser"]')) {
                e.preventDefault();
                const button = e.target.closest('button');
                const userId = this.extractUserIdFromOnclick(button.getAttribute('onclick'));
                if (userId) {
                    this.reportUser(userId);
                }
            }
        });

        // مراقبة أزرار التصفية في صفحة إعلانات المستخدم
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-filter]')) {
                e.preventDefault();
                const button = e.target.closest('button');
                const filter = button.getAttribute('data-filter');
                this.filterUserAds(filter, button);
            }
        });
    },

    /**
     * تهيئة CSRF Token
     */
    initializeCSRFToken() {
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!this.csrfToken) {
            this.log('warn', 'CSRF token not found');
        }
    },

    /**
     * تهيئة النوافذ المنبثقة
     */
    initializeModals() {
        // إنشاء modal للرسائل المباشرة
        this.createDirectMessageModal();
        
        // إنشاء modal للإبلاغ
        this.createReportModal();
    },

    /**
     * استخراج معرف المستخدم من onclick
     */
    extractUserIdFromOnclick(onclickValue) {
        if (!onclickValue) return null;
        const match = onclickValue.match(/\d+/);
        return match ? match[0] : null;
    },

    /**
     * فتح نافذة الرسالة المباشرة
     */
    async openDirectMessage(userId) {
        try {
            this.log('info', 'فتح نافذة الرسالة المباشرة', { userId });
            
            // التحقق من تسجيل الدخول
            if (!this.isAuthenticated()) {
                this.showLoginRequired();
                return;
            }

            // إظهار النافذة المنبثقة
            const modal = document.getElementById('directMessageModal');
            if (modal) {
                // تحديث معرف المستخدم في النافذة
                modal.setAttribute('data-user-id', userId);
                
                // إظهار النافذة
                const bootstrapModal = new bootstrap.Modal(modal);
                bootstrapModal.show();
                
                this.state.activeModals.add('directMessage');
            }

        } catch (error) {
            this.log('error', 'خطأ في فتح نافذة الرسالة المباشرة', { userId, error: error.message });
            this.showToast('حدث خطأ في فتح نافذة الرسالة', 'error');
        }
    },

    /**
     * فتح نافذة الإبلاغ
     */
    async reportUser(userId) {
        try {
            this.log('info', 'فتح نافذة الإبلاغ', { userId });
            
            // التحقق من تسجيل الدخول
            if (!this.isAuthenticated()) {
                this.showLoginRequired();
                return;
            }

            // إظهار النافذة المنبثقة
            const modal = document.getElementById('reportModal');
            if (modal) {
                // تحديث معرف المستخدم في النافذة
                modal.setAttribute('data-user-id', userId);
                
                // إظهار النافذة
                const bootstrapModal = new bootstrap.Modal(modal);
                bootstrapModal.show();
                
                this.state.activeModals.add('report');
            }

        } catch (error) {
            this.log('error', 'خطأ في فتح نافذة الإبلاغ', { userId, error: error.message });
            this.showToast('حدث خطأ في فتح نافذة الإبلاغ', 'error');
        }
    },

    /**
     * تصفية إعلانات المستخدم
     */
    filterUserAds(filter, button) {
        try {
            this.log('info', 'تصفية إعلانات المستخدم', { filter });
            
            // تحديث حالة الأزرار
            document.querySelectorAll('[data-filter]').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            
            // تطبيق التصفية
            const adsContainer = document.getElementById('ads-container');
            if (adsContainer) {
                const ads = adsContainer.querySelectorAll('.col-lg-4');
                
                ads.forEach(ad => {
                    ad.style.display = 'block';
                    
                    // تطبيق التصفية حسب النوع
                    switch (filter) {
                        case 'recent':
                            // ترتيب حسب التاريخ (يمكن تحسينه)
                            break;
                        case 'popular':
                            // ترتيب حسب المشاهدات (يمكن تحسينه)
                            break;
                        case 'all':
                        default:
                            // عرض جميع الإعلانات
                            break;
                    }
                });
            }
            
            this.showToast(`تم تطبيق تصفية: ${this.getFilterLabel(filter)}`, 'success');

        } catch (error) {
            this.log('error', 'خطأ في تصفية الإعلانات', { filter, error: error.message });
            this.showToast('حدث خطأ في تصفية الإعلانات', 'error');
        }
    },

    /**
     * الحصول على تسمية التصفية
     */
    getFilterLabel(filter) {
        const labels = {
            'all': 'جميع الإعلانات',
            'recent': 'الأحدث',
            'popular': 'الأكثر مشاهدة'
        };
        return labels[filter] || filter;
    },

    /**
     * إنشاء نافذة الرسالة المباشرة
     */
    createDirectMessageModal() {
        const modalHtml = `
            <div class="modal fade" id="directMessageModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إرسال رسالة مباشرة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="directMessageForm">
                                <div class="mb-3">
                                    <label for="messageSubject" class="form-label">الموضوع</label>
                                    <input type="text" class="form-control" id="messageSubject" required>
                                </div>
                                <div class="mb-3">
                                    <label for="messageContent" class="form-label">الرسالة</label>
                                    <textarea class="form-control" id="messageContent" rows="4" required></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="AdvertiserInteractions.sendDirectMessage()">إرسال</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // إضافة النافذة إلى الصفحة إذا لم تكن موجودة
        if (!document.getElementById('directMessageModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }
    },

    /**
     * إنشاء نافذة الإبلاغ
     */
    createReportModal() {
        const modalHtml = `
            <div class="modal fade" id="reportModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">الإبلاغ عن المستخدم</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="reportForm">
                                <div class="mb-3">
                                    <label for="reportReason" class="form-label">سبب الإبلاغ</label>
                                    <select class="form-select" id="reportReason" required>
                                        <option value="">اختر السبب</option>
                                        <option value="spam">محتوى مزعج</option>
                                        <option value="fake">معلومات مزيفة</option>
                                        <option value="inappropriate">محتوى غير مناسب</option>
                                        <option value="scam">احتيال</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="reportDetails" class="form-label">تفاصيل الإبلاغ</label>
                                    <textarea class="form-control" id="reportDetails" rows="3" placeholder="اكتب تفاصيل إضافية..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-warning" onclick="AdvertiserInteractions.submitReport()">إرسال الإبلاغ</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // إضافة النافذة إلى الصفحة إذا لم تكن موجودة
        if (!document.getElementById('reportModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }
    },

    /**
     * إرسال الرسالة المباشرة
     */
    async sendDirectMessage() {
        // هذه دالة مؤقتة - يمكن تطويرها لاحقاً
        this.showToast('ميزة الرسائل المباشرة قيد التطوير', 'info');
        
        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('directMessageModal'));
        if (modal) {
            modal.hide();
        }
    },

    /**
     * إرسال الإبلاغ
     */
    async submitReport() {
        // هذه دالة مؤقتة - يمكن تطويرها لاحقاً
        this.showToast('تم إرسال الإبلاغ بنجاح. سيتم مراجعته قريباً.', 'success');
        
        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('reportModal'));
        if (modal) {
            modal.hide();
        }
    },

    /**
     * التحقق من تسجيل الدخول
     */
    isAuthenticated() {
        // يمكن تحسين هذه الدالة حسب نظام المصادقة المستخدم
        return document.querySelector('meta[name="user-authenticated"]')?.getAttribute('content') === 'true' ||
               document.querySelector('.user-menu') !== null;
    },

    /**
     * إظهار رسالة تطلب تسجيل الدخول
     */
    showLoginRequired() {
        this.showToast('يجب تسجيل الدخول أولاً للتفاعل مع المستخدمين', 'warning');
    },

    /**
     * إظهار رسالة Toast
     */
    showToast(message, type = 'info') {
        // استخدام نظام Toast الموحد إذا كان متوفراً
        if (typeof showToast === 'function') {
            showToast(message, type);
            return;
        }

        // إنشاء Toast مخصص
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    },

    /**
     * تسجيل الأحداث
     */
    log(level, message, data = null) {
        if (this.config.logLevel === 'none') return;

        const logData = {
            timestamp: new Date().toISOString(),
            component: 'AdvertiserInteractions',
            message,
            data
        };

        switch (level) {
            case 'error':
                console.error('[AdvertiserInteractions]', message, data);
                break;
            case 'warn':
                console.warn('[AdvertiserInteractions]', message, data);
                break;
            case 'info':
            default:
                console.log('[AdvertiserInteractions]', message, data);
                break;
        }
    }
};

// دوال عامة للتوافق مع الكود الموجود
window.openDirectMessage = function(userId) {
    AdvertiserInteractions.openDirectMessage(userId);
};

window.reportUser = function(userId) {
    AdvertiserInteractions.reportUser(userId);
};

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    AdvertiserInteractions.init();
});

// تصدير للاستخدام العام
window.AdvertiserInteractions = AdvertiserInteractions;
