@extends('layouts.app')

@section('title', __('Compare Ads'))

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="text-center">
                <!-- أيقونة كبيرة -->
                <div class="empty-state-icon mb-4">
                    <i class="fas fa-balance-scale fa-5x text-muted opacity-50"></i>
                </div>
                
                <!-- العنوان والوصف -->
                <h1 class="h3 mb-3">{{ __('No Ads to Compare') }}</h1>
                <p class="text-muted mb-4 lead">
                    {{ __('You haven\'t selected any ads for comparison yet. Browse our ads and add them to your comparison list.') }}
                </p>
                
                <!-- خطوات الاستخدام -->
                <div class="how-to-compare mb-5">
                    <h5 class="mb-3">{{ __('How to Compare Ads') }}</h5>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="step-card">
                                <div class="step-number">1</div>
                                <div class="step-icon">
                                    <i class="fas fa-search fa-2x text-primary"></i>
                                </div>
                                <h6>{{ __('Browse Ads') }}</h6>
                                <p class="small text-muted">
                                    {{ __('Find ads you\'re interested in') }}
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="step-card">
                                <div class="step-number">2</div>
                                <div class="step-icon">
                                    <i class="fas fa-balance-scale fa-2x text-info"></i>
                                </div>
                                <h6>{{ __('Add to Compare') }}</h6>
                                <p class="small text-muted">
                                    {{ __('Click the compare button on ads') }}
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="step-card">
                                <div class="step-number">3</div>
                                <div class="step-icon">
                                    <i class="fas fa-eye fa-2x text-success"></i>
                                </div>
                                <h6>{{ __('Compare') }}</h6>
                                <p class="small text-muted">
                                    {{ __('View detailed side-by-side comparison') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="action-buttons">
                    <a href="{{ route('ads.index') }}" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-search me-2"></i>
                        {{ __('Browse Ads') }}
                    </a>
                    
                    <a href="{{ route('categories.index') }}" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-th-large me-2"></i>
                        {{ __('Browse Categories') }}
                    </a>
                </div>
                
                <!-- معلومات إضافية -->
                <div class="additional-info mt-5">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="info-card">
                                <i class="fas fa-lightbulb fa-2x text-warning mb-3"></i>
                                <h6>{{ __('Smart Comparison') }}</h6>
                                <p class="small text-muted">
                                    {{ __('Compare up to 4 ads at once with detailed features, pricing, and ratings') }}
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="info-card">
                                <i class="fas fa-save fa-2x text-primary mb-3"></i>
                                <h6>{{ __('Save Your Selections') }}</h6>
                                <p class="small text-muted">
                                    {{ __('Your comparison list is automatically saved and persists across browser sessions') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.empty-state-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.step-card {
    position: relative;
    padding: 2rem 1rem;
    border-radius: 15px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
}

.step-number {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.step-icon {
    margin: 1rem 0;
}

.info-card {
    padding: 2rem 1rem;
    text-align: center;
    border-radius: 15px;
    background: white;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
}

.info-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.action-buttons .btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.how-to-compare {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

/* Responsive */
@media (max-width: 768px) {
    .empty-state-icon i {
        font-size: 3rem !important;
    }
    
    .step-card {
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
    }
    
    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
    
    .how-to-compare {
        padding: 1.5rem;
    }
}

/* تحسينات إضافية */
.lead {
    font-size: 1.1rem;
    line-height: 1.6;
}

h1, h5, h6 {
    color: #2c3e50;
}

.text-muted {
    color: #6c757d !important;
}
</style>
@endpush

@push('scripts')
<script>
// تحقق من وجود عناصر في localStorage وإعادة توجيه
document.addEventListener('DOMContentLoaded', function() {
    try {
        const compareList = localStorage.getItem('compareList');
        if (compareList) {
            const items = JSON.parse(compareList);
            if (items && items.length > 0) {
                // إظهار رسالة للمستخدم
                const message = `{{ __("You have :count items in your comparison list. Would you like to view them?", ["count" => ":count"]) }}`.replace(':count', items.length);
                
                if (confirm(message)) {
                    const ids = items.map(item => item.id).join(',');
                    window.location.href = `{{ route('compare.index') }}?ids=${ids}`;
                }
            }
        }
    } catch (e) {
        console.error('Error checking compare list:', e);
    }
});

// تأثيرات تفاعلية
document.querySelectorAll('.step-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>
@endpush
