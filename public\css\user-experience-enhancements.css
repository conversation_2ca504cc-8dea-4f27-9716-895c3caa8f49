/*
 * ملف CSS للتحسينات العامة لتجربة المستخدم
 * تم إنشاؤه كجزء من خطة تحسين تجربة المستخدم العامة
 * يحتوي على تحسينات شاملة للموقع بأكمله
 */

/* ===== متغيرات عامة للتحسينات ===== */
:root {
    --ux-primary: #3b82f6;
    --ux-secondary: #64748b;
    --ux-success: #10b981;
    --ux-warning: #f59e0b;
    --ux-danger: #ef4444;
    --ux-info: #06b6d4;
    --ux-light: #f8fafc;
    --ux-dark: #1e293b;
    --ux-border: #e2e8f0;
    --ux-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --ux-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --ux-radius: 12px;
    --ux-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --ux-font-size-xs: 0.75rem;
    --ux-font-size-sm: 0.875rem;
    --ux-font-size-base: 1rem;
    --ux-font-size-lg: 1.125rem;
    --ux-font-size-xl: 1.25rem;
}

/* ===== تحسينات عامة للصفحة ===== */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--ux-dark);
    background-color: var(--ux-light);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== تحسينات التركيز وإمكانية الوصول ===== */
*:focus {
    outline: 2px solid var(--ux-primary);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== تحسينات الأزرار العامة ===== */
.btn {
    border-radius: var(--ux-radius);
    font-weight: 600;
    transition: var(--ux-transition);
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--ux-shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== تحسينات البطاقات العامة ===== */
.card {
    border: 1px solid var(--ux-border);
    border-radius: var(--ux-radius);
    box-shadow: var(--ux-shadow);
    transition: var(--ux-transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--ux-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--ux-light) 0%, white 100%);
    border-bottom: 1px solid var(--ux-border);
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* ===== تحسينات النماذج ===== */
.form-control {
    border: 2px solid var(--ux-border);
    border-radius: var(--ux-radius);
    padding: 0.75rem 1rem;
    transition: var(--ux-transition);
    font-size: var(--ux-font-size-base);
}

.form-control:focus {
    border-color: var(--ux-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:invalid {
    border-color: var(--ux-danger);
}

.form-control:valid {
    border-color: var(--ux-success);
}

.form-label {
    font-weight: 600;
    color: var(--ux-dark);
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: var(--ux-font-size-sm);
    color: var(--ux-secondary);
}

/* ===== تحسينات التنبيهات ===== */
.alert {
    border: none;
    border-radius: var(--ux-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 4px solid var(--ux-success);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-left: 4px solid var(--ux-danger);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left: 4px solid var(--ux-warning);
}

.alert-info {
    background: rgba(6, 182, 212, 0.1);
    color: #155e75;
    border-left: 4px solid var(--ux-info);
}

/* ===== تحسينات التحميل ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: currentColor;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--ux-radius);
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== تحسينات الرسوم المتحركة ===== */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== تحسينات التمرير ===== */
.scroll-smooth {
    scroll-behavior: smooth;
}

.scroll-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--ux-primary);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--ux-transition);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: #2563eb;
    transform: translateY(-3px);
    box-shadow: var(--ux-shadow-lg);
}

/* ===== تحسينات الشبكة والتخطيط ===== */
.container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 576px) {
    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 768px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* ===== تحسينات النصوص ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--ux-primary), #2563eb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== تحسينات الصور ===== */
img {
    max-width: 100%;
    height: auto;
    border-radius: var(--ux-radius);
}

.img-hover-zoom {
    transition: var(--ux-transition);
    overflow: hidden;
}

.img-hover-zoom:hover img {
    transform: scale(1.05);
}

/* ===== تحسينات الجداول ===== */
.table {
    border-radius: var(--ux-radius);
    overflow: hidden;
    box-shadow: var(--ux-shadow);
}

.table th {
    background: var(--ux-light);
    border-bottom: 2px solid var(--ux-border);
    font-weight: 600;
    color: var(--ux-dark);
}

.table td {
    border-bottom: 1px solid var(--ux-border);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(59, 130, 246, 0.05);
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 768px) {
    :root {
        --ux-font-size-base: 0.9rem;
        --ux-font-size-lg: 1rem;
        --ux-font-size-xl: 1.125rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: var(--ux-font-size-sm);
    }
    
    .scroll-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .form-control {
        padding: 0.5rem 0.75rem;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .btn,
    .scroll-to-top,
    .alert {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
    
    body {
        background: white;
        color: black;
    }
}

/* ===== تحسينات الوضع المظلم (معطل مؤقتاً) ===== */
/* تم إلغاء تفعيل الوضع المظلم مؤقتاً - سيتم تفعيله لاحقاً عند الطلب */
/*
@media (prefers-color-scheme: dark) {
    :root {
        --ux-light: #1e293b;
        --ux-dark: #f1f5f9;
        --ux-border: #334155;
        --ux-secondary: #94a3b8;
    }

    body {
        background-color: #0f172a;
        color: #f1f5f9;
    }

    .card {
        background: #1e293b;
        border-color: #334155;
    }

    .card-header {
        background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    }

    .form-control {
        background: #1e293b;
        border-color: #334155;
        color: #f1f5f9;
    }

    .form-control:focus {
        border-color: var(--ux-primary);
        background: #1e293b;
    }

    .table th {
        background: #334155;
        color: #f1f5f9;
    }

    .table tbody tr:hover {
        background: rgba(59, 130, 246, 0.1);
    }
}
*/

/* ===== تحسينات الأداء ===== */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== تحسينات إضافية للتفاعل ===== */
.interactive-element {
    cursor: pointer;
    transition: var(--ux-transition);
}

.interactive-element:hover {
    transform: translateY(-1px);
}

.interactive-element:active {
    transform: translateY(0);
}

/* ===== تحسينات الحدود والظلال ===== */
.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, var(--ux-primary), #2563eb) border-box;
}

.shadow-soft {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
}

.shadow-strong {
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.16);
}
