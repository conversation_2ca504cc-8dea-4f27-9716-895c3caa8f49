
<div class="card-header-custom">
    <!-- صورة الإعلان -->
    <div class="ad-image" style="height: <?php echo e($config['imageHeight']); ?>;">
        <?php if($ad->image_url): ?>
            <img src="<?php echo e($ad->image_url); ?>" alt="<?php echo e($ad->title); ?>" class="card-img-top">
        <?php else: ?>
            <div class="placeholder-image">
                <i class="<?php echo e($ad->category->icon); ?> fa-4x text-muted"></i>
            </div>
        <?php endif; ?>

        <!-- أيقونة المفضلة -->
        <?php if($config['showFavorites']): ?>
            <?php if(auth()->guard()->check()): ?>
                <div class="favorite-icon" data-ad-id="<?php echo e($ad->id); ?>">
                    <button class="btn-favorite <?php echo e($ad->isFavoritedBy(auth()->id()) ? 'favorited' : ''); ?>"
                            title="<?php echo e($ad->isFavoritedBy(auth()->id()) ? __('Remove from Favorites') : __('Add to Favorites')); ?>">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <!-- شارات الحالة والميزات المتقدمة -->
        <?php echo $__env->make('components.ad-card.partials.badges', [
            'ad' => $ad,
            'detailed' => false,
            'position' => 'overlay'
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>
</div><?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/partials/image.blade.php ENDPATH**/ ?>