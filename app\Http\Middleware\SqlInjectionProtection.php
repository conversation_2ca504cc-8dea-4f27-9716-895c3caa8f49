<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware للحماية من SQL Injection
 * يفحص المدخلات للكشف عن محاولات SQL Injection
 */
class SqlInjectionProtection
{
    /**
     * أنماط SQL Injection المشبوهة
     */
    private array $sqlPatterns = [
        // Union-based injection
        '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)(\s|$)/i',

        // Boolean-based injection
        '/(\s|^)(and|or)(\s+)(\d+(\s*)=(\s*)\d+|true|false)/i',

        // Time-based injection
        '/(sleep|benchmark|waitfor|delay)\s*\(/i',

        // Comment-based injection
        '/\/\*.*?\*\/|--[\s\S]*?$/i',

        // Function-based injection
        '/(concat|group_concat|load_file|into\s+outfile|into\s+dumpfile)/i',

        // Information schema
        '/(information_schema|mysql\.user|sys\.)/i',

        // Hex encoding
        '/0x[0-9a-f]+/i',

        // SQL keywords
        '/(having|group\s+by|order\s+by|limit|offset)\s/i',

        // Subqueries
        '/\(\s*(select|union)/i',

        // SQL operators (basic check only)
        '/(\|\||&&|<>|!=|<=|>=)/i',
    ];

    /**
     * كلمات مفتاحية خطيرة
     */
    private array $dangerousKeywords = [
        'script', 'javascript', 'vbscript', 'onload', 'onerror', 'onclick',
        'eval', 'expression', 'applet', 'object', 'embed', 'form',
        'iframe', 'frameset', 'meta', 'link', 'style', 'base',
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // استثناء مؤقت لجميع طلبات الإعلانات لحل مشكلة Unicode
        if ($request->is('dashboard/ads*') || $request->is('ads*') || $request->is('admin/ads*')) {
            // إضافة logging للطلبات الإدارية
            if ($request->is('admin/ads*') && $request->isMethod('POST')) {
                \Log::info('SqlInjectionProtection: Admin ads POST request allowed', [
                    'url' => $request->fullUrl(),
                    'method' => $request->method(),
                    'user_id' => auth()->id()
                ]);
            }
            return $next($request);
        }

        // فحص جميع المدخلات
        if ($this->containsSqlInjection($request)) {
            Log::warning('SQL Injection attempt detected', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'inputs' => $this->sanitizeLogData($request->all()),
            ]);

            return response()->json([
                'error' => 'Invalid request detected',
                'message' => 'تم رصد محاولة غير صحيحة. تم تسجيل هذا النشاط.',
            ], 400);
        }

        return $next($request);
    }

    /**
     * فحص وجود SQL Injection في الطلب
     */
    private function containsSqlInjection(Request $request): bool
    {
        // استثناءات للطلبات الإدارية المشروعة
        if ($this->isLegitimateAdminRequest($request)) {
            return false;
        }

        // استثناءات لطلبات إدارة الإعلانات للمستخدمين المسجلين
        if ($this->isLegitimateUserAdRequest($request)) {
            return false;
        }

        // استثناء مؤقت لجميع طلبات الإعلانات لحل مشكلة Unicode
        if ($request->is('dashboard/ads/*') || $request->is('ads/*') || $request->is('admin/ads/*')) {
            return false;
        }

        // فحص جميع المعاملات
        $allInputs = array_merge(
            $request->query(),
            $request->request->all(),
            $request->route() ? $request->route()->parameters() : []
        );

        return $this->scanInputs($allInputs);
    }

    /**
     * فحص ما إذا كان الطلب خاص بإدارة الإعلانات للمستخدمين
     */
    private function isLegitimateUserAdRequest(Request $request): bool
    {
        // التحقق من أن المستخدم مسجل دخول
        if (!auth()->check()) {
            return false;
        }

        // التحقق من أن الطلب خاص بإدارة الإعلانات
        if (!$request->is('dashboard/ads/*') && !$request->is('ads/*') && !$request->is('admin/ads/*')) {
            return false;
        }

        // السماح بطلبات إنشاء وتعديل الإعلانات
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            // فحص إضافي للتأكد من أن البيانات تحتوي على حقول الإعلانات المتوقعة
            $expectedFields = ['title_ar', 'description_ar', 'category_id'];
            $hasExpectedFields = false;

            foreach ($expectedFields as $field) {
                if ($request->has($field)) {
                    $hasExpectedFields = true;
                    break;
                }
            }

            return $hasExpectedFields;
        }

        return false;
    }

    /**
     * فحص ما إذا كان الطلب إدارياً مشروعاً
     */
    private function isLegitimateAdminRequest(Request $request): bool
    {
        // التحقق من أن المستخدم مسجل دخول ومدير
        if (!auth()->check() || !auth()->user()->is_admin) {
            return false;
        }

        // التحقق من أن الطلب من لوحة الإدارة
        if (!$request->is('admin/*')) {
            return false;
        }

        // السماح بطلبات DELETE للموارد الإدارية
        if ($request->isMethod('DELETE') && $this->isAdminResourceRequest($request)) {
            return true;
        }

        // السماح بطلبات PUT/PATCH للموارد الإدارية
        if (in_array($request->method(), ['PUT', 'PATCH']) && $this->isAdminResourceRequest($request)) {
            return true;
        }

        return false;
    }

    /**
     * فحص ما إذا كان الطلب لمورد إداري مشروع
     */
    private function isAdminResourceRequest(Request $request): bool
    {
        $legitimateRoutes = [
            'admin/announcements',
            'admin/ads',
            'admin/users',
            'admin/categories',
            'admin/settings',
            'admin/reviews',
        ];

        $path = $request->path();

        foreach ($legitimateRoutes as $route) {
            if (str_starts_with($path, $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * فحص المدخلات بشكل تكراري
     */
    private function scanInputs($inputs): bool
    {
        foreach ($inputs as $key => $value) {
            if (is_array($value)) {
                if ($this->scanInputs($value)) {
                    return true;
                }
            } elseif (is_string($value)) {
                if ($this->isSuspiciousInput($value) || $this->isSuspiciousInput($key)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * فحص المدخل الواحد
     */
    private function isSuspiciousInput(string $input): bool
    {
        // تجاهل المدخلات الفارغة أو القصيرة جداً
        if (empty($input) || strlen($input) < 3) {
            return false;
        }

        // تجاهل النصوص العربية
        if ($this->isArabicText($input)) {
            return false;
        }

        $input = strtolower(trim($input));

        // فحص أنماط SQL
        foreach ($this->sqlPatterns as $pattern) {
            try {
                if (preg_match($pattern, $input)) {
                    return true;
                }
            } catch (\Exception $e) {
                // تجاهل أخطاء regex وتسجيلها
                Log::warning('Regex pattern error in SQL injection protection', [
                    'pattern' => $pattern,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        // فحص الكلمات المفتاحية الخطيرة
        foreach ($this->dangerousKeywords as $keyword) {
            if (strpos($input, $keyword) !== false) {
                return true;
            }
        }

        // فحص التشفير المشبوه
        if ($this->containsSuspiciousEncoding($input)) {
            return true;
        }

        // فحص الأحرف المشبوهة
        if ($this->containsSuspiciousCharacters($input)) {
            return true;
        }

        return false;
    }

    /**
     * فحص ما إذا كان النص يحتوي على أحرف عربية
     */
    private function isArabicText(string $input): bool
    {
        // فحص وجود أحرف عربية باستخدام mb_string
        if (function_exists('mb_strlen')) {
            // البحث عن أحرف عربية
            for ($i = 0; $i < mb_strlen($input, 'UTF-8'); $i++) {
                $char = mb_substr($input, $i, 1, 'UTF-8');
                $ord = mb_ord($char, 'UTF-8');

                // نطاق الأحرف العربية في Unicode
                if (($ord >= 0x0600 && $ord <= 0x06FF) || // Arabic
                    ($ord >= 0x0750 && $ord <= 0x077F) || // Arabic Supplement
                    ($ord >= 0x08A0 && $ord <= 0x08FF) || // Arabic Extended-A
                    ($ord >= 0xFB50 && $ord <= 0xFDFF) || // Arabic Presentation Forms-A
                    ($ord >= 0xFE70 && $ord <= 0xFEFF)) { // Arabic Presentation Forms-B
                    return true;
                }
            }
        }

        // فحص بديل باستخدام preg_match مع أحرف عربية محددة
        $arabicChars = ['ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي', 'ى', 'ة', 'أ', 'إ', 'آ', 'ؤ', 'ئ'];

        foreach ($arabicChars as $char) {
            if (strpos($input, $char) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * فحص التشفير المشبوه
     */
    private function containsSuspiciousEncoding(string $input): bool
    {
        // فحص URL encoding مشبوه
        if (preg_match('/%[0-9a-f]{2}/i', $input)) {
            $decoded = urldecode($input);
            if ($decoded !== $input && $this->isSuspiciousInput($decoded)) {
                return true;
            }
        }

        // فحص HTML entities مشبوه
        if (preg_match('/&#?\w+;/', $input)) {
            $decoded = html_entity_decode($input, ENT_QUOTES, 'UTF-8');
            if ($decoded !== $input && $this->isSuspiciousInput($decoded)) {
                return true;
            }
        }

        // فحص Base64 مشبوه
        if (preg_match('/^[A-Za-z0-9+\/]+=*$/', $input) && strlen($input) > 20) {
            $decoded = base64_decode($input, true);
            if ($decoded !== false && $this->isSuspiciousInput($decoded)) {
                return true;
            }
        }

        return false;
    }

    /**
     * فحص الأحرف المشبوهة
     */
    private function containsSuspiciousCharacters(string $input): bool
    {
        // فحص أحرف SQL خاصة
        $suspiciousChars = ['\'', '"', ';', '(', ')', '{', '}', '[', ']'];
        $charCount = 0;

        foreach ($suspiciousChars as $char) {
            $charCount += substr_count($input, $char);
        }

        // إذا كان هناك أكثر من 5 أحرف مشبوهة
        if ($charCount > 5) {
            return true;
        }

        // فحص تسلسل أحرف مشبوه
        if (preg_match('/[\'";(){}[\]]{3,}/', $input)) {
            return true;
        }

        return false;
    }

    /**
     * تنظيف بيانات السجل
     */
    private function sanitizeLogData(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeLogData($value);
            } elseif (is_string($value)) {
                // إخفاء كلمات المرور والمعلومات الحساسة
                if (in_array(strtolower($key), ['password', 'token', 'secret', 'key'])) {
                    $sanitized[$key] = '[HIDDEN]';
                } else {
                    $sanitized[$key] = substr($value, 0, 100) . (strlen($value) > 100 ? '...' : '');
                }
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * إضافة نمط جديد للفحص
     */
    public function addPattern(string $pattern): void
    {
        $this->sqlPatterns[] = $pattern;
    }

    /**
     * إضافة كلمة مفتاحية خطيرة
     */
    public function addDangerousKeyword(string $keyword): void
    {
        $this->dangerousKeywords[] = strtolower($keyword);
    }
}
