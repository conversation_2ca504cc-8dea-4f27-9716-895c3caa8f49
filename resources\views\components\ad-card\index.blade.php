{{--
    مكون البطاقة الموحد للإعلانات
    يدمج جميع أنواع البطاقات في مكون واحد مرن
--}}

@props([
    'ad',                           // الإعلان المطلوب عرضه (مطلوب)
    'variant' => 'default',        // نوع البطاقة: default, compact, detailed, list
    'showFavorites' => true,       // إظهار أيقونة المفضلة
    'descriptionLength' => 100,    // طول الوصف المعروض
    'showPricing' => true,         // إظهار قسم الأسعار
    'showMeta' => true,            // إظهار المعلومات الإضافية
    'showActions' => true,         // إظهار أزرار الإجراءات
    'showExpiry' => true,          // إظهار معلومات انتهاء الصلاحية
    'noWrapper' => false,          // عدم إضافة wrapper div
    'customClass' => '',           // فئات CSS إضافية
    'imageHeight' => null,         // ارتفاع الصورة المخصص
    'cardHeight' => null,          // ارتفاع البطاقة المخصص
    'columns' => null,             // تخصيص عدد الأعمدة: 2, 3, 4, 5, 6
    'gridClass' => '',             // فئات Bootstrap Grid مخصصة
])

{{-- تحديد إعدادات البطاقة حسب النوع --}}
@php
    $cardConfig = match($variant) {
        'compact' => [
            'descriptionLength' => $descriptionLength ?: 80,
            'imageHeight' => $imageHeight ?: '220px',
            'cardHeight' => $cardHeight ?: '480px',
            'padding' => 'p-3',
            'marginBottom' => 'mb-2',
            'titleSize' => 'h6',
            'showFullMeta' => false,
            'showRating' => true,
        ],
        'detailed' => [
            'descriptionLength' => $descriptionLength ?: 150,
            'imageHeight' => $imageHeight ?: '320px',
            'cardHeight' => $cardHeight ?: '600px',
            'padding' => 'p-4',
            'marginBottom' => 'mb-3',
            'titleSize' => 'h4',
            'showFullMeta' => true,
            'showRating' => true,
            'showDetailedRating' => true,
            'showComments' => true,
        ],
        'list' => [
            'descriptionLength' => $descriptionLength ?: 200,
            'imageHeight' => $imageHeight ?: '200px',
            'cardHeight' => $cardHeight ?: 'auto',
            'padding' => 'p-3',
            'marginBottom' => 'mb-3',
            'titleSize' => 'h5',
            'showFullMeta' => true,
            'showRating' => true,
        ],
        'favorites' => [
            'descriptionLength' => $descriptionLength ?: 80,
            'imageHeight' => $imageHeight ?: '200px',
            'cardHeight' => $cardHeight ?: '480px',
            'padding' => 'p-3',
            'marginBottom' => 'mb-3',
            'titleSize' => 'h6',
            'showFullMeta' => true,
            'showFavoriteDate' => true,
            'showRating' => true,
        ],
        default => [
            'descriptionLength' => $descriptionLength ?: 100,
            'imageHeight' => $imageHeight ?: '280px',
            'cardHeight' => $cardHeight ?: '550px',
            'padding' => 'p-4',
            'marginBottom' => 'mb-3',
            'titleSize' => 'h5',
            'showFullMeta' => true,
            'showRating' => true,
        ],
    };

    // تحديد فئات Bootstrap Grid حسب عدد الأعمدة
    $gridClasses = '';
    if ($gridClass) {
        $gridClasses = $gridClass;
    } elseif ($columns) {
        $gridClasses = match($columns) {
            2 => 'col-xl-6 col-lg-6 col-md-6 col-sm-12',
            3 => 'col-xl-4 col-lg-6 col-md-6 col-sm-12',
            4 => 'col-xl-3 col-lg-4 col-md-6 col-sm-12',
            5 => 'col-xl-2 col-lg-3 col-md-4 col-sm-6',
            6 => 'col-xl-2 col-lg-2 col-md-3 col-sm-6',
            default => 'col-xl-4 col-lg-6 col-md-6 col-sm-12', // 3 أعمدة افتراضي
        };
    } else {
        // الأعمدة الافتراضية حسب variant
        $gridClasses = match($variant) {
            'compact' => 'col-xl-2 col-lg-3 col-md-4 col-sm-6',     // 6 أعمدة
            'detailed' => 'col-xl-6 col-lg-6 col-md-12 col-sm-12',  // 2 أعمدة
            'list' => 'col-12',                                      // عمود واحد
            'favorites' => 'col-xl-3 col-lg-4 col-md-6 col-sm-12',  // 4 أعمدة
            default => 'col-xl-4 col-lg-6 col-md-6 col-sm-12',      // 3 أعمدة
        };
    }

    // دمج الإعدادات المخصصة
    $config = array_merge($cardConfig, [
        'variant' => $variant,
        'showFavorites' => $showFavorites,
        'showPricing' => $showPricing,
        'showMeta' => $showMeta,
        'showActions' => $showActions,
        'showExpiry' => $showExpiry,
        'gridClasses' => $gridClasses,
        'customClass' => $customClass,
        'customAttributes' => $customAttributes ?? [],
    ]);
@endphp

{{-- Wrapper الخارجي للجميع --}}
@if(!$noWrapper)
    <div class="{{ $config['gridClasses'] }} mb-5 {{ $config['customAttributes']['class'] ?? '' }}"
         @foreach($config['customAttributes'] as $attr => $value)
             @if($attr !== 'class')
                 {{ $attr }}="{{ $value }}"
             @endif
         @endforeach>
@endif

{{-- استخدام variant مخصص للمفضلة --}}
@if($variant === 'favorites')
    @include('components.ad-card.variants.favorites', [
        'ad' => $ad,
        'favorite' => $favorite ?? null,
        'config' => $config
    ])
@else
    {{-- البطاقة الرئيسية --}}
    <div class="card ad-card ad-card-{{ $variant }} h-100 shadow-sm border-0 {{ $config['customClass'] }}"
         data-ad-id="{{ $ad->id }}"
         style="min-height: {{ $config['cardHeight'] }};">

        {{-- رأس البطاقة مع الصورة --}}
        @include('components.ad-card.partials.image', ['ad' => $ad, 'config' => $config])

        {{-- محتوى البطاقة --}}
        <div class="card-body {{ $config['padding'] }}" style="overflow: visible; position: relative;">
            {{-- شارة التصنيف --}}
            @include('components.ad-card.partials.category-badge', ['ad' => $ad, 'config' => $config])

            {{-- عنوان الإعلان --}}
            @include('components.ad-card.partials.title', ['ad' => $ad, 'config' => $config])

            {{-- وصف الإعلان --}}
            @include('components.ad-card.partials.description', ['ad' => $ad, 'config' => $config])

            {{-- قسم التقييم --}}
            @if($config['showRating'] ?? true)
                @include('components.ad-card.partials.rating', ['ad' => $ad, 'config' => $config])
            @endif

            {{-- المعلومات الإضافية --}}
            @if($config['showMeta'])
                @include('components.ad-card.partials.meta', ['ad' => $ad, 'config' => $config])
            @endif

            {{-- نظام التواصل الموحد (أيقونات ومعلومات) --}}
            @include('components.ad-card.partials.contact', ['ad' => $ad, 'config' => $config])

            {{-- قسم الأسعار --}}
            @if($config['showPricing'])
                @include('components.ad-card.partials.pricing', ['ad' => $ad, 'config' => $config])
            @endif

            {{-- قسم التعليقات --}}
            @if($config['showComments'] ?? false)
                @include('components.ad-card.partials.comments', ['ad' => $ad, 'config' => $config])
            @endif
        </div>

        {{-- تذييل البطاقة --}}
        @if($config['showActions'] || $config['showExpiry'])
            @include('components.ad-card.partials.footer', ['ad' => $ad, 'config' => $config])
        @endif

        {{-- تأثير hover --}}
        <div class="card-overlay"></div>
    </div>
@endif

{{-- إغلاق Wrapper الخارجي للجميع --}}
@if(!$noWrapper)
    </div>
@endif

{{-- تحميل CSS مرة واحدة فقط --}}
@once
    @push('styles')
        <link rel="stylesheet" href="{{ asset('css/ad-cards.css') }}">
        <link rel="stylesheet" href="{{ asset('css/badge-fix.css') }}">
        <link rel="stylesheet" href="{{ asset('css/components/contact-responsive.css') }}">
    @endpush
@endonce

{{-- تحميل JavaScript مرة واحدة فقط --}}
@once
    @push('scripts')
        <script src="{{ asset('js/favorites.js') }}"></script>
        <script>
            // دالة مشاركة الإعلان
            function shareAd(url, title) {
                if (navigator.share) {
                    navigator.share({
                        title: title,
                        url: url
                    }).catch(console.error);
                } else {
                    // نسخ الرابط للحافظة
                    navigator.clipboard.writeText(url).then(function() {
                        showToast('{{ __("Link copied to clipboard") }}', 'success');
                    }).catch(function() {
                        // فتح نافذة مشاركة تقليدية
                        window.open('https://wa.me/?text=' + encodeURIComponent(title + ' - ' + url), '_blank');
                    });
                }
            }
        </script>
    @endpush
@endonce