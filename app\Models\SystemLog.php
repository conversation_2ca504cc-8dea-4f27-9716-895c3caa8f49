<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Model موحد للسجلات
 * يحل محل: SecurityLog, SearchLog, ContactAccessLog
 */
class SystemLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'user_id',
        'action',
        'description',
        'data',
        'ip_address',
        'user_agent',
        'url',
        'severity',
    ];

    protected $casts = [
        'data' => 'array',
    ];

    /**
     * أنواع السجلات
     */
    const TYPES = [
        'security' => 'أمني',
        'search' => 'بحث',
        'contact_access' => 'الوصول لمعلومات التواصل',
        'admin_action' => 'إجراء إداري',
        'error' => 'خطأ',
        'audit' => 'مراجعة',
    ];

    /**
     * مستويات الخطورة
     */
    const SEVERITIES = [
        'low' => 'منخفض',
        'medium' => 'متوسط',
        'high' => 'عالي',
        'critical' => 'حرج',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope للسجلات الأمنية
     */
    public function scopeSecurity($query)
    {
        return $query->where('type', 'security');
    }

    /**
     * Scope لسجلات البحث
     */
    public function scopeSearch($query)
    {
        return $query->where('type', 'search');
    }

    /**
     * Scope لسجلات الوصول للتواصل
     */
    public function scopeContactAccess($query)
    {
        return $query->where('type', 'contact_access');
    }

    /**
     * Scope للإجراءات الإدارية
     */
    public function scopeAdminActions($query)
    {
        return $query->where('type', 'admin_action');
    }

    /**
     * Scope للأخطاء
     */
    public function scopeErrors($query)
    {
        return $query->where('type', 'error');
    }

    /**
     * Scope حسب مستوى الخطورة
     */
    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * تسجيل حدث أمني
     */
    public static function logSecurity($action, $description = null, $data = null, $userId = null, $severity = 'medium')
    {
        return static::create([
            'type' => 'security',
            'user_id' => $userId ?: auth()->id(),
            'action' => $action,
            'description' => $description,
            'data' => $data,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'severity' => $severity,
        ]);
    }

    /**
     * تسجيل عملية بحث
     */
    public static function logSearch($searchTerm, $resultsCount = null, $userId = null)
    {
        return static::create([
            'type' => 'search',
            'user_id' => $userId ?: auth()->id(),
            'action' => 'search',
            'description' => "بحث عن: {$searchTerm}",
            'data' => [
                'search_term' => $searchTerm,
                'results_count' => $resultsCount,
            ],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'severity' => 'low',
        ]);
    }

    /**
     * تسجيل الوصول لمعلومات التواصل
     */
    public static function logContactAccess($adId, $contactType, $userId = null)
    {
        return static::create([
            'type' => 'contact_access',
            'user_id' => $userId ?: auth()->id(),
            'action' => 'contact_reveal',
            'description' => "كشف معلومات التواصل للإعلان #{$adId}",
            'data' => [
                'ad_id' => $adId,
                'contact_type' => $contactType, // phone, email, whatsapp
            ],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'severity' => 'low',
        ]);
    }

    /**
     * تسجيل إجراء إداري
     */
    public static function logAdminAction($action, $description = null, $data = null, $severity = 'medium')
    {
        return static::create([
            'type' => 'admin_action',
            'user_id' => auth()->id(),
            'action' => $action,
            'description' => $description,
            'data' => $data,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'severity' => $severity,
        ]);
    }

    /**
     * تسجيل خطأ
     */
    public static function logError($action, $description, $data = null, $severity = 'high')
    {
        return static::create([
            'type' => 'error',
            'user_id' => auth()->id(),
            'action' => $action,
            'description' => $description,
            'data' => $data,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'severity' => $severity,
        ]);
    }

    /**
     * تسجيل مراجعة
     */
    public static function logAudit($action, $description = null, $data = null)
    {
        return static::create([
            'type' => 'audit',
            'user_id' => auth()->id(),
            'action' => $action,
            'description' => $description,
            'data' => $data,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'severity' => 'low',
        ]);
    }

    /**
     * تنظيف السجلات القديمة
     */
    public static function cleanup($days = 90)
    {
        return static::where('created_at', '<', now()->subDays($days))->delete();
    }

    /**
     * الحصول على إحصائيات السجلات
     */
    public static function getStats($days = 30)
    {
        $query = static::where('created_at', '>=', now()->subDays($days));

        return [
            'total' => $query->count(),
            'by_type' => $query->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'by_severity' => $query->selectRaw('severity, COUNT(*) as count')
                ->groupBy('severity')
                ->pluck('count', 'severity')
                ->toArray(),
        ];
    }
}
