@extends('layouts.app')

@section('title', __('Profile') . ' - ' . __('site_name'))
@section('description', __('Manage your account settings and personal information'))

@push('styles')
<link rel="stylesheet" href="{{ asset('css/components/contact-management.css') }}">
@endpush

@section('content')
<!-- القسم الرئيسي للملف الشخصي -->
<section class="profile-section py-5" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); min-height: 40vh;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white">
                <div class="profile-header mb-4">
                    <div class="profile-avatar mb-3">
                        <i class="fas fa-user-circle fa-5x"></i>
                    </div>
                    <h1 class="display-5 fw-bold arabic-text mb-2">
                        {{ __('My Profile') }}
                    </h1>
                    <p class="lead arabic-text mb-3">
                        {{ __('Manage your account settings and personal information') }}
                    </p>
                    <div class="profile-meta">
                        <span class="badge bg-light text-dark me-2">
                            <i class="fas fa-calendar me-1"></i>
                            {{ __('Member since') }}: {{ Auth::user()->created_at->format('Y/m/d') }}
                        </span>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-envelope me-1"></i>
                            {{ Auth::user()->email }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الملف الشخصي -->
<section class="profile-content py-5">
    <div class="container">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-lg-3 mb-4">
                <div class="profile-sidebar">
                    <div class="sidebar-card">
                        <h5 class="arabic-text fw-bold mb-3">
                            <i class="fas fa-cog me-2 text-primary"></i>
                            {{ __('Account Settings') }}
                        </h5>
                        <ul class="profile-menu">
                            <li class="active">
                                <a href="#personal-info" class="arabic-text" onclick="showTab('personal-info')">
                                    <i class="fas fa-user me-2"></i>
                                    {{ __('Personal Information') }}
                                </a>
                            </li>
                            <li>
                                <a href="#payment-info" class="arabic-text" onclick="showTab('payment-info')">
                                    <i class="fas fa-credit-card me-2"></i>
                                    {{ __('Payment Information') }}
                                </a>
                            </li>
                            <li>
                                <a href="#contact-info" class="arabic-text" onclick="showTab('contact-info')">
                                    <i class="fas fa-address-book me-2"></i>
                                    {{ __('Contact Information') }}
                                </a>
                            </li>
                            <li>
                                <a href="#security" class="arabic-text" onclick="showTab('security')">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    {{ __('Security') }}
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-lg-9">
                <!-- رسائل النجاح والأخطاء -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <span class="arabic-text">{{ session('success') }}</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li class="arabic-text">{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- نموذج تحديث الملف الشخصي -->
                <form method="POST" action="{{ route('auth.profile.update') }}" class="profile-form">
                    @csrf
                    @method('PUT')

                    <!-- المعلومات الشخصية -->
                    <div class="tab-content" id="personal-info">
                        <div class="profile-card">
                            <div class="card-header">
                                <h4 class="arabic-text fw-bold mb-0">
                                    <i class="fas fa-user me-2 text-primary"></i>
                                    {{ __('Personal Information') }}
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- الاسم الكامل -->
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-user me-2 text-primary"></i>
                                            {{ __('Full Name') }}
                                        </label>
                                        <input type="text" 
                                               class="form-control @error('name') is-invalid @enderror" 
                                               id="name" 
                                               name="name" 
                                               value="{{ old('name', Auth::user()->name) }}" 
                                               required>
                                        @error('name')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- البريد الإلكتروني -->
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-envelope me-2 text-primary"></i>
                                            {{ __('Email') }}
                                        </label>
                                        <input type="email" 
                                               class="form-control @error('email') is-invalid @enderror" 
                                               id="email" 
                                               name="email" 
                                               value="{{ old('email', Auth::user()->email) }}" 
                                               required>
                                        @error('email')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- العملة المفضلة -->
                                    <div class="col-12 mb-3">
                                        <label for="currency" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-coins me-2 text-primary"></i>
                                            {{ __('Preferred Currency') }}
                                        </label>
                                        <div class="currency-options">
                                            <div class="row g-2">
                                                <div class="col-md-4">
                                                    <div class="form-check currency-check">
                                                        <input class="form-check-input" type="radio" name="currency" id="currency_yer" value="YER" {{ Auth::user()->currency == 'YER' ? 'checked' : '' }}>
                                                        <label class="form-check-label arabic-text" for="currency_yer">
                                                            <i class="fas fa-coins text-warning me-2"></i>
                                                            {{ __('Yemeni Rial') }}
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-check currency-check">
                                                        <input class="form-check-input" type="radio" name="currency" id="currency_usd" value="USD" {{ Auth::user()->currency == 'USD' ? 'checked' : '' }}>
                                                        <label class="form-check-label arabic-text" for="currency_usd">
                                                            <i class="fas fa-dollar-sign text-success me-2"></i>
                                                            {{ __('US Dollar') }}
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-check currency-check">
                                                        <input class="form-check-input" type="radio" name="currency" id="currency_sar" value="SAR" {{ Auth::user()->currency == 'SAR' ? 'checked' : '' }}>
                                                        <label class="form-check-label arabic-text" for="currency_sar">
                                                            <i class="fas fa-coins text-info me-2"></i>
                                                            {{ __('Saudi Riyal') }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الدفع -->
                    <div class="tab-content" id="payment-info" style="display: none;">
                        <div class="profile-card">
                            <div class="card-header">
                                <h4 class="arabic-text fw-bold mb-0">
                                    <i class="fas fa-credit-card me-2 text-success"></i>
                                    {{ __('Payment Information') }}
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- رقم البطاقة -->
                                    <div class="col-md-6 mb-3">
                                        <label for="card_number" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-credit-card me-2 text-success"></i>
                                            {{ __('Card Number') }}
                                            <small class="text-muted">({{ __('Optional') }})</small>
                                        </label>
                                        <input type="text" 
                                               class="form-control @error('card_number') is-invalid @enderror" 
                                               id="card_number" 
                                               name="card_number" 
                                               value="{{ old('card_number', Auth::user()->card_number) }}" 
                                               placeholder="{{ __('Enter card number') }}">
                                        @error('card_number')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- نوع البطاقة -->
                                    <div class="col-md-6 mb-3">
                                        <label for="card_type" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-university me-2 text-success"></i>
                                            {{ __('Card Type') }}
                                            <small class="text-muted">({{ __('Optional') }})</small>
                                        </label>
                                        <select class="form-select @error('card_type') is-invalid @enderror" 
                                                id="card_type" 
                                                name="card_type">
                                            <option value="">{{ __('Select card type') }}</option>
                                            <option value="كاك بنك" {{ Auth::user()->card_type == 'كاك بنك' ? 'selected' : '' }}>{{ __('CAC Bank') }}</option>
                                            <option value="الكريمي" {{ Auth::user()->card_type == 'الكريمي' ? 'selected' : '' }}>{{ __('Al-Kuraimi Bank') }}</option>
                                            <option value="بنك اليمن الدولي" {{ Auth::user()->card_type == 'بنك اليمن الدولي' ? 'selected' : '' }}>{{ __('Yemen International Bank') }}</option>
                                            <option value="البنك الأهلي" {{ Auth::user()->card_type == 'البنك الأهلي' ? 'selected' : '' }}>{{ __('Al-Ahli Bank') }}</option>
                                            <option value="بنك التضامن" {{ Auth::user()->card_type == 'بنك التضامن' ? 'selected' : '' }}>{{ __('Tadhamon Bank') }}</option>
                                            <option value="أخرى" {{ Auth::user()->card_type == 'أخرى' ? 'selected' : '' }}>{{ __('Other') }}</option>
                                        </select>
                                        @error('card_type')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التواصل -->
                    <div class="tab-content" id="contact-info" style="display: none;">
                        <div class="profile-card">
                            <div class="card-header">
                                <h4 class="arabic-text fw-bold mb-0">
                                    <i class="fas fa-address-book me-2 text-info"></i>
                                    {{ __('Contact Information') }}
                                </h4>
                                <p class="text-muted mb-0 mt-2">
                                    إدارة طرق التواصل التي ستظهر في إعلاناتك
                                </p>
                            </div>
                            <div class="card-body">
                                <!-- أزرار الإجراءات -->
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <button type="button" class="btn btn-primary" onclick="showAddContactModal()">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة طريقة تواصل جديدة
                                    </button>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshContacts()">
                                            <i class="fas fa-sync-alt me-1"></i>
                                            تحديث
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="showContactsHelp()">
                                            <i class="fas fa-question-circle me-1"></i>
                                            مساعدة
                                        </button>
                                    </div>
                                </div>

                                <!-- قائمة معلومات التواصل -->
                                <div id="contacts-list" class="contacts-container">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="text-muted mt-2">جاري تحميل معلومات التواصل...</p>
                                    </div>
                                </div>

                                <!-- رسالة عدم وجود معلومات تواصل -->
                                <div id="no-contacts-message" class="text-center py-5" style="display: none;">
                                    <i class="fas fa-address-book fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد معلومات تواصل</h5>
                                    <p class="text-muted">أضف طرق التواصل الخاصة بك لتظهر في إعلاناتك</p>
                                    <button type="button" class="btn btn-primary" onclick="showAddContactModal()">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة أول طريقة تواصل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأمان -->
                    <div class="tab-content" id="security" style="display: none;">
                        <div class="profile-card">
                            <div class="card-header">
                                <h4 class="arabic-text fw-bold mb-0">
                                    <i class="fas fa-shield-alt me-2 text-warning"></i>
                                    {{ __('Security Settings') }}
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- كلمة المرور الحالية -->
                                    <div class="col-12 mb-3">
                                        <label for="current_password" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-lock me-2 text-warning"></i>
                                            {{ __('Current Password') }}
                                            <small class="text-muted">({{ __('Required to change password') }})</small>
                                        </label>
                                        <div class="password-input-group">
                                            <input type="password" 
                                                   class="form-control @error('current_password') is-invalid @enderror" 
                                                   id="current_password" 
                                                   name="current_password" 
                                                   placeholder="{{ __('Enter current password') }}">
                                            <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                                <i class="fas fa-eye" id="current_password-icon"></i>
                                            </button>
                                        </div>
                                        @error('current_password')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- كلمة المرور الجديدة -->
                                    <div class="col-md-6 mb-3">
                                        <label for="new_password" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-key me-2 text-warning"></i>
                                            {{ __('New Password') }}
                                        </label>
                                        <div class="password-input-group">
                                            <input type="password" 
                                                   class="form-control @error('new_password') is-invalid @enderror" 
                                                   id="new_password" 
                                                   name="new_password" 
                                                   placeholder="{{ __('Enter new password') }}">
                                            <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                                <i class="fas fa-eye" id="new_password-icon"></i>
                                            </button>
                                        </div>
                                        @error('new_password')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- تأكيد كلمة المرور الجديدة -->
                                    <div class="col-md-6 mb-3">
                                        <label for="new_password_confirmation" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-key me-2 text-warning"></i>
                                            {{ __('Confirm New Password') }}
                                        </label>
                                        <div class="password-input-group">
                                            <input type="password" 
                                                   class="form-control" 
                                                   id="new_password_confirmation" 
                                                   name="new_password_confirmation" 
                                                   placeholder="{{ __('Confirm new password') }}">
                                            <button type="button" class="password-toggle" onclick="togglePassword('new_password_confirmation')">
                                                <i class="fas fa-eye" id="new_password_confirmation-icon"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="profile-actions mt-4">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary btn-lg w-100 arabic-text fw-bold">
                                    <i class="fas fa-save me-2"></i>
                                    {{ __('Save Changes') }}
                                </button>
                            </div>
                            <div class="col-md-6">
                                <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-lg w-100 arabic-text">
                                    <i class="fas fa-home me-2"></i>
                                    {{ __('Back to Home') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق القسم الرئيسي */
.profile-section {
    position: relative;
    overflow: hidden;
}

.profile-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.profile-avatar {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* الشريط الجانبي */
.profile-sidebar .sidebar-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 2rem;
}

.profile-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.profile-menu li {
    margin-bottom: 0.5rem;
}

.profile-menu a {
    display: block;
    padding: 0.75rem 1rem;
    color: #6c757d;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.profile-menu a:hover,
.profile-menu li.active a {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    color: white;
    transform: translateX(5px);
}

/* بطاقات الملف الشخصي */
.profile-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
}

.profile-card .card-header {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
}

.profile-card .card-body {
    padding: 2rem;
}

/* نموذج الملف الشخصي */
.profile-form .form-control,
.profile-form .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.profile-form .form-control:focus,
.profile-form .form-select:focus {
    border-color: #6f42c1;
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    transform: translateY(-2px);
}

/* خيارات العملة */
.currency-check {
    background: rgba(111, 66, 193, 0.1);
    border-radius: 10px;
    padding: 0.75rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.currency-check:hover {
    background: rgba(111, 66, 193, 0.2);
    transform: translateY(-2px);
}

.currency-check input:checked + label {
    color: #6f42c1;
    font-weight: bold;
}

.currency-check input:checked {
    background-color: #6f42c1;
    border-color: #6f42c1;
}

/* مجموعة كلمة المرور */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #6f42c1;
}

/* أزرار الإجراءات */
.profile-actions .btn {
    border-radius: 15px;
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.profile-actions .btn-primary {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    border: none;
}

.profile-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(111, 66, 193, 0.3);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .profile-section {
        min-height: 30vh;
        padding: 3rem 0;
    }

    .profile-sidebar {
        margin-bottom: 2rem;
    }

    .profile-sidebar .sidebar-card {
        position: static;
    }

    .profile-menu {
        display: flex;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }

    .profile-menu li {
        flex-shrink: 0;
        margin-right: 0.5rem;
        margin-bottom: 0;
    }

    .profile-card .card-body {
        padding: 1.5rem;
    }

    .profile-actions .btn {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .profile-card .card-header,
    .profile-card .card-body {
        padding: 1rem;
    }

    .currency-check {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// دالة تبديل التبويبات
function showTab(tabId) {
    console.log('🔄 تبديل إلى تبويب:', tabId);

    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.style.display = 'none';
    });

    // إزالة الفئة النشطة من جميع عناصر القائمة
    document.querySelectorAll('.profile-menu li').forEach(item => {
        item.classList.remove('active');
    });

    // إظهار التبويب المحدد
    document.getElementById(tabId).style.display = 'block';

    // إضافة الفئة النشطة للعنصر المحدد
    event.target.closest('li').classList.add('active');
}

// دالة إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق التأثير على العملة المحددة
    const checkedCurrency = document.querySelector('input[name="currency"]:checked');
    if (checkedCurrency) {
        const parentCheck = checkedCurrency.closest('.currency-check');
        parentCheck.style.borderColor = '#6f42c1';
        parentCheck.style.background = 'rgba(111, 66, 193, 0.2)';
    }

    // إضافة مستمعات للعملة
    const currencyInputs = document.querySelectorAll('input[name="currency"]');
    currencyInputs.forEach(input => {
        input.addEventListener('change', function() {
            // إزالة التأثير من جميع الخيارات
            document.querySelectorAll('.currency-check').forEach(check => {
                check.style.borderColor = 'transparent';
                check.style.background = 'rgba(111, 66, 193, 0.1)';
            });

            // إضافة التأثير للخيار المحدد
            if (this.checked) {
                const parentCheck = this.closest('.currency-check');
                parentCheck.style.borderColor = '#6f42c1';
                parentCheck.style.background = 'rgba(111, 66, 193, 0.2)';
            }
        });
    });

    // تحسين تجربة النماذج
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    // ===== وظائف معلومات التواصل =====

    // متغيرات عامة
    let contactsData = [];
    let editingContactId = null;

    // تحقق من أن جميع العناصر موجودة
    console.log('🔍 فحص العناصر المطلوبة...');
    console.log('contactModal:', document.getElementById('contactModal'));
    console.log('contactForm:', document.getElementById('contactForm'));
    console.log('contactModalTitle:', document.getElementById('contactModalTitle'));
    console.log('contacts-list:', document.getElementById('contacts-list'));
    console.log('no-contacts-message:', document.getElementById('no-contacts-message'));

    // تحقق من Bootstrap
    console.log('Bootstrap متوفر:', typeof bootstrap !== 'undefined');
    console.log('Bootstrap.Modal متوفر:', typeof bootstrap !== 'undefined' && bootstrap.Modal);

    // تحميل معلومات التواصل عند تحميل الصفحة
    loadContacts();

    // تعريف الوظائف في النطاق العام للوصول إليها من HTML
    window.showAddContactModal = showAddContactModal;
    window.editContact = editContact;
    window.deleteContact = deleteContact;
    window.toggleContactPublic = toggleContactPublic;
    window.refreshContacts = refreshContacts;
    window.showContactsHelp = showContactsHelp;
    window.saveContact = saveContact;

    // تحميل معلومات التواصل
    function loadContacts() {
        console.log('Loading contacts...');
        fetch('/dashboard/contacts', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Contacts data received:', data);
            if (data.success) {
                contactsData = data.data;
                renderContacts();
            } else {
                console.error('API returned error:', data.message);
                showError(data.message || 'فشل في تحميل معلومات التواصل');
            }
        })
        .catch(error => {
            console.error('Error loading contacts:', error);
            showError('حدث خطأ في تحميل معلومات التواصل: ' + error.message);
        });
    }

    // عرض معلومات التواصل
    function renderContacts() {
        const container = document.getElementById('contacts-list');
        const noContactsMessage = document.getElementById('no-contacts-message');

        if (contactsData.length === 0) {
            container.style.display = 'none';
            noContactsMessage.style.display = 'block';
            return;
        }

        container.style.display = 'block';
        noContactsMessage.style.display = 'none';

        const html = contactsData.map(contact => `
            <div class="contact-item card mb-3" data-contact-id="${contact.id}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="${getContactIcon(contact.contact_type)} fa-lg me-3 text-primary"></i>
                                <div>
                                    <h6 class="mb-1">${contact.display_label || getContactTypeName(contact.contact_type)}</h6>
                                    <p class="mb-0 text-muted">${contact.contact_value}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex flex-wrap gap-1">
                                ${contact.is_primary ? '<span class="badge bg-success">أساسي</span>' : ''}
                                ${contact.is_verified ? '<span class="badge bg-info">متحقق</span>' : ''}
                                ${!contact.is_public ? '<span class="badge bg-warning">مخفي</span>' : ''}
                                <span class="badge bg-secondary">${getPrivacyLevelName(contact.privacy_level)}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editContact(${contact.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-${contact.is_public ? 'warning' : 'success'}"
                                        onclick="toggleContactPublic(${contact.id})">
                                    <i class="fas fa-${contact.is_public ? 'eye-slash' : 'eye'}"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteContact(${contact.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    // إظهار modal إضافة معلومة تواصل
    function showAddContactModal() {
        console.log('🔄 محاولة فتح Modal...');

        // تحقق من وجود العناصر المطلوبة
        const modalElement = document.getElementById('contactModal');
        const formElement = document.getElementById('contactForm');
        const titleElement = document.getElementById('contactModalTitle');

        if (!modalElement) {
            console.error('❌ لم يتم العثور على contactModal');
            alert('خطأ: لم يتم العثور على نافذة الإضافة');
            return;
        }

        if (!formElement) {
            console.error('❌ لم يتم العثور على contactForm');
            alert('خطأ: لم يتم العثور على النموذج');
            return;
        }

        if (!titleElement) {
            console.error('❌ لم يتم العثور على contactModalTitle');
            alert('خطأ: لم يتم العثور على عنوان النافذة');
            return;
        }

        // تحقق من Bootstrap
        if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
            console.error('❌ Bootstrap غير متوفر');
            alert('خطأ: Bootstrap غير محمل بشكل صحيح');
            return;
        }

        try {
            editingContactId = null;
            titleElement.textContent = 'إضافة طريقة تواصل جديدة';
            formElement.reset();

            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            console.log('✅ تم فتح Modal بنجاح');
        } catch (error) {
            console.error('❌ خطأ في فتح Modal:', error);
            alert('خطأ في فتح النافذة: ' + error.message);
        }
    }

    // تعديل معلومة تواصل
    function editContact(contactId) {
        const contact = contactsData.find(c => c.id === contactId);
        if (!contact) return;

        editingContactId = contactId;
        document.getElementById('contactModalTitle').textContent = 'تعديل معلومة التواصل';

        // ملء النموذج
        document.getElementById('contact_type').value = contact.contact_type || '';
        document.getElementById('contact_value').value = contact.contact_value;
        document.getElementById('display_label').value = contact.display_label || '';
        document.getElementById('privacy_level').value = contact.privacy_level;
        document.getElementById('is_primary').checked = contact.is_primary;
        document.getElementById('is_public').checked = contact.is_public;

        new bootstrap.Modal(document.getElementById('contactModal')).show();
    }

    // حفظ معلومة التواصل
    function saveContact() {
        console.log('Saving contact...');
        const form = document.getElementById('contactForm');

        if (!form) {
            console.error('Contact form not found');
            showError('لم يتم العثور على النموذج');
            return;
        }

        const formData = new FormData(form);

        const url = editingContactId ? `/dashboard/contacts/${editingContactId}` : '/dashboard/contacts';
        const method = editingContactId ? 'PUT' : 'POST';

        // تحويل FormData إلى JSON
        const data = {};
        formData.forEach((value, key) => {
            if (key === 'is_primary' || key === 'is_public') {
                data[key] = form.querySelector(`[name="${key}"]`).checked;
            } else {
                data[key] = value;
            }
        });

        console.log('Sending data:', data);
        console.log('URL:', url);
        console.log('Method:', method);

        fetch(url, {
            method: method,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            console.log('Save response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Save response data:', data);
            if (data.success) {
                const modalElement = document.getElementById('contactModal');
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                }
                loadContacts();
                showSuccess(data.message);
            } else {
                console.error('Save failed:', data);
                showError(data.message || 'فشل في حفظ معلومة التواصل');
                if (data.errors) {
                    console.error('Validation errors:', data.errors);
                }
            }
        })
        .catch(error => {
            console.error('Error saving contact:', error);
            showError('حدث خطأ في حفظ معلومة التواصل: ' + error.message);
        });
    }

    // حذف معلومة تواصل
    function deleteContact(contactId) {
        console.log('Deleting contact:', contactId);

        if (!confirm('هل أنت متأكد من حذف معلومة التواصل هذه؟')) {
            return;
        }

        fetch(`/dashboard/contacts/${contactId}`, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('Delete response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Delete response data:', data);
            if (data.success) {
                loadContacts();
                showSuccess(data.message);
            } else {
                console.error('Delete failed:', data);
                showError(data.message || 'فشل في حذف معلومة التواصل');
            }
        })
        .catch(error => {
            console.error('Error deleting contact:', error);
            showError('حدث خطأ في حذف معلومة التواصل: ' + error.message);
        });
    }

    // تبديل حالة العرض العام
    function toggleContactPublic(contactId) {
        console.log('Toggling public status for contact:', contactId);

        fetch(`/dashboard/contacts/${contactId}/toggle-public`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('Toggle public response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Toggle public response data:', data);
            if (data.success) {
                loadContacts();
                showSuccess(data.message);
            } else {
                console.error('Toggle public failed:', data);
                showError(data.message || 'فشل في تحديث حالة العرض');
            }
        })
        .catch(error => {
            console.error('Error toggling public status:', error);
            showError('حدث خطأ في تحديث حالة العرض: ' + error.message);
        });
    }

    // تحديث معلومات التواصل
    function refreshContacts() {
        loadContacts();
    }

    // إظهار مساعدة معلومات التواصل
    function showContactsHelp() {
        alert('معلومات التواصل:\n\n• الأساسي: الطريقة الرئيسية للتواصل من نوعها\n• متحقق: تم التحقق من صحة المعلومة\n• عام: يظهر للجميع\n• للمسجلين: يظهر للمستخدمين المسجلين فقط\n• خاص: لا يظهر في الإعلانات');
    }

    // وظائف مساعدة
    function getContactIcon(type) {
        const icons = {
            'phone': 'fas fa-phone',
            'whatsapp': 'fab fa-whatsapp',
            'email': 'fas fa-envelope',
            'telegram': 'fab fa-telegram',
            'instagram': 'fab fa-instagram',
            'facebook': 'fab fa-facebook',
            'twitter': 'fab fa-twitter',
            'linkedin': 'fab fa-linkedin',
            'website': 'fas fa-globe',
            'other': 'fas fa-link'
        };
        return icons[type] || 'fas fa-link';
    }

    function getContactTypeName(type) {
        const names = {
            'phone': 'هاتف',
            'whatsapp': 'واتساب',
            'email': 'إيميل',
            'telegram': 'تليجرام',
            'instagram': 'إنستجرام',
            'facebook': 'فيسبوك',
            'twitter': 'تويتر',
            'linkedin': 'لينكد إن',
            'website': 'موقع إلكتروني',
            'other': 'أخرى'
        };
        return names[type] || 'غير محدد';
    }

    function getPrivacyLevelName(level) {
        const names = {
            'public': 'عام',
            'registered_users': 'للمسجلين',
            'private': 'خاص'
        };
        return names[level] || 'غير محدد';
    }

    function showSuccess(message) {
        console.log('Success:', message);

        // إنشاء toast notification للنجاح
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast, { delay: 4000 });
        bsToast.show();

        // إزالة العنصر بعد الإخفاء
        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    }

    function showError(message) {
        console.error('Error:', message);

        // إنشاء toast notification للخطأ
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast, { delay: 6000 });
        bsToast.show();

        // إزالة العنصر بعد الإخفاء
        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    }
});
</script>
@endpush

<!-- Modal إضافة/تعديل معلومة التواصل -->
<div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactModalTitle">إضافة طريقة تواصل جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="contactForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contact_type" class="form-label">نوع التواصل</label>
                            <select class="form-select" id="contact_type" name="contact_type">
                                <option value="">اختر نوع التواصل</option>
                                <option value="phone">هاتف</option>
                                <option value="whatsapp">واتساب</option>
                                <option value="email">إيميل</option>
                                <option value="telegram">تليجرام</option>
                                <option value="instagram">إنستجرام</option>
                                <option value="facebook">فيسبوك</option>
                                <option value="twitter">تويتر</option>
                                <option value="linkedin">لينكد إن</option>
                                <option value="website">موقع إلكتروني</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contact_value" class="form-label">قيمة التواصل *</label>
                            <input type="text" class="form-control" id="contact_value" name="contact_value"
                                   placeholder="مثال: +967771234567" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="display_label" class="form-label">تسمية مخصصة</label>
                            <input type="text" class="form-control" id="display_label" name="display_label"
                                   placeholder="مثال: هاتف العمل">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="privacy_level" class="form-label">مستوى الخصوصية</label>
                            <select class="form-select" id="privacy_level" name="privacy_level" required>
                                <option value="public">عام للجميع</option>
                                <option value="registered_users">للمستخدمين المسجلين فقط</option>
                                <option value="private">خاص (لا يظهر)</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_primary" name="is_primary">
                                <label class="form-check-label" for="is_primary">
                                    طريقة التواصل الأساسية من هذا النوع
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_public" name="is_public" checked>
                                <label class="form-check-label" for="is_public">
                                    إظهار في الإعلانات
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveContact()">حفظ</button>
            </div>
        </div>
    </div>
</div>
