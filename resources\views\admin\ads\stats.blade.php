@extends('layouts.app')

@section('title', __('Ads Statistics') . ' - ' . __('Admin Panel'))

@section('content')
<div class="container-fluid my-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 fw-bold text-primary arabic-text">
                        <i class="fas fa-chart-bar me-2"></i>
                        {{ __('Detailed Ads Statistics') }}
                    </h1>
                    <p class="text-muted arabic-text">{{ __('Comprehensive reports on ads performance on the site') }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.ads.index') }}" class="btn btn-outline-primary me-2 arabic-text">
                        <i class="fas fa-list me-1"></i>
                        {{ __('Manage Ads') }}
                    </a>
                    <a href="{{ route('admin.settings') }}" class="btn btn-outline-secondary arabic-text">
                        <i class="fas fa-cog me-1"></i>
                        {{ __('Settings') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ $stats['total_ads'] }}</h4>
                            <p class="mb-0 arabic-text">{{ __('Total Ads') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullhorn fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ $stats['pending_ads'] }}</h4>
                            <p class="mb-0 arabic-text">{{ __('Pending Review') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ $stats['active_ads'] }}</h4>
                            <p class="mb-0 arabic-text">{{ __('Active Ads') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{ $stats['rejected_ads'] }}</h4>
                            <p class="mb-0 arabic-text">{{ __('Rejected Ads') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات زمنية -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>
                        {{ __('Today Ads') }}
                    </h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="text-info fw-bold">{{ $stats['ads_today'] }}</h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('New ads today') }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-week me-2"></i>
                        {{ __('Weekly Ads') }}
                    </h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="text-success fw-bold">{{ $stats['ads_this_week'] }}</h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Ads this week') }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        {{ __('Monthly Ads') }}
                    </h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="text-warning fw-bold">{{ $stats['ads_this_month'] }}</h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Ads this month') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات التصنيفات -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-th-large me-2"></i>
                        {{ __('Categories Statistics') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="arabic-text">{{ __('Category') }}</th>
                                    <th class="arabic-text">{{ __('Total Ads') }}</th>
                                    <th class="arabic-text">{{ __('Active Ads') }}</th>
                                    <th class="arabic-text">{{ __('Percentage') }}</th>
                                    <th class="arabic-text">{{ __('Status') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($categoryStats as $category)
                                <tr>
                                    <td>
                                        <i class="{{ $category->icon }} me-2 text-primary"></i>
                                        {{ $category->name }}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $category->ads_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $category->active_ads_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $percentage = $stats['total_ads'] > 0 ? round(($category->ads_count / $stats['total_ads']) * 100, 1) : 0;
                                        @endphp
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar"
                                                 style="width: {{ $percentage }}%"
                                                 aria-valuenow="{{ $percentage }}"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                                {{ $percentage }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($category->is_active)
                                            <span class="badge bg-success arabic-text">{{ __('Active') }}</span>
                                        @else
                                            <span class="badge bg-secondary arabic-text">{{ __('Inactive') }}</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعلانات المميزة -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        {{ __('Featured Ads') }}
                    </h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="text-warning fw-bold">{{ $stats['featured_ads'] }}</h2>
                    <p class="text-muted mb-3 arabic-text">{{ __('Featured ads') }}</p>
                    <div class="progress mb-3">
                        @php
                            $featuredPercentage = $stats['total_ads'] > 0 ? round(($stats['featured_ads'] / $stats['total_ads']) * 100, 1) : 0;
                        @endphp
                        <div class="progress-bar bg-warning" role="progressbar"
                             style="width: {{ $featuredPercentage }}%">
                            {{ $featuredPercentage }}%
                        </div>
                    </div>
                    <small class="text-muted arabic-text">{{ __('Of total ads') }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- أحدث الإعلانات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        {{ __('Latest Ads') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="arabic-text">{{ __('Title') }}</th>
                                    <th class="arabic-text">{{ __('Category') }}</th>
                                    <th class="arabic-text">{{ __('User') }}</th>
                                    <th class="arabic-text">{{ __('Status') }}</th>
                                    <th class="arabic-text">{{ __('Created Date') }}</th>
                                    <th class="arabic-text">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentAds as $ad)
                                <tr>
                                    <td>
                                        <strong>{{ Str::limit($ad->title, 30) }}</strong>
                                    </td>
                                    <td>
                                        <i class="{{ $ad->category->icon }} me-1"></i>
                                        {{ $ad->category->name }}
                                    </td>
                                    <td>{{ $ad->user->name }}</td>
                                    <td>
                                        @if($ad->status === 'pending')
                                            <span class="badge bg-warning arabic-text">{{ __('Pending Review') }}</span>
                                        @elseif($ad->status === 'active')
                                            <span class="badge bg-success arabic-text">{{ __('Active') }}</span>
                                        @else
                                            <span class="badge bg-danger arabic-text">{{ __('Rejected') }}</span>
                                        @endif
                                    </td>
                                    <td>{{ $ad->created_at->diffForHumans() }}</td>
                                    <td>
                                        <a href="{{ route('admin.ads.show', $ad) }}"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
