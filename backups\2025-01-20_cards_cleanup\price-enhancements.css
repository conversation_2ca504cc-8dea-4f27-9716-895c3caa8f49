/* تحسينات إضافية لعرض الأسعار والعروض */

/* تأثيرات متقدمة للخصومات */
.discount-tag::before {
    content: '';
    position: absolute;
    top: 100%;
    right: 0;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 6px solid rgba(220, 38, 38, 0.8);
}

/* تأثير shimmer للعروض المحدودة */
.limited-offer-mini {
    background: linear-gradient(
        90deg,
        #f59e0b 0%,
        #fbbf24 25%,
        #f59e0b 50%,
        #fbbf24 75%,
        #f59e0b 100%
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* تأثير نبضة للأسعار الجديدة */
.price-current {
    position: relative;
}

.price-current::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(5, 150, 105, 0.2) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    border-radius: 50%;
    animation: priceGlow 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes priceGlow {
    0%, 100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

/* تحسين تأثير hover للبطاقات */
.ad-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(59, 130, 246, 0.05) 0%,
        rgba(16, 185, 129, 0.05) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.ad-card:hover::before {
    opacity: 1;
}

/* تأثير متدرج للأسعار - خلفية شفافة */
.price-display-container {
    background: transparent;
    /* إزالة الخلفية المتدرجة لتجنب التداخل مع شارات الأسعار */
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* تحسين الظلال للعمق البصري */
.price-badge {
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.price-display-container {
    /* إزالة الظلال لتجنب التداخل مع شارات الأسعار */
}

/* تأثير loading skeleton للأسعار */
.price-skeleton {
    background: linear-gradient(
        90deg,
        #f0f0f0 25%,
        #e0e0e0 50%,
        #f0f0f0 75%
    );
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    height: 20px;
    margin: 4px 0;
}

@keyframes loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* تحسين الطباعة */
.price-current,
.price-main {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: -0.5px;
    text-rendering: optimizeLegibility;
}

/* تأثيرات الانتقال المحسنة */
.price-section-modern * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين الألوان للوضع الليلي */
@media (prefers-color-scheme: dark) {
    .price-display-container {
        background: transparent;
        /* خلفية شفافة حتى في الوضع الليلي */
    }
    
    .price-current {
        color: #10b981 !important;
    }
    
    .price-main {
        color: #10b981 !important;
    }
    
    .price-original {
        color: #94a3b8;
    }
}

/* تحسينات الطباعة */
@media print {
    .price-section-modern {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
    
    .discount-tag,
    .mini-badge {
        background: #333 !important;
        color: white !important;
    }
}

/* تحسين الوصولية */
.price-badge:focus,
.mini-badge:focus {
    outline: 2px solid #10b981;
    outline-offset: 2px;
}

/* تأثير تفاعلي للمس */
@media (hover: none) and (pointer: coarse) {
    .price-display-container:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    .price-badge:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
}

/* تحسين الأداء */
.price-section-modern {
    will-change: transform;
    contain: layout style paint;
}

.discount-tag {
    will-change: transform;
    contain: layout style paint;
}

/* تأثيرات متقدمة للتفاعل */
.price-current:hover {
    text-shadow: 0 0 8px rgba(5, 150, 105, 0.4);
}

.savings-compact:hover {
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    transform: translateY(-1px);
}

/* تحسين التباعد للنصوص العربية */
.price-badge span,
.mini-badge span {
    line-height: 1.2;
    font-feature-settings: 'kern' 1, 'liga' 1;
}
