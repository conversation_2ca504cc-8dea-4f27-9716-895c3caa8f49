/*
 * ملف CSS موحد لنظام التواصل الشامل
 * يجمع أيقونات التواصل ومعلومات التواصل في حل واحد
 * يدعم: الأيقونات، التفاصيل، النظام المتقدم مع الكشف المتدرج
 * 
 * الميزات:
 * - أيقونات دائرية/مربعة/مبسطة
 * - معلومات تواصل مفصلة
 * - نظام كشف متدرج للأمان
 * - دعم contact_info القديم
 * - تصميم متجاوب كامل
 */

/* ===== قسم التواصل الرئيسي ===== */
.contact-section {
    width: 100%;
}

.contact-detailed {
    padding: 0.5rem 0;
}

.contact-compact {
    padding: 0.25rem 0;
}

/* ===== أيقونات التواصل ===== */
.contact-icons-section {
    padding: 0;
    background: transparent;
    border: none;
}

.contact-icons-container {
    display: flex;
    justify-content: left;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.contact-icon-item {
    flex: 0 0 auto;
}

.contact-icon {
    display: inline-flex;
     align-items: center;
    justify-content: center; 
    width: 35px;
    height: 35px;
    text-decoration: none;
     border-radius: 50%; 
    transition: all 0.3s ease;
     /* background: white;  */
    border: 0px solid;
     box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.contact-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.contact-icon:hover::before {
    left: 100%;
}

.contact-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    text-decoration: none;
}

.contact-icon i {
    font-size: 1.2rem;
    margin: 0;
    transition: all 0.3s ease;
}

.icon-label {
    display: none; /* إخفاء النصوص لتبسيط التصميم */
}

/* ===== ألوان مخصصة لكل نوع - تصميم دائري مبسط ===== */
.phone-icon {
    color: #28a745;
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.phone-icon:hover {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.whatsapp-icon {
    color: #25d366;
    border-color: #25d366;
    background: rgba(37, 211, 102, 0.1);
}

.whatsapp-icon:hover {
    background: #25d366;
    color: white;
    border-color: #25d366;
}

.email-icon {
    color: #007bff;
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.email-icon:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* ===== معلومات التواصل المفصلة ===== */
.contact-details-section {
    margin-top: 0.75rem;
}

.contact-item {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(59, 130, 246, 0.02);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(59, 130, 246, 0.05);
    border-color: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.contact-item-simple {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-item-simple:last-child {
    border-bottom: none;
}

/* ===== نظام الكشف المتدرج ===== */
.contact-preview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.masked-info {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #6b7280;
    letter-spacing: 1px;
    background: rgba(255, 255, 255, 0.8);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.reveal-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: none;
    color: white;
    font-weight: 600;
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.reveal-btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.contact-revealed {
    padding: 0.75rem;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 8px;
    border-right: 4px solid #10b981;
    animation: fadeInUp 0.5s ease-out;
}

.full-info {
    font-weight: 700;
    color: #10b981;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem;
    border-radius: 6px;
    border: 1px solid rgba(16, 185, 129, 0.2);
    display: inline-block;
    margin: 0.5rem 0;
}

.contact-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.75rem;
}

.contact-actions .btn {
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

/* ===== معلومات التواصل القديمة ===== */
.contact-info-legacy {
    padding: 0.5rem;
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.1);
    border-radius: 8px;
    border-left: 3px solid #f59e0b;
}

/* ===== إحصائيات التواصل ===== */
.contact-stats {
    padding: 0.5rem;
    background: rgba(107, 114, 128, 0.05);
    border: 1px solid rgba(107, 114, 128, 0.1);
    border-radius: 6px;
    text-align: center;
}

/* ===== تفاصيل التواصل للعرض المفصل ===== */
.contact-info-details {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #dee2e6;
    text-align: center;
}

.contact-detail-item {
    margin: 0.25rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* ===== تحسينات للأجهزة المحمولة ===== */
@media (max-width: 576px) {
    .contact-icons-container {
        gap: 0.4rem;
    }
    
    .contact-icon {
        width: 36px;
        height: 36px;
    }
    
    .contact-icon i {
        font-size: 1.1rem;
    }
    
    .contact-info-details {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
    }
    
    .contact-detail-item {
        font-size: 0.8rem;
    }
}

/* ===== تحسينات للأجهزة اللوحية ===== */
@media (min-width: 577px) and (max-width: 768px) {
    .contact-icons-container {
        gap: 0.75rem;
    }
    
    .contact-icon-item {
        min-width: 85px;
        max-width: 110px;
    }
}

/* ===== تحسينات للعرض المضغوط ===== */
.ad-card-compact .contact-icons-section {
    margin: 0.5rem 0;
}

.ad-card-compact .contact-icon {
    width: 36px;
    height: 36px;
}

.ad-card-compact .contact-icon i {
    font-size: 1.1rem;
}

/* ===== تحسينات للعرض المفصل ===== */
.ad-card-detailed .contact-icons-section {
    margin: 1.25rem 0;
    padding: 1rem;
}

.ad-card-detailed .contact-icons-container {
    gap: 1.25rem;
}

.ad-card-detailed .contact-icon-item {
    min-width: 100px;
    max-width: 140px;
}

.ad-card-detailed .contact-icon {
    padding: 1rem 0.75rem;
}

.ad-card-detailed .contact-icon i {
    font-size: 1.75rem;
}

.ad-card-detailed .icon-label {
    font-size: 0.8rem;
}

/* ===== تحسينات للطباعة ===== */
@media print {
    .contact-icons-section {
        background: white !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .contact-icon {
        background: white !important;
        color: #000 !important;
        border: 1px solid #000 !important;
    }
    
    .contact-icon:hover {
        transform: none !important;
        box-shadow: none !important;
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.contact-icon:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .contact-icon,
    .contact-icon i,
    .icon-label,
    .contact-icons-section {
        transition: none !important;
        animation: none !important;
    }
    
    .contact-icon:hover {
        transform: none !important;
    }
    
    .contact-icon::before {
        display: none !important;
    }
}

/* ===== تحسينات للشاشات عالية الدقة ===== */
@media (min-resolution: 2dppx) {
    .contact-icon {
        border-width: 1px;
    }
}

/* ===== تأثيرات الحركة ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
