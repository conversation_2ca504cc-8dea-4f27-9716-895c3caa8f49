<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Ad;
use App\Models\User;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PriceDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now()
        ]);

        $this->category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'icon' => 'fas fa-test',
            'is_active' => true
        ]);
    }

    /** @test */
    public function it_displays_free_price_correctly()
    {
        $ad = Ad::create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'title' => 'Free Test Ad',
            'slug' => 'free-test-ad',
            'description' => 'Test description',
            'is_free' => true,
            'price_type' => 'free',
            'status' => 'active',
            'expires_at' => now()->addDays(30)
        ]);

        $response = $this->get(route('ads.index'));

        $response->assertStatus(200);
        $response->assertSee('price-free-modern');
        $response->assertSee('fas fa-gift');
    }

    /** @test */
    public function it_displays_price_on_request_correctly()
    {
        $ad = Ad::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'price_type' => 'on_request'
        ]);

        $response = $this->get(route('ads.index'));
        
        $response->assertStatus(200);
        $response->assertSee('price-on-request-modern');
        $response->assertSee('fas fa-phone');
    }

    /** @test */
    public function it_displays_regular_price_correctly()
    {
        $ad = Ad::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'price' => 1000,
            'currency' => 'YER',
            'price_type' => 'fixed'
        ]);

        $response = $this->get(route('ads.index'));
        
        $response->assertStatus(200);
        $response->assertSee('price-regular-modern');
        $response->assertSee('1,000');
    }

    /** @test */
    public function it_displays_discounted_price_correctly()
    {
        $ad = Ad::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'price' => 800,
            'original_price' => 1000,
            'currency' => 'YER',
            'price_type' => 'fixed'
        ]);

        $response = $this->get(route('ads.index'));
        
        $response->assertStatus(200);
        $response->assertSee('price-with-discount-modern');
        $response->assertSee('discount-tag');
        $response->assertSee('-20%'); // نسبة الخصم
        $response->assertSee('800'); // السعر الحالي
        $response->assertSee('1,000'); // السعر الأصلي
    }

    /** @test */
    public function it_displays_negotiable_badge()
    {
        $ad = Ad::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'price' => 1000,
            'is_negotiable' => true,
            'price_type' => 'fixed'
        ]);

        $response = $this->get(route('ads.index'));
        
        $response->assertStatus(200);
        $response->assertSee('negotiable-mini');
        $response->assertSee('fas fa-handshake');
    }

    /** @test */
    public function it_displays_limited_offer_badge()
    {
        $ad = Ad::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'price' => 800,
            'original_price' => 1000,
            'is_limited_offer' => true,
            'price_type' => 'fixed'
        ]);

        $response = $this->get(route('ads.index'));
        
        $response->assertStatus(200);
        $response->assertSee('limited-offer-mini');
        $response->assertSee('fas fa-clock');
    }

    /** @test */
    public function it_calculates_savings_amount_correctly()
    {
        $ad = Ad::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'price' => 750,
            'original_price' => 1000,
            'currency' => 'YER',
            'price_type' => 'fixed'
        ]);

        $response = $this->get(route('ads.index'));
        
        $response->assertStatus(200);
        $response->assertSee('savings-compact');
        $response->assertSee('250'); // مبلغ التوفير
    }

    /** @test */
    public function it_includes_price_enhancement_css()
    {
        $response = $this->get(route('ads.index'));
        
        $response->assertStatus(200);
        $response->assertSee('css/price-enhancements.css');
    }

    /** @test */
    public function it_includes_price_interactions_js()
    {
        $response = $this->get(route('ads.index'));
        
        $response->assertStatus(200);
        $response->assertSee('js/price-interactions.js');
    }

    /** @test */
    public function price_component_renders_correctly()
    {
        $ad = Ad::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'price' => 1500,
            'currency' => 'YER',
            'price_type' => 'fixed'
        ]);

        $component = $this->blade('<x-price-display :ad="$ad" />', ['ad' => $ad]);
        
        $component->assertSee('price-section-modern');
        $component->assertSee('1,500');
    }

    /** @test */
    public function compact_price_component_renders_correctly()
    {
        $ad = Ad::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'price' => 1200,
            'currency' => 'YER',
            'price_type' => 'fixed'
        ]);

        $component = $this->blade('<x-price-display :ad="$ad" :compact="true" />', ['ad' => $ad]);
        
        $component->assertSee('price-section-modern');
        $component->assertSee('price-compact');
        $component->assertSee('1,200');
    }
}
