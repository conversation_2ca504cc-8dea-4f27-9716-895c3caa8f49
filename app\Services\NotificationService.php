<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\Ad;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * خدمة الإشعارات
 * تدير إرسال وإدارة الإشعارات في النظام
 */
class NotificationService
{
    /**
     * إرسال إشعار لمستخدم واحد
     */
    public static function sendToUser(
        int $userId,
        string $title,
        string $message,
        string $type = Notification::TYPE_INFO,
        array $data = [],
        ?string $actionUrl = null,
        string $priority = Notification::PRIORITY_NORMAL
    ): ?Notification {
        try {
            $notification = Notification::createNotification(
                $userId,
                $title,
                $message,
                $type,
                $data,
                $actionUrl,
                $priority
            );

            // مسح كاش عدد الإشعارات غير المقروءة
            self::clearUserNotificationCache($userId);

            Log::info('تم إرسال إشعار جديد', [
                'user_id' => $userId,
                'title' => $title,
                'type' => $type,
                'priority' => $priority
            ]);

            return $notification;

        } catch (\Exception $e) {
            Log::error('فشل في إرسال الإشعار', [
                'user_id' => $userId,
                'title' => $title,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * إرسال إشعار لعدة مستخدمين
     */
    public static function sendToMultipleUsers(
        array $userIds,
        string $title,
        string $message,
        string $type = Notification::TYPE_INFO,
        array $data = [],
        ?string $actionUrl = null,
        string $priority = Notification::PRIORITY_NORMAL
    ): int {
        $sentCount = 0;

        foreach ($userIds as $userId) {
            if (self::sendToUser($userId, $title, $message, $type, $data, $actionUrl, $priority)) {
                $sentCount++;
            }
        }

        return $sentCount;
    }

    /**
     * إرسال إشعار لجميع المستخدمين
     */
    public static function broadcastToAll(
        string $title,
        string $message,
        string $type = Notification::TYPE_SYSTEM,
        string $priority = Notification::PRIORITY_NORMAL
    ): int {
        try {
            $count = Notification::broadcastToAll($title, $message, $type, $priority);
            
            // مسح جميع كاش الإشعارات
            self::clearAllNotificationCache();

            Log::info('تم إرسال إشعار جماعي', [
                'title' => $title,
                'type' => $type,
                'users_count' => $count
            ]);

            return $count;

        } catch (\Exception $e) {
            Log::error('فشل في إرسال الإشعار الجماعي', [
                'title' => $title,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * إشعار موافقة على الإعلان
     */
    public static function notifyAdApproved(Ad $ad): ?Notification
    {
        return self::sendToUser(
            $ad->user_id,
            'تم قبول إعلانك',
            "تم قبول إعلان \"{$ad->title}\" ونشره بنجاح",
            Notification::TYPE_AD_APPROVED,
            ['ad_id' => $ad->id],
            route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]),
            Notification::PRIORITY_HIGH
        );
    }

    /**
     * إشعار رفض الإعلان
     */
    public static function notifyAdRejected(Ad $ad, ?string $reason = null): ?Notification
    {
        $message = "تم رفض إعلان \"{$ad->title}\"";
        if ($reason) {
            $message .= "\nالسبب: {$reason}";
        }

        return self::sendToUser(
            $ad->user_id,
            'تم رفض إعلانك',
            $message,
            Notification::TYPE_AD_REJECTED,
            ['ad_id' => $ad->id, 'reason' => $reason],
            route('my-ads.index'),
            Notification::PRIORITY_HIGH
        );
    }

    /**
     * إشعار انتهاء صلاحية الإعلان
     */
    public static function notifyAdExpired(Ad $ad): ?Notification
    {
        return self::sendToUser(
            $ad->user_id,
            'انتهت صلاحية إعلانك',
            "انتهت صلاحية إعلان \"{$ad->title}\". يمكنك تجديده من لوحة التحكم",
            Notification::TYPE_AD_EXPIRED,
            ['ad_id' => $ad->id],
            route('my-ads.index'),
            Notification::PRIORITY_NORMAL
        );
    }

    /**
     * إشعار تحذيري قبل انتهاء الصلاحية
     */
    public static function notifyAdExpiringSoon(Ad $ad, int $daysLeft): ?Notification
    {
        return self::sendToUser(
            $ad->user_id,
            'إعلانك سينتهي قريباً',
            "سينتهي إعلان \"{$ad->title}\" خلال {$daysLeft} أيام. يمكنك تجديده الآن",
            Notification::TYPE_WARNING,
            ['ad_id' => $ad->id, 'days_left' => $daysLeft],
            route('my-ads.index'),
            Notification::PRIORITY_NORMAL
        );
    }

    /**
     * إشعار ترحيب بالمستخدم الجديد
     */
    public static function welcomeNewUser(User $user): ?Notification
    {
        return self::sendToUser(
            $user->id,
            'مرحباً بك في موقع إعلاناتي',
            "أهلاً وسهلاً {$user->name}! يمكنك الآن إنشاء إعلاناتك وتصفح آلاف الإعلانات الأخرى",
            Notification::TYPE_SUCCESS,
            ['user_id' => $user->id],
            route('ads.create'),
            Notification::PRIORITY_NORMAL
        );
    }

    /**
     * إشعار تحديث النظام
     */
    public static function systemUpdate(string $version, string $features): int
    {
        return self::broadcastToAll(
            'تحديث جديد للنظام',
            "تم تحديث النظام إلى الإصدار {$version}\nالميزات الجديدة: {$features}",
            Notification::TYPE_SYSTEM,
            Notification::PRIORITY_NORMAL
        );
    }

    /**
     * إشعار صيانة النظام
     */
    public static function maintenanceNotice(string $startTime, string $duration): int
    {
        return self::broadcastToAll(
            'إشعار صيانة النظام',
            "سيتم إجراء صيانة للنظام في {$startTime} لمدة {$duration}. نعتذر عن أي إزعاج",
            Notification::TYPE_WARNING,
            Notification::PRIORITY_HIGH
        );
    }

    /**
     * الحصول على الإشعارات الحديثة للمستخدم
     */
    public static function getRecentForUser(int $userId, int $limit = 10)
    {
        $cacheKey = "user_notifications_{$userId}_{$limit}";
        
        return Cache::remember($cacheKey, 300, function () use ($userId, $limit) {
            return Notification::getRecentForUser($userId, $limit);
        });
    }

    /**
     * الحصول على عدد الإشعارات غير المقروءة
     */
    public static function getUnreadCount(int $userId): int
    {
        $cacheKey = "user_unread_notifications_{$userId}";
        
        return Cache::remember($cacheKey, 60, function () use ($userId) {
            return Notification::getUnreadCount($userId);
        });
    }

    /**
     * وضع علامة مقروء على إشعار
     */
    public static function markAsRead(int $notificationId, int $userId): bool
    {
        try {
            $notification = Notification::where('id', $notificationId)
                ->where('user_id', $userId)
                ->first();

            if (!$notification) {
                return false;
            }

            $result = $notification->markAsRead();
            
            if ($result) {
                self::clearUserNotificationCache($userId);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('فشل في وضع علامة مقروء على الإشعار', [
                'notification_id' => $notificationId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * وضع علامة مقروء على جميع الإشعارات
     */
    public static function markAllAsRead(int $userId): bool
    {
        try {
            $count = Notification::markAllAsReadForUser($userId);
            
            self::clearUserNotificationCache($userId);

            Log::info('تم وضع علامة مقروء على جميع الإشعارات', [
                'user_id' => $userId,
                'count' => $count
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('فشل في وضع علامة مقروء على جميع الإشعارات', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * حذف إشعار
     */
    public static function deleteNotification(int $notificationId, int $userId): bool
    {
        try {
            $deleted = Notification::where('id', $notificationId)
                ->where('user_id', $userId)
                ->delete();

            if ($deleted) {
                self::clearUserNotificationCache($userId);
            }

            return $deleted > 0;

        } catch (\Exception $e) {
            Log::error('فشل في حذف الإشعار', [
                'notification_id' => $notificationId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * تنظيف الإشعارات القديمة
     */
    public static function cleanOldNotifications(int $daysToKeep = 90): int
    {
        try {
            $deletedCount = Notification::cleanOldNotifications($daysToKeep);
            
            // مسح جميع الكاش
            self::clearAllNotificationCache();

            Log::info('تم تنظيف الإشعارات القديمة', [
                'deleted_count' => $deletedCount,
                'days_kept' => $daysToKeep
            ]);

            return $deletedCount;

        } catch (\Exception $e) {
            Log::error('فشل في تنظيف الإشعارات القديمة', [
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * مسح كاش الإشعارات للمستخدم
     */
    private static function clearUserNotificationCache(int $userId): void
    {
        Cache::forget("user_unread_notifications_{$userId}");
        
        // مسح كاش الإشعارات الحديثة بأحجام مختلفة
        for ($i = 5; $i <= 20; $i += 5) {
            Cache::forget("user_notifications_{$userId}_{$i}");
        }
    }

    /**
     * مسح جميع كاش الإشعارات
     */
    private static function clearAllNotificationCache(): void
    {
        // يمكن تحسين هذا لاحقاً باستخدام tags في Redis
        Cache::flush();
    }

    /**
     * الحصول على إحصائيات الإشعارات
     */
    public static function getStats(): array
    {
        return Cache::remember('notification_stats', 3600, function () {
            return [
                'total' => Notification::count(),
                'unread' => Notification::unread()->count(),
                'high_priority' => Notification::ofPriority(Notification::PRIORITY_HIGH)->count(),
                'urgent' => Notification::ofPriority(Notification::PRIORITY_URGENT)->count(),
                'today' => Notification::whereDate('created_at', today())->count(),
                'this_week' => Notification::where('created_at', '>=', now()->startOfWeek())->count(),
            ];
        });
    }
}
