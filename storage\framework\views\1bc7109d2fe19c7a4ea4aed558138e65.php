
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['ad', 'detailed' => false, 'showStats' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['ad', 'detailed' => false, 'showStats' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>


<?php
    if ($detailed) {
        Log::info('عرض معلومات صاحب الإعلان المتقدمة', [
            'ad_id' => $ad->id,
            'ad_title' => $ad->title,
            'advertiser_id' => $ad->user->id,
            'advertiser_name' => $ad->user->name,
            'viewer_id' => auth()->id(),
            'ip' => request()->ip(),
            'timestamp' => now()
        ]);
    }
?>

<div class="advertiser-section <?php echo e($detailed ? 'advertiser-detailed' : 'advertiser-compact'); ?>">
    
    <div class="advertiser-profile">
        
        <div class="advertiser-avatar">
            <?php if($ad->user->avatar): ?>
                <img src="<?php echo e($ad->user->avatar_url); ?>" 
                     alt="<?php echo e($ad->user->name); ?>" 
                     class="avatar-image">
            <?php else: ?>
                <div class="avatar-placeholder">
                    <i class="fas fa-user"></i>
                </div>
            <?php endif; ?>
            
            
            <div class="user-badges">
                <?php if($ad->user->is_verified): ?>
                    <span class="badge-verified" title="مستخدم موثق">
                        <i class="fas fa-check-circle"></i>
                    </span>
                <?php endif; ?>
                
                <?php if($ad->user->is_admin): ?>
                    <span class="badge-admin" title="مدير">
                        <i class="fas fa-crown"></i>
                    </span>
                <?php endif; ?>
                
                <?php if($ad->user->is_premium): ?>
                    <span class="badge-premium" title="عضوية مميزة">
                        <i class="fas fa-star"></i>
                    </span>
                <?php endif; ?>
            </div>
        </div>

        
        <div class="advertiser-details">
            <div class="advertiser-name">
                <h6 class="name-text"><?php echo e($ad->user->name); ?></h6>
                <?php if($detailed && $ad->user->company_name): ?>
                    <p class="company-name"><?php echo e($ad->user->company_name); ?></p>
                <?php endif; ?>
            </div>

            
            <div class="join-date">
                <i class="fas fa-calendar-alt text-muted me-1"></i>
                <small class="text-muted">
                    عضو منذ <?php echo e($ad->user->created_at->format('Y')); ?>

                </small>
            </div>

            
            <?php if($detailed && $ad->user->last_seen_at): ?>
                <div class="last-seen">
                    <i class="fas fa-clock text-muted me-1"></i>
                    <small class="text-muted">
                        آخر نشاط <?php echo e($ad->user->last_seen_at->diffForHumans()); ?>

                    </small>
                </div>
            <?php endif; ?>
        </div>
    </div>

    
    <?php if($detailed && $showStats): ?>
        <div class="advertiser-stats">
            <div class="stats-grid">
                
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-bullhorn text-primary"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number"><?php echo e($ad->user->activeAds()->count()); ?></span>
                        <span class="stat-label">إعلان نشط</span>
                    </div>
                </div>

                
                <?php if($ad->user->average_rating > 0): ?>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <span class="stat-number"><?php echo e(number_format($ad->user->average_rating, 1)); ?></span>
                            <span class="stat-label">متوسط التقييم</span>
                        </div>
                    </div>
                <?php endif; ?>

                
                <?php if($ad->user->received_reviews_count > 0): ?>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-comments text-info"></i>
                        </div>
                        <div class="stat-content">
                            <span class="stat-number"><?php echo e($ad->user->received_reviews_count); ?></span>
                            <span class="stat-label">تقييم</span>
                        </div>
                    </div>
                <?php endif; ?>

                
                <?php if($ad->user->favorites_count > 0): ?>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-heart text-danger"></i>
                        </div>
                        <div class="stat-content">
                            <span class="stat-number"><?php echo e($ad->user->favorites_count); ?></span>
                            <span class="stat-label">مفضلة</span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            
            <?php if($detailed && $ad->user->average_rating > 0): ?>
                <div class="quick-rating">
                    <div class="rating-stars">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <?php if($i <= floor($ad->user->average_rating)): ?>
                                <i class="fas fa-star text-warning"></i>
                            <?php elseif($i - 0.5 <= $ad->user->average_rating): ?>
                                <i class="fas fa-star-half-alt text-warning"></i>
                            <?php else: ?>
                                <i class="far fa-star text-muted"></i>
                            <?php endif; ?>
                        <?php endfor; ?>
                        <span class="rating-text">
                            (<?php echo e($ad->user->received_reviews_count); ?> تقييم)
                        </span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    
    <?php if($detailed): ?>
        <div class="advertiser-actions">
            
            <a href="<?php echo e(route('ads.by-user', $ad->user->id)); ?>" 
               class="btn btn-outline-primary btn-sm">
                <i class="fas fa-list me-1"></i>
                جميع الإعلانات (<?php echo e($ad->user->activeAds()->count()); ?>)
            </a>

            
            <?php if(auth()->guard()->check()): ?>
                <?php if(auth()->id() !== $ad->user->id): ?>
                    <button class="btn btn-outline-success btn-sm" 
                            onclick="openDirectMessage(<?php echo e($ad->user->id); ?>)">
                        <i class="fas fa-envelope me-1"></i>
                        رسالة مباشرة
                    </button>
                <?php endif; ?>
            <?php endif; ?>

            
            <?php if(auth()->guard()->check()): ?>
                <?php if(auth()->id() !== $ad->user->id): ?>
                    <button class="btn btn-outline-warning btn-sm" 
                            onclick="reportUser(<?php echo e($ad->user->id); ?>)">
                        <i class="fas fa-flag me-1"></i>
                        إبلاغ
                    </button>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    
    <?php if($detailed): ?>
        <div class="trust-indicators">
            <div class="trust-items">
                <?php if($ad->user->is_verified): ?>
                    <div class="trust-item verified">
                        <i class="fas fa-shield-alt text-success me-1"></i>
                        <small>هوية موثقة</small>
                    </div>
                <?php endif; ?>

                <?php if($ad->user->phone_verified_at): ?>
                    <div class="trust-item phone-verified">
                        <i class="fas fa-phone text-success me-1"></i>
                        <small>هاتف موثق</small>
                    </div>
                <?php endif; ?>

                <?php if($ad->user->email_verified_at): ?>
                    <div class="trust-item email-verified">
                        <i class="fas fa-envelope text-success me-1"></i>
                        <small>إيميل موثق</small>
                    </div>
                <?php endif; ?>

                
                <?php
                    $trustScore = 0;
                    if($ad->user->is_verified) $trustScore += 30;
                    if($ad->user->phone_verified_at) $trustScore += 25;
                    if($ad->user->email_verified_at) $trustScore += 20;
                    if($ad->user->average_rating >= 4) $trustScore += 25;
                    $trustScore = min($trustScore, 100);
                ?>

                <?php if($trustScore > 0): ?>
                    <div class="trust-score">
                        <div class="trust-score-bar">
                            <div class="trust-score-fill" style="width: <?php echo e($trustScore); ?>%"></div>
                        </div>
                        <small class="trust-score-text">
                            مؤشر الثقة: <?php echo e($trustScore); ?>%
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/partials/advertiser-info.blade.php ENDPATH**/ ?>