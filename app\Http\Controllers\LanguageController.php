<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;

/**
 * كونترولر اللغات لتغيير اللغة بين العربية والإنجليزية
 */
class LanguageController extends Controller
{
    /**
     * تغيير لغة الموقع
     */
    public function switch($locale)
    {
        try {
            // تنظيف وفحص المدخل لحماية من XSS
            $locale = strip_tags(trim($locale));

            // التحقق من أن اللغة مدعومة
            $supportedLocales = $this->getSupportedLocales();

            if (!$this->isValidLocale($locale, $supportedLocales)) {
                // إذا كانت اللغة غير مدعومة، استخدم العربية كافتراضي
                $locale = 'ar';
            }

            // حفظ اللغة في الجلسة والتطبيق
            $this->setLocale($locale);

            // مسح الكاش إذا كان موجود
            $this->clearTranslationCache();

            // رسالة تأكيد حسب اللغة
            $message = $this->getSuccessMessage($locale);

            // إنشاء Cookie آمن
            $cookie = $this->createLocaleCookie($locale);

            // تحديد الصفحة المقصودة للإعادة التوجيه
            $redirectUrl = $this->getRedirectUrl();

            // إضافة headers لمنع الكاش
            $response = redirect($redirectUrl)
                ->with('success', $message)
                ->withCookie($cookie);

            // إضافة headers لضمان تحديث الصفحة
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');

            return $response;

        } catch (\Exception $e) {
            // تسجيل الخطأ
            \Log::error('خطأ في تغيير اللغة: ' . $e->getMessage());

            // العودة مع رسالة خطأ
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تغيير اللغة. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * عرض معلومات تشخيص اللغة (للتطوير فقط)
     */
    public function debug()
    {
        // التحقق من وضع التطوير
        if (!config('app.debug')) {
            abort(404);
        }

        $currentLocale = App::getLocale();
        $sessionLocale = Session::get('locale');
        $cookieLocale = request()->cookie('locale');
        $configLocale = config('app.locale');
        $fallbackLocale = config('app.fallback_locale');

        // تحديد مصدر اللغة الحالية
        $localeSource = 'default';
        if ($sessionLocale) {
            $localeSource = 'session';
        } elseif ($cookieLocale) {
            $localeSource = 'cookie';
        } elseif ($currentLocale === $configLocale) {
            $localeSource = 'config';
        }

        $debugInfo = [
            'current_locale' => $currentLocale,
            'locale_source' => $localeSource,
            'direction' => $currentLocale === 'ar' ? 'rtl' : 'ltr',
            'available_locales' => [
                'ar' => 'العربية',
                'en' => 'English'
            ],
            'storage_info' => [
                'session_locale' => $sessionLocale,
                'cookie_locale' => $cookieLocale,
                'config_locale' => $configLocale,
                'fallback_locale' => $fallbackLocale,
            ],
            'environment' => [
                'app_debug' => config('app.debug'),
                'app_env' => config('app.env'),
                'timezone' => config('app.timezone'),
            ],
            'request_info' => [
                'user_agent' => request()->userAgent(),
                'accept_language' => request()->header('Accept-Language'),
                'url' => request()->fullUrl(),
                'method' => request()->method(),
            ],
            'timestamp' => now()->toDateTimeString(),
        ];

        // إذا كان الطلب يتوقع JSON، أرجع JSON
        if (request()->wantsJson() || request()->has('json')) {
            return response()->json($debugInfo, 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }

        // وإلا أرجع صفحة HTML منسقة
        return view('debug.language', compact('debugInfo'));
    }

    /**
     * تطبيق اللغة من الجلسة أو Cookie
     * يتم استدعاؤها في middleware
     */
    public static function applyLocaleFromStorage()
    {
        $controller = new self();
        $locale = $controller->resolveLocaleFromStorage();

        App::setLocale($locale);

        // حفظ اللغة في الجلسة إذا لم تكن محفوظة
        if (!Session::has('locale')) {
            Session::put('locale', $locale);
        }
    }

    /**
     * تحديد اللغة من التخزين (الجلسة أو Cookie)
     */
    private function resolveLocaleFromStorage(): string
    {
        $supportedLocales = $this->getSupportedLocales();

        // الأولوية الأولى: الجلسة
        $locale = Session::get('locale');
        if ($locale && $this->isValidLocale($locale, $supportedLocales)) {
            return $locale;
        }

        // الأولوية الثانية: Cookie
        $cookieLocale = Cookie::get('locale');
        if ($cookieLocale && $this->isValidLocale($cookieLocale, $supportedLocales)) {
            return $cookieLocale;
        }

        // الأولوية الثالثة: الإعدادات الافتراضية
        return config('app.locale', 'ar');
    }

    /**
     * التحقق من صحة اللغة
     */
    private function isValidLocale(string $locale, array $supportedLocales): bool
    {
        $locale = strip_tags(trim($locale));
        return preg_match('/^[a-z]{2}$/', $locale) && in_array($locale, $supportedLocales);
    }

    /**
     * الحصول على قائمة اللغات المدعومة
     */
    private function getSupportedLocales(): array
    {
        return config('app.supported_locales', ['ar', 'en']);
    }

    /**
     * تطبيق اللغة على الجلسة والتطبيق
     */
    private function setLocale(string $locale): void
    {
        Session::put('locale', $locale);
        App::setLocale($locale);
    }

    /**
     * الحصول على رسالة النجاح حسب اللغة
     */
    private function getSuccessMessage(string $locale): string
    {
        return $locale === 'ar'
            ? 'تم تغيير اللغة إلى العربية بنجاح'
            : 'Language changed to English successfully';
    }

    /**
     * إنشاء Cookie آمن للغة
     */
    private function createLocaleCookie(string $locale): \Illuminate\Cookie\CookieJar|\Symfony\Component\HttpFoundation\Cookie
    {
        return cookie('locale', $locale, 60 * 24 * 30, null, null, true, false); // 30 يوم، آمن
    }

    /**
     * الحصول على اتجاه النص حسب اللغة
     */
    public static function getDirection($locale = null)
    {
        $locale = $locale ?: App::getLocale();
        return $locale === 'ar' ? 'rtl' : 'ltr';
    }

    /**
     * الحصول على اسم اللغة
     */
    public static function getLanguageName($locale = null)
    {
        $locale = $locale ?: App::getLocale();

        $languages = [
            'ar' => 'العربية',
            'en' => 'English'
        ];

        return $languages[$locale] ?? 'العربية';
    }

    /**
     * الحصول على اللغة المقابلة (للتبديل)
     */
    public static function getAlternateLocale($locale = null)
    {
        $locale = $locale ?: App::getLocale();
        return $locale === 'ar' ? 'en' : 'ar';
    }

    /**
     * الحصول على اللغة من Cookie
     */
    public static function getLocaleFromCookie()
    {
        return Cookie::get('locale', config('app.locale', 'ar'));
    }

    /**
     * حفظ اللغة في Cookie
     */
    public static function setLocaleCookie($locale, $minutes = 43200) // 30 يوماً افتراضياً
    {
        return Cookie::make('locale', $locale, $minutes);
    }

    /**
     * مسح كاش الترجمة
     */
    private function clearTranslationCache()
    {
        try {
            // مسح كاش Laravel
            if (function_exists('cache')) {
                cache()->forget('translations');
                cache()->forget('locale');
                cache()->forget('laravel_session');
            }

            // مسح كاش الملفات إذا كان موجود
            if (class_exists('\Artisan')) {
                \Artisan::call('cache:clear');
                \Artisan::call('view:clear');
                \Artisan::call('config:clear');
            }

            // مسح كاش المتصفح عبر headers
            if (function_exists('header')) {
                header('Cache-Control: no-cache, no-store, must-revalidate');
                header('Pragma: no-cache');
                header('Expires: 0');
            }
        } catch (\Exception $e) {
            \Log::warning('تعذر مسح كاش الترجمة: ' . $e->getMessage());
        }
    }

    /**
     * تحديد الصفحة المقصودة للإعادة التوجيه
     */
    private function getRedirectUrl(): string
    {
        // الحصول على الـ referrer من الطلب
        $referrer = request()->header('referer');

        // التحقق من أن الـ referrer صالح وليس صفحة تغيير اللغة
        if ($referrer && !str_contains($referrer, '/language/')) {
            // التحقق من أن الـ referrer من نفس الدومين
            $currentDomain = request()->getSchemeAndHttpHost();
            if (str_starts_with($referrer, $currentDomain)) {
                return $referrer;
            }
        }

        // إذا لم يكن هناك referrer صالح، ارجع للصفحة الرئيسية
        return route('home');
    }

}
