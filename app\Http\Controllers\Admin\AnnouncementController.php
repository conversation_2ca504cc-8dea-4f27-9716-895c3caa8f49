<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Announcement;
use App\Models\Setting;
use App\Services\InputSanitizationService;

/**
 * كونترولر إدارة الإعلانات
 * يدير إعلانات وإعدادات شريط الإعلانات المتحرك
 */
class AnnouncementController extends Controller
{
    /**
     * عرض قائمة الإعلانات
     */
    public function index()
    {
        $announcements = Announcement::ordered()->get();
        $settings = (object) [
            'is_enabled' => Setting::get('announcements_enabled', true),
            'animation_type' => Setting::get('announcements_animation_type', 'rotation'),
            'transition_duration' => Setting::get('announcements_transition_duration', 5),
            'scroll_speed' => Setting::get('announcements_scroll_speed', 30)
        ];

        return view('admin.announcements.index', compact('announcements', 'settings'));
    }

    /**
     * عرض نموذج إنشاء إعلان جديد
     */
    public function create()
    {
        return view('admin.announcements.create');
    }

    /**
     * حفظ إعلان جديد
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title_ar' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content_ar' => 'required|string|max:2000',
            'content_en' => 'required|string|max:2000',
            'icon' => 'required|string|max:50',
            'color' => 'required|string|max:20',
            'sort_order' => 'required|integer|min:0'
        ]);

        // تنظيف البيانات المدخلة لحماية من XSS
        $validated = InputSanitizationService::sanitizeFormData($validated);

        Announcement::create($validated);

        return redirect()->route('admin.announcements.index')
            ->with('success', 'تم إنشاء الإعلان بنجاح');
    }

    /**
     * عرض تفاصيل إعلان
     */
    public function show(Announcement $announcement)
    {
        return view('admin.announcements.show', compact('announcement'));
    }

    /**
     * عرض نموذج تعديل إعلان
     */
    public function edit(Announcement $announcement)
    {
        return view('admin.announcements.edit', compact('announcement'));
    }

    /**
     * تحديث إعلان
     */
    public function update(Request $request, Announcement $announcement)
    {
        $request->validate([
            'title_ar' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content_ar' => 'required|string',
            'content_en' => 'required|string',
            'icon' => 'required|string|max:50',
            'color' => 'required|string|max:20',
            'sort_order' => 'required|integer|min:0'
        ]);

        $announcement->update($request->all());

        return redirect()->route('admin.announcements.index')
            ->with('success', 'تم تحديث الإعلان بنجاح');
    }

    /**
     * حذف إعلان
     */
    public function destroy(Announcement $announcement)
    {
        $announcement->delete();

        return redirect()->route('admin.announcements.index')
            ->with('success', 'تم حذف الإعلان بنجاح');
    }

    /**
     * تحديث إعدادات الإعلانات
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'animation_type' => 'required|in:rotation,marquee',
            'transition_duration' => 'required|integer|min:1|max:60',
            'scroll_speed' => 'required|integer|min:5|max:60'
        ]);

        // معالجة checkbox التفعيل
        $data = $request->all();
        $data['is_enabled'] = $request->has('is_enabled') && $request->is_enabled == '1';

        // تحديث الإعدادات في النظام الموحد
        Setting::set('announcements_enabled', $data['is_enabled'], 'boolean', 'announcements');
        Setting::set('announcements_animation_type', $data['animation_type'], 'string', 'announcements');
        Setting::set('announcements_transition_duration', $data['transition_duration'], 'integer', 'announcements');
        Setting::set('announcements_scroll_speed', $data['scroll_speed'], 'integer', 'announcements');

        // إضافة معلومات إضافية للرسالة
        $message = 'تم تحديث الإعدادات بنجاح';
        if (!$data['is_enabled']) {
            $message .= ' - تم إخفاء شريط الإعلانات';
        } else {
            $animationType = $data['animation_type'] === 'marquee' ? 'التمرير الأفقي' : 'التبديل التلقائي';
            $message .= ' - نوع الحركة: ' . $animationType;
        }

        return redirect()->route('admin.announcements.index')
            ->with('success', $message)
            ->with('settings_updated', true);
    }

    /**
     * تبديل حالة تفعيل إعلان
     */
    public function toggleStatus(Announcement $announcement)
    {
        $announcement->update(['is_active' => !$announcement->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة الإعلان بنجاح',
            'is_active' => $announcement->is_active
        ]);
    }
}
