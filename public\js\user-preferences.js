/**
 * إعدادات المستخدم للميزات المحسنة
 * يسمح للمستخدم بتفعيل/إلغاء الميزات الجديدة
 */

class UserPreferences {
    constructor() {
        this.init();
    }

    init() {
        this.createPreferencesPanel();
        this.loadSavedPreferences();
    }

    /**
     * إنشاء لوحة الإعدادات
     */
    createPreferencesPanel() {
        // إنشاء زر الإعدادات
        const settingsButton = document.createElement('button');
        settingsButton.id = 'preferences-toggle';
        settingsButton.className = 'btn btn-sm btn-outline-secondary position-fixed';
        settingsButton.style.cssText = 'bottom: 20px; left: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;';
        settingsButton.innerHTML = '<i class="fas fa-cog"></i>';
        settingsButton.title = 'إعدادات التصفح';

        // إنشاء لوحة الإعدادات
        const preferencesPanel = document.createElement('div');
        preferencesPanel.id = 'preferences-panel';
        preferencesPanel.className = 'position-fixed bg-white border rounded shadow-lg p-3';
        preferencesPanel.style.cssText = 'bottom: 80px; left: 20px; z-index: 1001; min-width: 250px; display: none;';
        
        preferencesPanel.innerHTML = `
            <h6 class="mb-3">إعدادات التصفح</h6>
            
            <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="ajax-navigation" checked>
                <label class="form-check-label" for="ajax-navigation">
                    التنقل السريع بين الإعلانات
                </label>
                <small class="form-text text-muted d-block">تحميل الإعلانات بدون تحديث الصفحة</small>
            </div>
            
            <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="enhanced-toast" checked>
                <label class="form-check-label" for="enhanced-toast">
                    الرسائل المحسنة
                </label>
                <small class="form-text text-muted d-block">رسائل تفاعلية بدلاً من النوافذ المنبثقة</small>
            </div>
            
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="keyboard-navigation" checked>
                <label class="form-check-label" for="keyboard-navigation">
                    التنقل بلوحة المفاتيح
                </label>
                <small class="form-text text-muted d-block">استخدام الأسهم للتنقل بين الإعلانات</small>
            </div>
            
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-primary" onclick="userPreferences.savePreferences()">حفظ</button>
                <button class="btn btn-sm btn-secondary" onclick="userPreferences.resetPreferences()">إعادة تعيين</button>
            </div>
        `;

        document.body.appendChild(settingsButton);
        document.body.appendChild(preferencesPanel);

        // ربط الأحداث
        settingsButton.addEventListener('click', () => {
            const panel = document.getElementById('preferences-panel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        });

        // إغلاق اللوحة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#preferences-panel') && !e.target.closest('#preferences-toggle')) {
                document.getElementById('preferences-panel').style.display = 'none';
            }
        });
    }

    /**
     * تحميل الإعدادات المحفوظة
     */
    loadSavedPreferences() {
        const ajaxNav = localStorage.getItem('ajax_navigation') !== 'disabled';
        const enhancedToast = localStorage.getItem('enhanced_toast') !== 'disabled';
        const keyboardNav = localStorage.getItem('keyboard_navigation') !== 'disabled';

        document.getElementById('ajax-navigation').checked = ajaxNav;
        document.getElementById('enhanced-toast').checked = enhancedToast;
        document.getElementById('keyboard-navigation').checked = keyboardNav;
    }

    /**
     * حفظ الإعدادات
     */
    savePreferences() {
        const ajaxNav = document.getElementById('ajax-navigation').checked;
        const enhancedToast = document.getElementById('enhanced-toast').checked;
        const keyboardNav = document.getElementById('keyboard-navigation').checked;

        localStorage.setItem('ajax_navigation', ajaxNav ? 'enabled' : 'disabled');
        localStorage.setItem('enhanced_toast', enhancedToast ? 'enabled' : 'disabled');
        localStorage.setItem('keyboard_navigation', keyboardNav ? 'enabled' : 'disabled');

        // إظهار رسالة تأكيد
        if (window.showToast && enhancedToast) {
            window.showToast('تم حفظ الإعدادات بنجاح', 'success');
        } else {
            alert('تم حفظ الإعدادات بنجاح');
        }

        // إخفاء اللوحة
        document.getElementById('preferences-panel').style.display = 'none';
    }

    /**
     * إعادة تعيين الإعدادات
     */
    resetPreferences() {
        localStorage.removeItem('ajax_navigation');
        localStorage.removeItem('enhanced_toast');
        localStorage.removeItem('keyboard_navigation');

        document.getElementById('ajax-navigation').checked = true;
        document.getElementById('enhanced-toast').checked = true;
        document.getElementById('keyboard-navigation').checked = true;

        if (window.showToast) {
            window.showToast('تم إعادة تعيين الإعدادات', 'info');
        } else {
            alert('تم إعادة تعيين الإعدادات');
        }
    }

    /**
     * فحص ما إذا كانت ميزة معينة مفعلة
     */
    isFeatureEnabled(feature) {
        return localStorage.getItem(feature) !== 'disabled';
    }
}

// تهيئة إعدادات المستخدم
document.addEventListener('DOMContentLoaded', () => {
    window.userPreferences = new UserPreferences();
});

// تصدير للاستخدام العام
window.UserPreferences = UserPreferences;
