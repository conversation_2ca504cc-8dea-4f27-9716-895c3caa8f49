/*
 * ملف CSS متقدم لإحصائيات التفاعل والمفضلة
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على أنماط متقدمة لعرض الإحصائيات والرسوم البيانية
 */

/* ===== متغيرات الألوان للإحصائيات ===== */
:root {
    --stats-primary: #3b82f6;
    --stats-success: #10b981;
    --stats-warning: #f59e0b;
    --stats-danger: #ef4444;
    --stats-info: #06b6d4;
    --stats-purple: #8b5cf6;
    --stats-bg: #f8fafc;
    --stats-border: #e5e7eb;
    --stats-text: #374151;
    --stats-muted: #6b7280;
    --chart-bg: #ffffff;
    --chart-grid: #f3f4f6;
}

/* ===== قسم الإحصائيات الرئيسي ===== */
.interaction-stats-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.stats-detailed {
    padding: 2.5rem;
}

.stats-compact {
    padding: 1.5rem;
}

.stats-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--stats-border);
}

.stats-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--stats-text);
    margin: 0;
}

.stats-summary {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.engagement-rate {
    background: linear-gradient(135deg, var(--stats-success) 0%, #059669 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== بطاقات الإحصائيات الرئيسية ===== */
.main-stats {
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--stats-bg);
    border: 1px solid var(--stats-border);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--stats-primary), var(--stats-success));
}

.views-card::before {
    background: linear-gradient(90deg, var(--stats-primary), #2563eb);
}

.favorites-card::before {
    background: linear-gradient(90deg, var(--stats-danger), #dc2626);
}

.ratings-card::before {
    background: linear-gradient(90deg, var(--stats-warning), #d97706);
}

.comments-card::before {
    background: linear-gradient(90deg, var(--stats-info), #0891b2);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--stats-primary);
}

.views-card .stat-icon {
    color: var(--stats-primary);
}

.favorites-card .stat-icon {
    color: var(--stats-danger);
}

.ratings-card .stat-icon {
    color: var(--stats-warning);
}

.comments-card .stat-icon {
    color: var(--stats-info);
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--stats-text);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--stats-muted);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-trend {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.trend-up {
    color: var(--stats-success);
}

.trend-down {
    color: var(--stats-danger);
}

.trend-stable {
    color: var(--stats-muted);
}

.stat-average {
    margin-top: 0.5rem;
}

.rating-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.1rem;
    font-size: 0.9rem;
}

/* ===== الإحصائيات الإضافية ===== */
.additional-stats {
    background: var(--chart-bg);
    border: 1px solid var(--stats-border);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.stat-item:hover {
    background: var(--stats-bg);
}

.stat-icon-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    background: var(--stats-bg);
}

.stat-details {
    flex: 1;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--stats-text);
    line-height: 1;
}

.stat-name {
    font-size: 0.85rem;
    color: var(--stats-muted);
    margin-top: 0.25rem;
}

/* ===== الرسم البياني البسيط ===== */
.stats-chart {
    background: var(--chart-bg);
    border: 1px solid var(--stats-border);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--stats-text);
    margin-bottom: 1.5rem;
}

.chart-container {
    position: relative;
    height: 200px;
}

.chart-bars {
    display: flex;
    align-items: end;
    justify-content: space-around;
    height: 100%;
    padding: 1rem 0;
}

.chart-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 80px;
    position: relative;
}

.bar-fill {
    width: 40px;
    min-height: 10px;
    border-radius: 4px 4px 0 0;
    transition: all 0.5s ease;
    position: relative;
    margin-bottom: 0.5rem;
}

.views-bar {
    background: linear-gradient(180deg, var(--stats-primary), #1d4ed8);
}

.favorites-bar {
    background: linear-gradient(180deg, var(--stats-danger), #dc2626);
}

.ratings-bar {
    background: linear-gradient(180deg, var(--stats-warning), #d97706);
}

.comments-bar {
    background: linear-gradient(180deg, var(--stats-info), #0891b2);
}

.bar-label {
    font-size: 0.8rem;
    color: var(--stats-muted);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.bar-value {
    font-size: 0.9rem;
    font-weight: 700;
    color: var(--stats-text);
}

/* ===== المستخدمون الذين أضافوا للمفضلة ===== */
.recent-favorites {
    background: var(--chart-bg);
    border: 1px solid var(--stats-border);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--stats-text);
    margin-bottom: 1rem;
}

.favorites-users {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.favorite-user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: var(--stats-bg);
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.favorite-user:hover {
    background: var(--stats-border);
    transform: translateY(-1px);
}

.user-avatar-xs {
    flex-shrink: 0;
}

.avatar-xs {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid white;
}

.avatar-placeholder-xs {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--stats-border);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--stats-muted);
    font-size: 0.8rem;
    border: 2px solid white;
}

.user-info {
    min-width: 0;
}

.user-name {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--stats-text);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.favorite-date {
    font-size: 0.75rem;
    color: var(--stats-muted);
}

.more-favorites {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--stats-bg);
    padding: 0.75rem;
    border-radius: 8px;
    border: 2px dashed var(--stats-border);
    min-width: 80px;
}

.more-count {
    font-size: 1rem;
    font-weight: 700;
    color: var(--stats-primary);
}

.more-text {
    font-size: 0.75rem;
    color: var(--stats-muted);
}

/* ===== أزرار الإجراءات ===== */
.stats-actions {
    background: var(--stats-bg);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid var(--stats-border);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.last-updated {
    display: flex;
    align-items: center;
}

/* ===== الاستجابة للأجهزة المحمولة ===== */
@media (max-width: 768px) {
    .interaction-stats-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .stats-detailed {
        padding: 1.5rem;
    }
    
    .stats-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .stats-summary {
        align-self: stretch;
        justify-content: center;
    }
    
    .stat-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .stat-icon {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 150px;
    }
    
    .chart-bars {
        padding: 0.5rem 0;
    }
    
    .bar-fill {
        width: 30px;
    }
    
    .favorites-users {
        justify-content: center;
    }
    
    .favorite-user {
        padding: 0.5rem;
    }
    
    .stats-actions {
        padding: 0.75rem;
    }
    
    .stats-actions .d-flex {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .action-buttons {
        justify-content: center;
    }
    
    .last-updated {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .stat-card {
        padding: 0.75rem;
    }
    
    .stat-icon {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
    
    .stat-number {
        font-size: 1.25rem;
    }
    
    .additional-stats,
    .stats-chart,
    .recent-favorites {
        padding: 1rem;
    }
    
    .chart-container {
        height: 120px;
    }
    
    .bar-fill {
        width: 25px;
    }
    
    .bar-label,
    .bar-value {
        font-size: 0.75rem;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .interaction-stats-section {
        box-shadow: none;
        border: 1px solid var(--stats-border);
    }
    
    .stats-actions {
        display: none !important;
    }
    
    .stat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--stats-border);
    }
    
    .chart-bars {
        print-color-adjust: exact;
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.stat-card:focus {
    outline: 2px solid var(--stats-primary);
    outline-offset: 2px;
}

.btn:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

/* ===== تأثيرات حركية ===== */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-number {
    animation: countUp 0.6s ease-out;
}

@keyframes barGrow {
    from {
        height: 0;
    }
    to {
        height: var(--bar-height);
    }
}

.bar-fill {
    animation: barGrow 1s ease-out 0.3s both;
}

/* ===== تحسينات الوضع المظلم (معطل مؤقتاً) ===== */
/* تم إلغاء تفعيل الوضع المظلم مؤقتاً - سيتم تفعيله لاحقاً عند الطلب */
/*
@media (prefers-color-scheme: dark) {
    :root {
        --stats-bg: #1f2937;
        --stats-border: #374151;
        --stats-text: #f9fafb;
        --stats-muted: #9ca3af;
        --chart-bg: #111827;
        --chart-grid: #374151;
    }

    .interaction-stats-section {
        background: #111827;
        color: #f9fafb;
    }

    .stats-title {
        color: #f9fafb;
    }
}
*/
