# Migrations Backup

هذا المجلد يحتوي على نسخة احتياطية من الـ migrations القديمة قبل إعادة الهيكلة.

## الـ Migrations المحذوفة:

### 1. User-related migrations:
- `2024_01_15_000001_add_security_fields_to_users_table.php`
- `2025_07_02_225229_add_card_fields_to_users_table.php`
- `2025_07_23_041427_create_user_contacts_table.php`
- `2025_07_23_051709_create_user_privacy_settings_table.php`

### 2. Settings migrations:
- `2025_07_02_034820_create_announcement_settings_table.php`
- `2025_07_04_042516_create_ad_settings_table.php`

### 3. Logs migrations:
- `2025_07_05_005848_create_security_logs_table.php`
- `2025_07_05_011832_create_search_logs_table.php`
- `2025_07_23_052056_create_contact_access_logs_table.php`

### 4. Interaction migrations:
- `2025_07_17_120000_create_favorites_table.php`
- `2025_07_20_074233_create_ratings_table.php`
- `2025_07_20_075742_create_comments_table.php`
- `2025_07_05_013306_create_reviews_table.php`

### 5. Other migrations:
- `2025_07_02_034716_create_announcements_table.php`
- `2025_07_02_225301_create_feedbacks_table.php`
- `2025_07_05_012639_create_notifications_table.php`
- `2025_07_10_020548_fix_user_password_timestamps.php`

## الهيكل الجديد:

تم دمج جميع هذه الـ migrations في ملف واحد محسن:
`2025_07_25_100000_create_optimized_database_structure.php`

## الفوائد:

1. **تقليل عدد الملفات**: من 20+ migration إلى 1 فقط
2. **هيكل موحد**: جميع الجداول في مكان واحد
3. **علاقات محسنة**: تصميم أفضل للعلاقات بين الجداول
4. **فهارس محسنة**: فهارس مدروسة لتحسين الأداء
5. **سهولة الصيانة**: أسهل في الفهم والتطوير

## كيفية التراجع:

إذا احتجت للتراجع، يمكنك:
1. حذف الـ migration الجديد
2. استعادة الـ migrations القديمة من هذا المجلد
3. تشغيل `php artisan migrate:fresh`
