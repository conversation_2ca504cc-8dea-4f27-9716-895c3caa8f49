@extends('layouts.app')

@section('title', __('My Ads'))

@section('content')
<div class="container my-5">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 fw-bold text-primary">
                        <i class="fas fa-list-alt me-2"></i>
                        {{ __('My Ads') }}
                    </h1>
                    <p class="text-muted">{{ __('Manage all your ads from one place') }}</p>
                </div>
                <div>
                    @auth
                        <a href="{{ route('ads.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            {{ __('Add New Ad') }}
                        </a>
                    @else
                        <a href="{{ route('login') }}" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            {{ __('Login to add ad') }}
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4>{{ $ads->where('status', 'active')->count() }}</h4>
                    <small>{{ __('Active Ads') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4>{{ $ads->where('status', 'pending')->count() }}</h4>
                    <small>{{ __('Pending Approval') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-eye fa-2x mb-2"></i>
                    <h4>{{ $ads->sum('views_count') }}</h4>
                    <small>{{ __('Total Views') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-list fa-2x mb-2"></i>
                    <h4>{{ $ads->total() }}</h4>
                    <small>{{ __('Total Ads') }}</small>
                </div>
            </div>
        </div>
    </div>

    @if($ads->count() > 0)
        <!-- قائمة الإعلانات -->
        <div class="row">
            @foreach($ads as $ad)
                <div class="col-lg-6 mb-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge 
                                    @if($ad->status === 'active') bg-success
                                    @elseif($ad->status === 'pending') bg-warning
                                    @elseif($ad->status === 'rejected') bg-danger
                                    @else bg-secondary
                                    @endif">
                                    @if($ad->status === 'active') {{ __('Active') }}
                                    @elseif($ad->status === 'pending') {{ __('Pending Approval') }}
                                    @elseif($ad->status === 'rejected') {{ __('Rejected') }}
                                    @else {{ __('Inactive') }}
                                    @endif
                                </span>
                                @if($ad->is_featured)
                                    <span class="badge bg-warning text-dark ms-1">
                                        <i class="fas fa-star me-1"></i>{{ __('Featured') }}
                                    </span>
                                @endif
                            </div>
                            <small class="text-muted">
                                {{ $ad->created_at->diffForHumans() }}
                            </small>
                        </div>
                        
                        <div class="card-body">
                            <div class="row">
                                @if($ad->image_url)
                                    <div class="col-4">
                                        <img src="{{ $ad->image_url }}"
                                             alt="{{ $ad->title }}"
                                             class="img-fluid rounded">
                                    </div>
                                    <div class="col-8">
                                @else
                                    <div class="col-12">
                                @endif
                                    <h5 class="card-title">{{ $ad->title }}</h5>
                                    <p class="card-text text-muted small">
                                        {{ Str::limit($ad->description, 100) }}
                                    </p>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-tag me-1"></i>
                                            {{ $ad->category->name }}
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            {{ $ad->location }}
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i>
                                            {{ $ad->views_count }} {{ __('views') }}
                                        </small>
                                    </div>
                                    
                                    @if($ad->expires_at)
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar-alt me-1"></i>
                                                {{ __('Expires on') }}: {{ $ad->expires_at->format('Y-m-d') }}
                                                @if($ad->expires_at->isPast())
                                                    <span class="text-danger">({{ __('Expired') }})</span>
                                                {{-- شارة "ينتهي قريباً" مخفية --}}
                                                {{-- @elseif($ad->expires_at->diffInDays() <= 7)
                                                    <span class="text-warning">({{ __('Expires Soon') }})</span> --}}
                                                @endif
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group" role="group">
                                    @if($ad->status === 'active')
                                        <a href="{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>{{ __('View') }}
                                        </a>
                                    @endif
                                    <a href="{{ route('ads.edit', $ad) }}" 
                                       class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-edit me-1"></i>{{ __('Edit') }}
                                    </a>
                                </div>
                                
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        onclick="confirmDelete({{ $ad->id }})">
                                    <i class="fas fa-trash me-1"></i>{{ __('Delete') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- التصفح -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $ads->links() }}
                </div>
            </div>
        </div>

        <!-- نماذج حذف الإعلانات (مخفية) -->
        @foreach($ads as $ad)
            <form id="deleteForm{{ $ad->id }}" action="{{ route('ads.destroy', $ad) }}" method="POST" style="display: none;">
                @csrf
                @method('DELETE')
            </form>
        @endforeach

    @else
        <!-- رسالة عدم وجود إعلانات -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-5x text-muted mb-3"></i>
                    <h3 class="text-muted">{{ __('No ads yet') }}</h3>
                    <p class="text-muted mb-4">{{ __('Start by creating your first ad now') }}</p>
                    <a href="{{ route('ads.create') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        {{ __('Create New Ad') }}
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
function confirmDelete(adId) {
    if (confirm('{{ __("Are you sure you want to delete this ad? This action cannot be undone.") }}')) {
        document.getElementById('deleteForm' + adId).submit();
    }
}
</script>
@endsection
