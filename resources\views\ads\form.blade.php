@extends('layouts.app')

@section('title', $isEdit ? __('Edit Ad') : __('Create New Ad'))

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- عنوان الصفحة -->
            <div class="text-center mb-4">
                <h1 class="display-6 fw-bold {{ $isEdit ? 'text-warning' : 'text-primary' }}">
                    <i class="fas {{ $isEdit ? 'fa-edit' : 'fa-plus-circle' }} me-2"></i>
                    {{ $isEdit ? __('Edit Ad') : __('Create New Ad') }}
                </h1>
                <p class="text-muted">
                    {{ $isEdit ? __('Update your ad data') : __('Add your ad now and reach thousands of users') }}
                </p>
            </div>

            <!-- نموذج الإعلان -->
            <div class="card shadow-lg border-0">
                <div class="card-header {{ $isEdit ? 'bg-warning text-dark' : 'bg-primary text-white' }}">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        {{ $isEdit ? __('Update Ad Details') : __('Ad Details') }}
                    </h5>
                </div>
                
                <div class="card-body p-4">
                    <form action="{{ $isEdit ? route('ads.update', $ad) : route('ads.store') }}" 
                          method="POST" 
                          enctype="multipart/form-data" 
                          id="adForm">
                        @csrf
                        @if($isEdit)
                            @method('PUT')
                        @endif
                        
                        <!-- العناوين -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="title_ar" class="form-label fw-bold">
                                    <i class="fas fa-heading me-1"></i>
                                    {{ __('Title in Arabic') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control @error('title_ar') is-invalid @enderror" 
                                       id="title_ar" 
                                       name="title_ar" 
                                       value="{{ old('title_ar', $isEdit ? $ad->title_ar : '') }}"
                                       placeholder="{{ __('Enter ad title in Arabic') }}"
                                       required>
                                @error('title_ar')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="title_en" class="form-label fw-bold">
                                    <i class="fas fa-heading me-1"></i>
                                    {{ __('Title in English') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control @error('title_en') is-invalid @enderror" 
                                       id="title_en" 
                                       name="title_en" 
                                       value="{{ old('title_en', $isEdit ? $ad->title_en : '') }}"
                                       placeholder="{{ __('Enter ad title in English') }}"
                                       required>
                                @error('title_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- الأوصاف -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="description_ar" class="form-label fw-bold">
                                    <i class="fas fa-align-left me-1"></i>
                                    {{ __('Description in Arabic') }} <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control @error('description_ar') is-invalid @enderror" 
                                          id="description_ar" 
                                          name="description_ar" 
                                          rows="5"
                                          placeholder="{{ __('Write detailed description in Arabic') }}"
                                          required>{{ old('description_ar', $isEdit ? $ad->description_ar : '') }}</textarea>
                                @error('description_ar')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="description_en" class="form-label fw-bold">
                                    <i class="fas fa-align-left me-1"></i>
                                    {{ __('Description in English') }} <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control @error('description_en') is-invalid @enderror" 
                                          id="description_en" 
                                          name="description_en" 
                                          rows="5"
                                          placeholder="{{ __('Write detailed description in English') }}"
                                          required>{{ old('description_en', $isEdit ? $ad->description_en : '') }}</textarea>
                                @error('description_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- التصنيف والموقع -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="category_id" class="form-label fw-bold">
                                    <i class="fas fa-tags me-1"></i>
                                    {{ __('Category') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('category_id') is-invalid @enderror" 
                                        id="category_id" 
                                        name="category_id" 
                                        required>
                                    <option value="">{{ __('Choose Category') }}</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" 
                                                {{ old('category_id', $isEdit ? $ad->category_id : '') == $category->id ? 'selected' : '' }}>
                                            {{ app()->getLocale() === 'ar' ? $category->name_ar : $category->name_en }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="location" class="form-label fw-bold">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ __('Ad Location') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control @error('location') is-invalid @enderror" 
                                       id="location" 
                                       name="location" 
                                       value="{{ old('location', $isEdit ? $ad->location : '') }}"
                                       placeholder="{{ __('City or Area') }}"
                                       required>
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- معلومات التواصل -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="phone" class="form-label fw-bold">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ __('Phone Number') }} <span class="text-danger">*</span>
                                </label>
                                <input type="tel" 
                                       class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" 
                                       name="phone" 
                                       value="{{ old('phone', $isEdit ? $ad->phone : '') }}"
                                       placeholder="{{ __('Example: +967 770 119 544') }}"
                                       required>
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="email" class="form-label fw-bold">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ __('Email') }} <span class="text-muted">({{ __('Optional') }})</span>
                                </label>
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email', $isEdit ? $ad->email : '') }}"
                                       placeholder="{{ __('<EMAIL>') }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- تاريخ الانتهاء والصورة -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="expires_at" class="form-label fw-bold">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    {{ __('Ad Expiry Date') }} <span class="text-muted">({{ __('Optional') }})</span>
                                </label>
                                <input type="date" 
                                       class="form-control @error('expires_at') is-invalid @enderror" 
                                       id="expires_at" 
                                       name="expires_at" 
                                       value="{{ old('expires_at', $isEdit && $ad->expires_at ? $ad->expires_at->format('Y-m-d') : '') }}"
                                       min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                                @error('expires_at')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">{{ __('If no date is specified, the ad will remain active indefinitely') }}</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="image" class="form-label fw-bold">
                                    <i class="fas fa-image me-1"></i>
                                    {{ __('Ad Image') }} <span class="text-muted">({{ __('Optional') }})</span>
                                </label>
                                <input type="file" 
                                       class="form-control @error('image') is-invalid @enderror" 
                                       id="image" 
                                       name="image" 
                                       accept="image/jpeg,image/png,image/jpg,image/webp">
                                @error('image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">{{ __('Maximum: 5 MB. Supported types: JPG, PNG, WEBP') }}</div>
                            </div>
                        </div>

                        @if($isEdit && ($ad->main_image || ($ad->images && count($ad->images) > 0)))
                            <!-- عرض الصور الحالية -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <label class="form-label fw-bold">{{ __('Current Images') }}</label>
                                    <div class="text-center">
                                        @if($ad->main_image)
                                            <div class="mb-3">
                                                <h6 class="text-muted">{{ __('Main Image') }}</h6>
                                                <img src="{{ $ad->main_image_url }}"
                                                     alt="{{ $ad->title }}"
                                                     class="img-thumbnail"
                                                     style="max-height: 200px;">
                                            </div>
                                        @endif

                                        @if($ad->images && count($ad->images) > 1)
                                            <div class="mb-3">
                                                <h6 class="text-muted">{{ __('Additional Images') }}</h6>
                                                <div class="row">
                                                    @foreach($ad->image_urls as $index => $imageUrl)
                                                        @if($index > 0) {{-- تخطي الصورة الرئيسية --}}
                                                            <div class="col-md-3 mb-2">
                                                                <img src="{{ $imageUrl }}"
                                                                     alt="{{ $ad->title }}"
                                                                     class="img-thumbnail"
                                                                     style="max-height: 120px;">
                                                            </div>
                                                        @endif
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif

                                        <div class="mt-2">
                                            <small class="text-muted">{{ __('Choose new images to replace current images') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- معاينة الصورة الجديدة -->
                        <div class="row mb-4" id="imagePreview" style="display: none;">
                            <div class="col-12">
                                <div class="text-center">
                                    <img id="previewImg" src="" alt="{{ __('Image Preview') }}" class="img-thumbnail" style="max-height: 200px;">
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeImage()">
                                            <i class="fas fa-trash me-1"></i>{{ __('Remove Image') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{{ $isEdit ? route('ads.my-ads') : route('ads.index') }}" 
                                       class="btn btn-outline-secondary me-md-2">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        {{ __('Cancel') }}
                                    </a>
                                    <button type="submit" class="btn {{ $isEdit ? 'btn-warning' : 'btn-primary' }}" id="submitBtn">
                                        <i class="fas {{ $isEdit ? 'fa-save' : 'fa-plus' }} me-1"></i>
                                        {{ $isEdit ? __('Save Changes') : __('Publish Ad') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- ملاحظات مهمة -->
            <div class="alert {{ $isEdit ? 'alert-warning' : 'alert-info' }} mt-4">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>
                    {{ __('Important Notes') }}
                </h6>
                <ul class="mb-0">
                    @if($isEdit)
                        <li>{{ __('Changes will be reviewed before publishing to ensure compliance with site terms') }}</li>
                        <li>{{ __('If you change the image, the old image will be automatically deleted') }}</li>
                    @else
                        <li>{{ __('Your ad will be reviewed before publishing to ensure compliance with site terms') }}</li>
                    @endif
                    <li>{{ __('Make sure contact information is correct to ensure interested parties can reach you') }}</li>
                    <li>{{ __('Use clear title and description to increase your ad visibility in search results') }}</li>
                    @if(!$isEdit)
                        <li>{{ __('You can edit or delete your ad anytime through "My Ads" page') }}</li>
                    @endif
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة الصور قبل الرفع - محدث للنظام الجديد
document.getElementById('images').addEventListener('change', function(e) {
    const files = e.target.files;
    const previewContainer = document.getElementById('imagePreview');

    if (files.length > 0) {
        previewContainer.innerHTML = '';
        previewContainer.style.display = 'block';

        Array.from(files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageDiv = document.createElement('div');
                    imageDiv.className = 'col-md-3 mb-2';
                    imageDiv.innerHTML = `
                        <div class="position-relative">
                            <img src="${e.target.result}" class="img-thumbnail" style="max-height: 120px;">
                            <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0"
                                    onclick="removePreviewImage(this)" style="transform: translate(50%, -50%);">
                                <i class="fas fa-times"></i>
                            </button>
                            ${index === 0 ? '<small class="badge bg-primary">الصورة الرئيسية</small>' : ''}
                        </div>
                    `;
                    previewContainer.appendChild(imageDiv);
                };
                reader.readAsDataURL(file);
            }
        });
    }
});

// إزالة صورة من المعاينة
function removePreviewImage(button) {
    button.closest('.col-md-3').remove();

    // إذا لم تعد هناك صور، إخفاء المعاينة
    const previewContainer = document.getElementById('imagePreview');
    if (previewContainer.children.length === 0) {
        previewContainer.style.display = 'none';
    }
}

// إزالة جميع الصور
function removeAllImages() {
    document.getElementById('images').value = '';
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('imagePreview').innerHTML = '';
}

// تحسين تجربة المستخدم عند الإرسال
document.getElementById('adForm').addEventListener('submit', function() {
    const submitBtn = document.getElementById('submitBtn');
    const isEdit = {{ $isEdit ? 'true' : 'false' }};
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>' + 
                         (isEdit ? '{{ __("Saving...") }}' : '{{ __("Publishing...") }}');
    submitBtn.disabled = true;
});
</script>
@endsection
