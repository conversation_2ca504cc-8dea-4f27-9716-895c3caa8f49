
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['ad', 'compact' => false, 'detailed' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['ad', 'compact' => false, 'detailed' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>


<?php
    if ($detailed) {
        $ad->logAdvancedPriceView();
    }
?>

<div class="price-section-modern <?php echo e($compact ? 'price-compact' : ''); ?> <?php echo e($detailed ? 'price-detailed' : ''); ?>">
    <?php if($ad->is_free): ?>
        
        <div class="price-badge price-free-modern">
            <i class="fas fa-gift"></i>
            <span>مجاني</span>
        </div>
    <?php elseif($ad->price_type === 'on_request'): ?>
        
        <div class="price-badge price-on-request-modern">
            <i class="fas fa-phone"></i>
            <span>السعر عند الطلب</span>
        </div>
    <?php elseif($ad->price): ?>
        <div class="price-display-container">
            <?php if($ad->hasDiscount()): ?>
                
                <div class="price-with-discount-overlay">
                    
                    <div class="current-price-with-overlay">
                        
                        <div class="discount-tag-overlay-modern">
                            <i class="fas fa-tag"></i>
                            <span><?php echo e($ad->calculated_discount_percentage); ?>%</span>
                        </div>

                        
                        <div class="original-price-overlay-modern">
                            <span><?php echo $ad->getFormattedOriginalPriceWithSmallCurrency(); ?></span>
                        </div>

                        
                        <div class="current-price-content">
                            <span class="price-label-overlay">السعر</span>
                            <span class="price-value-overlay"><?php echo $ad->getFormattedPriceWithSmallCurrency(); ?></span>
                        </div>
                    </div>

                    
                    <div class="savings-badge-improved">
                        <i class="fas fa-piggy-bank"></i>
                        <span>وفر <?php echo e(number_format($ad->savings_amount, 0)); ?> <span class="currency-small"><?php echo e($ad->currency === 'YER' ? 'ر.ي' : $ad->currency); ?></span></span>
                    </div>
                </div>
            <?php else: ?>
                
                <div class="price-regular-improved">
                    <div class="current-price-full-width">
                        <span class="price-label-full">السعر</span>
                        <span class="price-value-full"><?php echo $ad->getFormattedPriceWithSmallCurrency(); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            
            <div class="additional-badges-row">
                <?php if($ad->is_negotiable): ?>
                    <span class="feature-badge negotiable-badge">
                        <i class="fas fa-handshake"></i>
                        <span>قابل للتفاوض</span>
                    </span>
                <?php endif; ?>

                <?php if($ad->is_limited_offer): ?>
                    <span class="feature-badge limited-offer-badge">
                        <i class="fas fa-clock"></i>
                        <span>عرض محدود</span>
                    </span>
                <?php endif; ?>

                
                <?php if($ad->discount_expires_at && !$ad->isDiscountExpired()): ?>
                    <span class="feature-badge expires-badge">
                        <i class="fas fa-hourglass-half"></i>
                        <span>ينتهي في <?php echo e($ad->discount_expires_at->diffForHumans()); ?></span>
                    </span>
                <?php endif; ?>
            </div>

            
            <?php if($detailed && $ad->price_notes): ?>
                <div class="price-notes-detailed">
                    <div class="price-notes-header">
                        <i class="fas fa-info-circle text-info"></i>
                        <span>ملاحظات السعر</span>
                    </div>
                    <div class="price-notes-content">
                        <?php echo e($ad->price_notes); ?>

                    </div>
                </div>
            <?php endif; ?>

            
            <?php if($detailed && $ad->currency): ?>
                <div class="currency-info-detailed">
                    <div class="currency-info-item">
                        <i class="fas fa-coins text-warning"></i>
                        <span>العملة: <?php echo e($ad->currency === 'YER' ? 'ريال يمني' : $ad->currency); ?></span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>


<?php if (! $__env->hasRenderedOnce('9c2db9e4-3f82-4836-a2d1-0bfa62680db7')): $__env->markAsRenderedOnce('9c2db9e4-3f82-4836-a2d1-0bfa62680db7'); ?>
<?php $__env->startPush('styles'); ?>
<style>
/* === أساسيات المكون === */
.price-section-modern {
    width: 100%;
}

.price-compact {
    margin: 0.5rem 0;
}

.price-detailed {
    margin: 1rem 0;
    max-width: 100%;
}

/* === شارات الحالات الخاصة === */
.price-free-modern,
.price-on-request-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price-free-modern {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.price-on-request-modern {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.price-free-modern:hover,
.price-on-request-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* === التصميم الرئيسي مع الشارات على الحواف === */
.price-with-discount-overlay,
.price-regular-improved {
    margin: 0.4rem 0;
}

.price-with-discount-overlay {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

/* الشارة الخضراء الرئيسية */
.current-price-with-overlay,
.current-price-full-width {
    position: relative;
    width: 100%;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-radius: 8px;
    padding: 0.6rem 1rem;
    box-shadow: 0 3px 8px rgba(16, 185, 129, 0.3);
    transition: all 0.3s ease;
    text-align: center;
}

.current-price-with-overlay {
    overflow: visible;
}

.current-price-full-width:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* شارات الحواف - التصميم الحديث */
.discount-tag-overlay-modern {
    position: absolute;
    top: -8px;
    left: -8px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 700;
    box-shadow: 0 3px 8px rgba(239, 68, 68, 0.4);
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

.discount-tag-overlay-modern i {
    font-size: 0.7rem;
}

.original-price-overlay-modern {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    text-decoration: line-through;
    box-shadow: 0 3px 8px rgba(107, 114, 128, 0.4);
    z-index: 2;
}

.original-price-overlay-modern .currency-small {
    font-size: 0.6rem;
}

/* === محتوى الأسعار === */
.current-price-content,
.current-price-full-width {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.current-price-content {
    position: relative;
    z-index: 1;
}

/* تسميات الأسعار */
.price-label-overlay,
.price-label-full {
    font-size: 0.7rem;
    font-weight: 500;
    opacity: 0.9;
    margin-bottom: 0.3rem;
}

/* قيم الأسعار */
.price-value-overlay,
.price-value-full {
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1;
}

/* رموز العملة الصغيرة */
.price-value-overlay .currency-small,
.price-value-full .currency-small,
.currency-small {
    font-size: 0.8em;
    opacity: 0.9;
}

/* === شارة التوفير === */
.savings-badge-improved {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.35rem 0.7rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    margin: 0.1rem auto 0;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
    width: fit-content;
}

/* === الشارات الإضافية === */
.additional-badges-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
    margin-top: 0.3rem;
    justify-content: center;
}

.feature-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.3rem 0.6rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.feature-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.negotiable-badge {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    border: 1px solid rgba(6, 182, 212, 0.3);
}

.limited-offer-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: 1px solid rgba(245, 158, 11, 0.3);
    animation: pulse 2s infinite;
}

/* === حاوي عرض الأسعار === */
.price-display-container {
    background: transparent;
    border-radius: 8px;
    padding: 12px; /* مساحة كافية للعناصر المطلقة الموضع */
    margin: 0;
    position: relative;
    overflow: visible; /* السماح للعناصر بالظهور خارج الحدود */
    /* خلفية شفافة لتجنب التداخل مع شارات الأسعار */
}

/* === التأثيرات والحركات === */
.price-display-container:hover {
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* === الاستجابة للشاشات المختلفة === */
@media (max-width: 768px) {
    .current-price-with-overlay,
    .current-price-full-width {
        padding: 0.5rem 0.8rem;
    }

    .discount-tag-overlay-modern,
    .original-price-overlay-modern {
        font-size: 0.65rem;
        padding: 0.25rem 0.45rem;
        top: -6px;
    }

    .discount-tag-overlay-modern {
        left: -6px;
    }

    .discount-tag-overlay-modern i {
        font-size: 0.6rem;
    }

    .original-price-overlay-modern {
        right: -6px;
    }

    .price-value-overlay,
    .price-value-full {
        font-size: 1.1rem;
    }

    .additional-badges-row {
        gap: 0.25rem;
        margin-top: 0.25rem;
    }

    .feature-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .current-price-with-overlay,
    .current-price-full-width {
        padding: 0.4rem 0.6rem;
    }

    .price-value-overlay,
    .price-value-full {
        font-size: 1rem;
    }

    .price-label-overlay,
    .price-label-full {
        font-size: 0.65rem;
    }

    .savings-badge-improved {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }

    .discount-tag-overlay-modern,
    .original-price-overlay-modern {
        font-size: 0.6rem;
        padding: 0.2rem 0.35rem;
        top: -5px;
    }

    .discount-tag-overlay-modern {
        left: -5px;
    }

    .discount-tag-overlay-modern i {
        font-size: 0.55rem;
    }

    .original-price-overlay-modern {
        right: -5px;
    }
}

/* === تحسينات الوضع المضغوط === */
.price-compact .feature-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.price-compact .savings-badge-improved {
    font-size: 0.75rem;
    padding: 0.3rem 0.5rem;
}

.price-compact .price-display-container {
    padding: 8px; /* مساحة أقل في الوضع المضغوط */
}

/* === أنماط الميزات المتقدمة === */

/* شارة انتهاء الخصم */
.expires-badge {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: 1px solid rgba(239, 68, 68, 0.3);
    animation: pulse 2s infinite;
}

/* ملاحظات السعر المفصلة */
.price-notes-detailed {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    border-right: 4px solid #3b82f6;
}

.price-notes-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.85rem;
    color: #3b82f6;
    margin-bottom: 0.5rem;
}

.price-notes-content {
    font-size: 0.85rem;
    color: #4b5563;
    line-height: 1.5;
    text-align: right;
}

/* معلومات العملة المفصلة */
.currency-info-detailed {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.1);
    border-radius: 6px;
}

.currency-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #92400e;
    font-weight: 500;
}

/* تحسينات للعرض المفصل */
.price-detailed .price-display-container {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price-detailed .additional-badges-row {
    margin-top: 0.75rem;
    justify-content: flex-start;
}

/* ضمان ظهور العناصر في جميع الحاويات */
.card-body,
.ad-card,
.favorite-card-wrapper {
    overflow: visible !important;
    position: relative;
}
</style>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/price-display.blade.php ENDPATH**/ ?>