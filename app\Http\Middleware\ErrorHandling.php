<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware لمعالجة الأخطاء بشكل محسن
 */
class ErrorHandling
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);
            
            // فحص حالة الاستجابة
            if ($response->getStatusCode() >= 500) {
                $this->logServerError($request, $response);
            }
            
            return $response;
        } catch (QueryException $e) {
            return $this->handleDatabaseError($request, $e);
        } catch (\Exception $e) {
            return $this->handleGeneralError($request, $e);
        }
    }

    /**
     * معالجة أخطاء قاعدة البيانات
     */
    private function handleDatabaseError(Request $request, QueryException $e)
    {
        Log::error('Database Error', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
            'error' => $e->getMessage(),
            'sql' => $e->getSql(),
            'bindings' => $e->getBindings(),
        ]);

        // التحقق من نوع الخطأ
        $errorCode = $e->getCode();
        $errorMessage = $e->getMessage();

        // أخطاء الاتصال
        if (str_contains($errorMessage, 'Connection refused') || 
            str_contains($errorMessage, 'No connection could be made')) {
            
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'خطأ في الاتصال بقاعدة البيانات',
                    'message' => 'نعتذر، هناك مشكلة مؤقتة في الخدمة. يرجى المحاولة لاحقاً.',
                    'code' => 'DB_CONNECTION_ERROR'
                ], 503);
            }

            return response()->view('errors.database-connection', [], 503);
        }

        // أخطاء SQL أخرى
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'خطأ في قاعدة البيانات',
                'message' => 'حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
                'code' => 'DB_ERROR'
            ], 500);
        }

        return response()->view('errors.database', [], 500);
    }

    /**
     * معالجة الأخطاء العامة
     */
    private function handleGeneralError(Request $request, \Exception $e)
    {
        Log::error('General Error', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'خطأ في الخادم',
                'message' => 'حدث خطأ غير متوقع. يرجى المحاولة لاحقاً.',
                'code' => 'GENERAL_ERROR'
            ], 500);
        }

        return response()->view('errors.general', [], 500);
    }

    /**
     * تسجيل أخطاء الخادم
     */
    private function logServerError(Request $request, Response $response)
    {
        Log::warning('Server Error Response', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'status_code' => $response->getStatusCode(),
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
        ]);
    }
}
