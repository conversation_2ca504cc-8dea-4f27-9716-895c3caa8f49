# دليل إعداد قاعدة البيانات المحسنة 🗄️

## نظرة عامة

تم تطوير هيكل قاعدة بيانات محسن ومنظم لموقع الإعلانات المبوبة، يتضمن:

- **7 جداول رئيسية** بدلاً من 15+ جدول متناثر
- **نظام تشفير متقدم** لحماية البيانات الحساسة
- **بيانات تجريبية شاملة** مناسبة للسوق اليمني والعربي
- **دعم عملات متعددة** (ريال يمني، ريال سعودي، دولار، درهم)
- **نظام تفاعلات موحد** (مفضلة، تقييمات، تعليقات، مشاهدات)

## 🚀 التشغيل السريع

### Windows:
```bash
reset_database.bat
```

### Linux/Mac:
```bash
chmod +x reset_database.sh
./reset_database.sh
```

### يدوياً:
```bash
php artisan db:wipe --force
php artisan migrate --force
php artisan db:seed --force
```

## 📊 البيانات المنشأة

### المستخدمين:
- **مدير النظام**: `<EMAIL>` (كلمة المرور تظهر عند التشغيل)
- **مستخدمين تجريبيين**: `<EMAIL>`, `<EMAIL>`, `<EMAIL>`
- **كلمة مرور المستخدمين التجريبيين**: `TestPass123!`

### التصنيفات (8 تصنيفات رئيسية):
1. 🚗 **السيارات والمركبات** - سيارات، دراجات، قطع غيار
2. 🏠 **العقارات** - شقق، فيلات، أراضي، مكاتب
3. 📱 **الإلكترونيات والأجهزة** - هواتف، حاسوب، أجهزة منزلية
4. 💼 **الوظائف والتوظيف** - وظائف حكومية وخاصة، عمل حر
5. 🔧 **الخدمات** - صيانة، تنظيف، تعليم، استشارات
6. 👗 **الأزياء والموضة** - ملابس، أحذية، إكسسوارات
7. 🛋️ **الأثاث والمنزل** - أثاث، ديكورات، أدوات مطبخ
8. 🏃‍♂️ **الرياضة واللياقة** - أجهزة رياضية، دراجات، ملابس رياضية

### الإعلانات (50+ إعلان متنوع):
- **إعلانات سيارات**: تويوتا، هيونداي، كيا (مدن يمنية وعربية)
- **إعلانات عقارات**: شقق وفيلات في صنعاء، عدن، تعز، الرياض
- **إعلانات إلكترونيات**: هواتف ذكية، لابتوب، أجهزة منزلية
- **إعلانات وظائف**: مبرمجين، مدرسين، خدمات مختلفة
- **إعلانات خدمات**: صيانة، تنظيف، ترجمة، تصوير

### أنواع الأسعار:
- **ثابت**: أسعار محددة غير قابلة للتفاوض
- **قابل للتفاوض**: أسعار يمكن التفاوض عليها
- **مجاني**: خدمات وسلع مجانية
- **حسب الطلب**: خدمات بأسعار متغيرة

### العملات المدعومة:
- **YER**: الريال اليمني 🇾🇪
- **SAR**: الريال السعودي 🇸🇦
- **USD**: الدولار الأمريكي 🇺🇸
- **AED**: الدرهم الإماراتي 🇦🇪
- **EGP**: الجنيه المصري 🇪🇬

## 🔒 الأمان والتشفير

### البيانات المشفرة:
- أرقام الهواتف
- عناوين البريد الإلكتروني
- أرقام الواتساب
- معلومات بطاقات الدفع

### إعدادات الخصوصية:
- إظهار/إخفاء رقم الهاتف
- إظهار/إخفاء البريد الإلكتروني
- إظهار/إخفاء رقم الواتساب
- السماح بكشف معلومات التواصل
- إظهار/إخفاء حالة الاتصال

## 🎨 الأيقونات الافتراضية

### عرض جميل للإعلانات:
- **بدلاً من المربعات الفارغة**: أيقونات FontAwesome جميلة ومناسبة لكل تصنيف
- **تصميم متجاوب**: يتكيف مع جميع أحجام الشاشات
- **تأثيرات تفاعلية**: حركات ناعمة عند التمرير
- **ألوان مميزة**: كل تصنيف له لون مميز

### أيقونات التصنيفات:
- 🚗 **السيارات**: أيقونة سيارة زرقاء
- 🏠 **العقارات**: أيقونة منزل خضراء
- 📱 **الإلكترونيات**: أيقونة هاتف بنفسجية
- 💼 **الوظائف**: أيقونة حقيبة برتقالية
- 🔧 **الخدمات**: أيقونة أدوات خضراء فاتحة
- 👗 **الأزياء**: أيقونة قميص وردية
- 🛋️ **الأثاث**: أيقونة أريكة رمادية
- 🏃‍♂️ **الرياضة**: أيقونة دمبل حمراء

## 🗂️ هيكل قاعدة البيانات

### الجداول الرئيسية:
1. **users** - المستخدمين مع التشفير والخصوصية
2. **categories** - التصنيفات الهرمية مع الأيقونات
3. **ads** - الإعلانات مع الأسعار المتقدمة
4. **interactions** - التفاعلات الموحدة (مفضلة، تقييمات، تعليقات)
5. **notifications** - الإشعارات الموحدة
6. **system_logs** - سجلات النظام الشاملة
7. **settings** - إعدادات النظام المرنة

## 🌍 التخصيص للسوق اليمني والعربي

### المدن المدعومة:
- **اليمن**: صنعاء، عدن، تعز، الحديدة، إب
- **السعودية**: الرياض، جدة، الدمام، مكة، المدينة
- **الإمارات**: دبي، أبوظبي، الشارقة
- **مصر**: القاهرة، الإسكندرية، الجيزة

### المحتوى المحلي:
- أسماء عربية أصيلة
- منتجات وخدمات مناسبة للمنطقة
- أسعار واقعية بالعملات المحلية
- مواقع وأحياء حقيقية

## 🔧 الصيانة والتطوير

### إضافة بيانات جديدة:
```php
// في DatabaseSeeder.php
$newAds = [
    [
        'title_ar' => 'عنوان الإعلان',
        'title_en' => 'Ad Title',
        'description_ar' => 'وصف مفصل...',
        'price' => 1000,
        'currency' => 'YER',
        'category_name' => 'اسم التصنيف',
        // ... باقي الحقول
    ]
];
```

### تحديث التصنيفات:
```php
// إضافة تصنيف جديد مع أيقونة
[
    'name_ar' => 'تصنيف جديد',
    'name_en' => 'New Category',
    'icon' => 'fas fa-icon-name',
    'subcategories' => [...]
]
```

## 📈 الإحصائيات والتقارير

النظام يتتبع تلقائياً:
- عدد المشاهدات لكل إعلان
- عدد مرات كشف معلومات التواصل
- عدد المفضلة والتقييمات
- سجلات تفصيلية لجميع العمليات

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ في التشفير**: تأكد من وجود APP_KEY في .env
2. **خطأ في قاعدة البيانات**: تحقق من إعدادات DB في .env
3. **مشاكل الصلاحيات**: تأكد من صلاحيات مجلد storage

### الحلول:
```bash
# إعادة إنشاء مفتاح التطبيق
php artisan key:generate

# مسح الكاش
php artisan cache:clear
php artisan config:clear

# إصلاح الصلاحيات
chmod -R 775 storage bootstrap/cache
```

---

**تم تطوير هذا النظام خصيصاً للسوق اليمني والعربي مع مراعاة الخصوصية والأمان** 🛡️
