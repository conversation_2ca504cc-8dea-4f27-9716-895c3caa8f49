<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware لحظر عناوين IP المشبوهة
 * يحظر عناوين IP التي تظهر سلوكاً مشبوهاً
 */
class IpBlocking
{
    /**
     * قائمة عناوين IP المحظورة
     */
    private array $blockedIps = [
        // يمكن إضافة عناوين IP محددة هنا
    ];

    /**
     * قائمة البلدان المحظورة (اختيارية)
     */
    private array $blockedCountries = [
        // يمكن إضافة رموز البلدان هنا إذا لزم الأمر
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // التحقق من تفعيل حظر IP
        if (!config('myadssite.security.enable_ip_blocking', false)) {
            return $next($request);
        }

        $clientIp = $this->getClientIp($request);

        // التحقق من القائمة السوداء
        if ($this->isIpBlocked($clientIp)) {
            Log::warning('Blocked IP attempt', [
                'ip' => $clientIp,
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
            ]);

            return response('Access Denied', 403);
        }

        // التحقق من السلوك المشبوه
        if ($this->isSuspiciousBehavior($request, $clientIp)) {
            $this->blockIpTemporarily($clientIp);
            
            Log::warning('Suspicious behavior detected', [
                'ip' => $clientIp,
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
            ]);

            return response('Too Many Requests', 429);
        }

        return $next($request);
    }

    /**
     * الحصول على عنوان IP الحقيقي للعميل
     */
    private function getClientIp(Request $request): string
    {
        // التحقق من headers مختلفة للحصول على IP الحقيقي
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancers
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipHeaders as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }

    /**
     * التحقق من حظر عنوان IP
     */
    private function isIpBlocked(string $ip): bool
    {
        // التحقق من القائمة الثابتة
        if (in_array($ip, $this->blockedIps)) {
            return true;
        }

        // التحقق من الحظر المؤقت
        if (Cache::has("blocked_ip:{$ip}")) {
            return true;
        }

        // التحقق من قاعدة البيانات (يمكن تطبيقها لاحقاً)
        // return DB::table('blocked_ips')->where('ip', $ip)->exists();

        return false;
    }

    /**
     * التحقق من السلوك المشبوه
     */
    private function isSuspiciousBehavior(Request $request, string $ip): bool
    {
        $key = "requests:{$ip}";
        $requests = Cache::get($key, 0);
        
        // زيادة عداد الطلبات
        Cache::put($key, $requests + 1, now()->addMinutes(5));

        // إذا تجاوز 100 طلب في 5 دقائق
        if ($requests > 100) {
            return true;
        }

        // التحقق من User Agent المشبوه
        $userAgent = $request->userAgent();
        $suspiciousAgents = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget'
        ];

        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                return true;
            }
        }

        // التحقق من طلبات مشبوهة للمسارات الحساسة
        $suspiciousPaths = [
            '/wp-admin', '/admin.php', '/.env', '/config.php',
            '/phpmyadmin', '/mysql', '/database'
        ];

        $currentPath = $request->getPathInfo();
        foreach ($suspiciousPaths as $path) {
            if (str_contains($currentPath, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * حظر عنوان IP مؤقتاً
     */
    private function blockIpTemporarily(string $ip, int $minutes = 60): void
    {
        Cache::put("blocked_ip:{$ip}", true, now()->addMinutes($minutes));
        
        Log::info("IP blocked temporarily", [
            'ip' => $ip,
            'duration_minutes' => $minutes,
        ]);
    }

    /**
     * إلغاء حظر عنوان IP
     */
    public static function unblockIp(string $ip): void
    {
        Cache::forget("blocked_ip:{$ip}");
        
        Log::info("IP unblocked", ['ip' => $ip]);
    }

    /**
     * الحصول على قائمة عناوين IP المحظورة
     */
    public static function getBlockedIps(): array
    {
        $keys = Cache::get('blocked_ip_keys', []);
        $blockedIps = [];

        foreach ($keys as $key) {
            if (Cache::has($key)) {
                $ip = str_replace('blocked_ip:', '', $key);
                $blockedIps[] = $ip;
            }
        }

        return $blockedIps;
    }
}
