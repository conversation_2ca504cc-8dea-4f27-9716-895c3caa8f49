<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;
use App\Models\Announcement;
use App\Models\Ad;
use App\Services\InputSanitizationService;

/**
 * كونترولر الإعدادات العامة للموقع
 * يدير جميع إعدادات الموقع من مكان واحد
 */
class SettingsController extends Controller
{
    /**
     * عرض صفحة الإعدادات العامة
     */
    public function index()
    {
        // جمع إحصائيات سريعة للعرض
        $stats = [
            'announcements' => [
                'total' => Announcement::count(),
                'active' => Announcement::active()->count(),
                'settings' => Setting::byGroup('announcements')->count()
            ],
            // إحصائيات التصنيفات والإعلانات
            'categories' => [
                'total' => \App\Models\Category::count(),
                'active' => \App\Models\Category::active()->count()
            ],
            'ads' => [
                'total' => Ad::count(),
                'active' => Ad::active()->count(),
                'pending' => Ad::pending()->count(),
                'settings' => Setting::byGroup('ads')->count()
            ]
        ];

        return view('admin.settings.index', compact('stats'));
    }

    /**
     * عرض إعدادات قسم معين
     */
    public function section($section)
    {
        // تنظيف وفحص المدخل لحماية من XSS
        $section = InputSanitizationService::sanitizeText($section);

        // قائمة الأقسام المسموحة
        $allowedSections = ['announcements', 'ads', 'general', 'appearance', 'users'];

        if (!in_array($section, $allowedSections)) {
            return redirect()->route('admin.settings.index')
                ->with('error', 'القسم المطلوب غير موجود');
        }

        switch ($section) {
            case 'announcements':
                return redirect()->route('admin.announcements.index');

            case 'ads':
                return $this->adSettings();

            case 'general':
                return $this->generalSettings();

            case 'appearance':
                return $this->appearanceSettings();

            case 'users':
                return $this->userSettings();

            default:
                return redirect()->route('admin.settings.index')
                    ->with('error', 'القسم المطلوب غير موجود');
        }
    }

    /**
     * إعدادات عامة للموقع
     */
    private function generalSettings()
    {
        return view('admin.settings.general');
    }

    /**
     * إعدادات المظهر
     */
    private function appearanceSettings()
    {
        return view('admin.settings.appearance');
    }

    /**
     * إعدادات الإعلانات
     */
    private function adSettings()
    {
        $settings = Setting::byGroup('ads')->get()->groupBy('group');
        return view('admin.settings.ads', compact('settings'));
    }

    /**
     * إعدادات المستخدمين
     */
    private function userSettings()
    {
        return view('admin.settings.users');
    }

    /**
     * تحديث إعدادات الإعلانات
     */
    public function updateAdSettings(Request $request)
    {
        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'nullable|string|max:1000'
        ]);

        // تنظيف البيانات المدخلة لحماية من XSS
        $validated = InputSanitizationService::sanitizeFormData($validated);

        foreach ($validated['settings'] as $key => $value) {
            // تنظيف مفتاح الإعداد
            $key = InputSanitizationService::sanitizeText($key);

            $setting = Setting::where('key', $key)->first();
            if ($setting) {
                // تحويل القيم حسب النوع
                if ($setting->type === 'boolean') {
                    $value = $request->has("settings.{$key}") ? '1' : '0';
                } else {
                    // تنظيف القيمة
                    $value = InputSanitizationService::sanitizeText($value);
                }

                $setting->update(['value' => $value]);
            }
        }

        return redirect()->back()->with('success', __('تم تحديث إعدادات الإعلانات بنجاح'));
    }
}
