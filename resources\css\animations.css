/* ===== الرسوم المتحركة المحسنة والمبسطة ===== */
/*
 * ملف محسن للرسوم المتحركة - تم تبسيطه وتنظيمه
 * يحتوي على جميع التأثيرات الأساسية المطلوبة
 * محسن للأداء ومنظم في أقسام منطقية
 */

/* ===== الرسوم المتحركة الأساسية ===== */

/* تأثيرات الظهور الأساسية */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

@keyframes slideInDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* تأثيرات التفاعل الأساسية */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== فئات الرسوم المتحركة المحسنة ===== */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.8s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.6s ease-out;
}

.animate-zoom-in {
    animation: zoomIn 0.6s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* تأثيرات التحويم */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.5);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* تأثيرات الأزرار */
.btn-animated {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animated:hover::before {
    left: 100%;
}

.btn-ripple {
    position: relative;
    overflow: hidden;
}

.btn-ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::after {
    width: 300px;
    height: 300px;
}

/* تأثيرات البطاقات */
.card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* تأثير card-flip محذوف لتحسين الأداء - كان معقد ومستهلك للموارد */

/* تأثيرات النصوص */
.text-gradient {
    background: linear-gradient(45deg, #007bff, #6f42c1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.text-glow {
    text-shadow: 0 0 10px rgba(0, 123, 255, 0.8);
}

/* تأثيرات التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* تأثيرات الشريط المتحرك */
.progress-animated .progress-bar {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* تأثيرات الظلال */
.shadow-soft {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.shadow-strong {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.shadow-colored {
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* تأثيرات الحدود */
.border-animated {
    position: relative;
    border: 2px solid transparent;
}

.border-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid #007bff;
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.border-animated:hover::before {
    opacity: 1;
}

/* تأثيرات الخلفية */
.bg-gradient {
    background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
}

/* bg-animated محذوف لتحسين الأداء - كان يستهلك موارد كثيرة */

/* تأثيرات الانتقال السلس */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-transition-slow {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات التركيز */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* تأثيرات الاختفاء */
.fade-out {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.slide-out-up {
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.slide-out-down {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}
