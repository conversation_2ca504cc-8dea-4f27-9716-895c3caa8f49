/*
 * ملف CSS متقدم لمكون معلومات صاحب الإعلان
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على أنماط متقدمة لعرض معلومات المستخدم وإحصائياته
 */

/* ===== متغيرات الألوان لمعلومات المستخدم ===== */
:root {
    --advertiser-primary: #6366f1;
    --advertiser-secondary: #8b5cf6;
    --advertiser-success: #10b981;
    --advertiser-warning: #f59e0b;
    --advertiser-danger: #ef4444;
    --advertiser-info: #06b6d4;
    --advertiser-muted: #6b7280;
    --advertiser-border: rgba(99, 102, 241, 0.2);
    --advertiser-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --advertiser-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== قسم معلومات المستخدم الرئيسي ===== */
.advertiser-section {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
    border: 1px solid var(--advertiser-border);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.advertiser-detailed {
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    box-shadow: var(--advertiser-shadow);
}

.advertiser-compact {
    padding: 0.75rem;
}

.advertiser-section:hover {
    border-color: var(--advertiser-primary);
    box-shadow: var(--advertiser-shadow-hover);
}

/* ===== ملف المستخدم الشخصي ===== */
.advertiser-profile {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

/* ===== صورة المستخدم ===== */
.advertiser-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--advertiser-primary);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--advertiser-primary) 0%, var(--advertiser-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* ===== شارات المستخدم ===== */
.user-badges {
    position: absolute;
    bottom: -5px;
    right: -5px;
    display: flex;
    gap: 2px;
}

.badge-verified,
.badge-admin,
.badge-premium {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: white;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.badge-verified {
    background: var(--advertiser-success);
}

.badge-admin {
    background: var(--advertiser-warning);
}

.badge-premium {
    background: var(--advertiser-secondary);
}

/* ===== تفاصيل المستخدم ===== */
.advertiser-details {
    flex: 1;
    min-width: 0;
}

.advertiser-name .name-text {
    font-weight: 700;
    color: var(--advertiser-primary);
    margin: 0;
    font-size: 1.1rem;
}

.advertiser-name .company-name {
    font-size: 0.9rem;
    color: var(--advertiser-muted);
    margin: 0.25rem 0 0 0;
    font-style: italic;
}

.join-date,
.last-seen {
    display: flex;
    align-items: center;
    margin-top: 0.25rem;
}

.join-date small,
.last-seen small {
    font-size: 0.8rem;
    color: var(--advertiser-muted);
}

/* ===== إحصائيات المستخدم ===== */
.advertiser-stats {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(99, 102, 241, 0.03);
    border: 1px solid rgba(99, 102, 241, 0.1);
    border-radius: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(99, 102, 241, 0.1);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--advertiser-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
}

.stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(99, 102, 241, 0.1);
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--advertiser-primary);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--advertiser-muted);
}

/* ===== تقييم سريع ===== */
.quick-rating {
    text-align: center;
    padding: 0.75rem;
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.1);
    border-radius: 8px;
}

.rating-stars {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.rating-stars i {
    font-size: 1rem;
}

.rating-text {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    color: var(--advertiser-muted);
}

/* ===== أزرار التفاعل ===== */
.advertiser-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(99, 102, 241, 0.1);
}

.advertiser-actions .btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.advertiser-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* ===== مؤشرات الثقة ===== */
.trust-indicators {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(16, 185, 129, 0.03);
    border: 1px solid rgba(16, 185, 129, 0.1);
    border-radius: 8px;
}

.trust-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.trust-item {
    display: flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 4px;
    font-size: 0.75rem;
}

.trust-item i {
    margin-right: 0.25rem;
}

/* ===== مؤشر الثقة ===== */
.trust-score {
    margin-top: 0.5rem;
}

.trust-score-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.trust-score-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--advertiser-warning) 0%, var(--advertiser-success) 100%);
    border-radius: 3px;
    transition: width 0.5s ease;
}

.trust-score-text {
    display: block;
    text-align: center;
    margin-top: 0.25rem;
    font-size: 0.7rem;
    color: var(--advertiser-muted);
    font-weight: 600;
}

/* ===== الاستجابة للأجهزة المحمولة ===== */
@media (max-width: 768px) {
    .advertiser-detailed {
        padding: 1rem;
    }
    
    .advertiser-profile {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 0.75rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
        padding: 0.75rem 0.5rem;
    }
    
    .stat-icon {
        margin-bottom: 0.25rem;
    }
    
    .advertiser-actions {
        flex-direction: column;
    }
    
    .advertiser-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .trust-items {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .advertiser-section {
        padding: 0.75rem;
    }
    
    .avatar-image,
    .avatar-placeholder {
        width: 50px;
        height: 50px;
    }
    
    .user-badges {
        bottom: -3px;
        right: -3px;
    }
    
    .badge-verified,
    .badge-admin,
    .badge-premium {
        width: 16px;
        height: 16px;
        font-size: 0.6rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .advertiser-name .name-text {
        font-size: 1rem;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .advertiser-section {
        background: white;
        border: 1px solid #ccc;
        box-shadow: none;
    }
    
    .advertiser-actions {
        display: none;
    }
    
    .trust-score-bar {
        border: 1px solid #ddd;
    }
    
    .trust-score-fill {
        background: #666 !important;
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.advertiser-actions .btn:focus {
    outline: 2px solid currentColor;
    outline-offset: 2px;
}

.stat-item:focus-within {
    outline: 2px solid var(--advertiser-primary);
    outline-offset: 2px;
}

/* ===== تأثيرات الحركة ===== */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.advertiser-section {
    animation: slideInLeft 0.6s ease-out;
}

.stat-item {
    animation: slideInLeft 0.8s ease-out;
}

/* ===== تحسينات الوضع المظلم (معطل مؤقتاً) ===== */
/* تم إلغاء تفعيل الوضع المظلم مؤقتاً - سيتم تفعيله لاحقاً عند الطلب */
/*
@media (prefers-color-scheme: dark) {
    .advertiser-section {
        background: rgba(99, 102, 241, 0.1);
        border-color: rgba(99, 102, 241, 0.3);
    }

    .advertiser-detailed {
        background: rgba(0, 0, 0, 0.3);
    }

    .stat-item {
        background: rgba(0, 0, 0, 0.3);
        border-color: rgba(99, 102, 241, 0.3);
    }

    .advertiser-name .name-text {
        color: #e5e7eb;
    }

    .trust-score-bar {
        background: rgba(255, 255, 255, 0.2);
    }
}
*/
