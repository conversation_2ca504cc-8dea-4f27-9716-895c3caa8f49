<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ad;
use Illuminate\View\View;

/**
 * كونترولر مقارنة الإعلانات
 * يدير صفحة مقارنة الإعلانات
 */
class CompareController extends Controller
{
    /**
     * عرض صفحة المقارنة
     */
    public function index(Request $request): View
    {
        $idsString = $request->get('ids', '');
        $ids = array_filter(explode(',', $idsString));

        if (empty($ids)) {
            return view('compare.empty');
        }

        // التحقق من الحد الأقصى
        if (count($ids) > 4) {
            $ids = array_slice($ids, 0, 4);
        }

        // جلب الإعلانات
        $ads = Ad::whereIn('id', $ids)
                ->active()
                ->notExpired()
                ->with(['category', 'user', 'ratings'])
                ->get();

        if ($ads->isEmpty()) {
            return view('compare.empty');
        }

        // ترتيب الإعلانات حسب الترتيب المطلوب
        $orderedAds = collect();
        foreach ($ids as $id) {
            $ad = $ads->firstWhere('id', $id);
            if ($ad) {
                $orderedAds->push($ad);
            }
        }

        return view('compare.index', [
            'ads' => $orderedAds,
            'comparisonData' => $this->prepareComparisonData($orderedAds)
        ]);
    }

    /**
     * تحضير بيانات المقارنة
     */
    private function prepareComparisonData($ads)
    {
        $data = [
            'basic_info' => [],
            'pricing' => [],
            'ratings' => [],
            'features' => [],
            'contact' => []
        ];

        foreach ($ads as $ad) {
            // المعلومات الأساسية
            $data['basic_info'][] = [
                'title' => $ad->title,
                'category' => $ad->category->name ?? __('Uncategorized'),
                'location' => $ad->location ?? __('Not specified'),
                'published_date' => $ad->created_at->format('Y-m-d'),
                'views' => $ad->views_count ?? 0,
                'expires_at' => $ad->expires_at ? $ad->expires_at->format('Y-m-d') : __('No expiry')
            ];

            // الأسعار
            $data['pricing'][] = [
                'price' => $ad->price ?? __('Not specified'),
                'original_price' => $ad->original_price,
                'discount_percentage' => $ad->discount_percentage,
                'is_negotiable' => $ad->is_negotiable,
                'is_free' => $ad->is_free,
                'currency' => $ad->currency ?? 'USD'
            ];

            // التقييمات
            $data['ratings'][] = [
                'average_rating' => $ad->average_rating,
                'ratings_count' => $ad->ratings_count,
                'rating_distribution' => $ad->rating_distribution
            ];

            // الميزات (يمكن توسيعها لاحقاً)
            $data['features'][] = [
                'is_featured' => $ad->is_featured,
                'has_images' => !empty($ad->image),
                'description_length' => strlen($ad->description ?? ''),
                'has_contact_info' => !empty($ad->phone) || !empty($ad->email)
            ];

            // معلومات التواصل
            $data['contact'][] = [
                'phone' => $ad->phone,
                'email' => $ad->email,
                'user_name' => $ad->user->name ?? __('Unknown'),
                'user_joined' => $ad->user->created_at ?? null
            ];
        }

        return $data;
    }
}
