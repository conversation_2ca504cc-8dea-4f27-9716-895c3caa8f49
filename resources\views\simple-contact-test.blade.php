@extends('layouts.app')

@section('title', 'اختبار بسيط لمعلومات التواصل')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/components/contact-management.css') }}">
@endpush

@section('content')
<div class="container mt-5">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-address-book me-2"></i>اختبار بسيط لمعلومات التواصل</h3>
                    <p class="mb-0">المستخدم: {{ Auth::user()->name }} ({{ Auth::user()->email }})</p>
                </div>
                <div class="card-body">
                    <!-- أزرار الاختبار -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>اختبار الأزرار</h5>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" onclick="testShowModal()">
                                    <i class="fas fa-plus me-2"></i>
                                    اختبار فتح Modal
                                </button>
                                <button type="button" class="btn btn-success" onclick="testLoadContacts()">
                                    <i class="fas fa-download me-2"></i>
                                    اختبار تحميل معلومات التواصل
                                </button>
                                <button type="button" class="btn btn-info" onclick="testBootstrap()">
                                    <i class="fas fa-cog me-2"></i>
                                    اختبار Bootstrap
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>معلومات التواصل الحالية</h5>
                            <div id="contacts-display" class="border p-3 bg-light">
                                <p class="text-muted">جاري التحميل...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- منطقة النتائج -->
                    <div class="mt-4">
                        <h5>سجل الأحداث:</h5>
                        <div id="log-output" class="border p-3 bg-dark text-light" style="height: 300px; overflow-y: auto; font-family: monospace;">
                            <div class="text-success">[جاهز] صفحة الاختبار محملة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal معلومات التواصل -->
<div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactModalTitle">إضافة طريقة تواصل جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="contactForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contact_type" class="form-label">نوع التواصل</label>
                            <select class="form-select" id="contact_type" name="contact_type">
                                <option value="">اختر نوع التواصل</option>
                                <option value="phone">هاتف</option>
                                <option value="whatsapp">واتساب</option>
                                <option value="email">إيميل</option>
                                <option value="telegram">تليجرام</option>
                                <option value="instagram">إنستجرام</option>
                                <option value="facebook">فيسبوك</option>
                                <option value="twitter">تويتر</option>
                                <option value="linkedin">لينكد إن</option>
                                <option value="website">موقع إلكتروني</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contact_value" class="form-label">قيمة التواصل *</label>
                            <input type="text" class="form-control" id="contact_value" name="contact_value" 
                                   placeholder="مثال: +967771234567" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="display_label" class="form-label">تسمية مخصصة</label>
                            <input type="text" class="form-control" id="display_label" name="display_label" 
                                   placeholder="مثال: هاتف العمل">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="privacy_level" class="form-label">مستوى الخصوصية</label>
                            <select class="form-select" id="privacy_level" name="privacy_level" required>
                                <option value="public">عام للجميع</option>
                                <option value="registered_users">للمستخدمين المسجلين فقط</option>
                                <option value="private">خاص (لا يظهر)</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_primary" name="is_primary">
                                <label class="form-check-label" for="is_primary">
                                    طريقة التواصل الأساسية من هذا النوع
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_public" name="is_public" checked>
                                <label class="form-check-label" for="is_public">
                                    إظهار في الإعلانات
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="testSaveContact()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const logOutput = document.getElementById('log-output');
    const contactsDisplay = document.getElementById('contacts-display');
    
    function log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            'error': 'text-danger',
            'success': 'text-success',
            'warning': 'text-warning',
            'info': 'text-info'
        };
        const color = colors[type] || 'text-light';
        logOutput.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
        logOutput.scrollTop = logOutput.scrollHeight;
    }
    
    // فحص البيئة
    log('🔍 فحص البيئة...', 'info');
    log(`✅ jQuery: ${typeof $ !== 'undefined' ? 'متوفر' : 'غير متوفر'}`, typeof $ !== 'undefined' ? 'success' : 'error');
    log(`✅ Bootstrap: ${typeof bootstrap !== 'undefined' ? 'متوفر' : 'غير متوفر'}`, typeof bootstrap !== 'undefined' ? 'success' : 'error');
    log(`✅ Bootstrap.Modal: ${typeof bootstrap !== 'undefined' && bootstrap.Modal ? 'متوفر' : 'غير متوفر'}`, typeof bootstrap !== 'undefined' && bootstrap.Modal ? 'success' : 'error');
    
    // فحص العناصر
    const elements = {
        'contactModal': document.getElementById('contactModal'),
        'contactForm': document.getElementById('contactForm'),
        'contactModalTitle': document.getElementById('contactModalTitle'),
        'contact_type': document.getElementById('contact_type'),
        'contact_value': document.getElementById('contact_value')
    };
    
    Object.entries(elements).forEach(([name, element]) => {
        log(`✅ ${name}: ${element ? 'موجود' : 'غير موجود'}`, element ? 'success' : 'error');
    });
    
    // تحميل معلومات التواصل
    loadContacts();
    
    // تعريف الوظائف
    window.testShowModal = function() {
        log('🔄 اختبار فتح Modal...', 'info');
        
        try {
            const modal = new bootstrap.Modal(document.getElementById('contactModal'));
            modal.show();
            log('✅ تم فتح Modal بنجاح', 'success');
        } catch (error) {
            log(`❌ خطأ في فتح Modal: ${error.message}`, 'error');
        }
    };
    
    window.testLoadContacts = function() {
        log('🔄 اختبار تحميل معلومات التواصل...', 'info');
        loadContacts();
    };
    
    window.testBootstrap = function() {
        log('🔄 اختبار Bootstrap...', 'info');
        
        if (typeof bootstrap === 'undefined') {
            log('❌ Bootstrap غير محمل', 'error');
            return;
        }
        
        try {
            // إنشاء modal اختبار
            const testModal = document.createElement('div');
            testModal.className = 'modal fade';
            testModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">اختبار Bootstrap</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>✅ Bootstrap يعمل بشكل صحيح!</p>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(testModal);
            const modal = new bootstrap.Modal(testModal);
            modal.show();
            
            // إزالة Modal بعد 3 ثوان
            setTimeout(() => {
                modal.hide();
                setTimeout(() => document.body.removeChild(testModal), 500);
            }, 3000);
            
            log('✅ Bootstrap يعمل بشكل صحيح', 'success');
        } catch (error) {
            log(`❌ خطأ في Bootstrap: ${error.message}`, 'error');
        }
    };
    
    window.testSaveContact = function() {
        log('🔄 اختبار حفظ معلومة التواصل...', 'info');
        
        const form = document.getElementById('contactForm');
        const formData = new FormData(form);
        
        const data = {};
        formData.forEach((value, key) => {
            if (key === 'is_primary' || key === 'is_public') {
                data[key] = form.querySelector(`[name="${key}"]`).checked;
            } else {
                data[key] = value;
            }
        });
        
        log('📤 البيانات: ' + JSON.stringify(data), 'info');
        
        fetch('/dashboard/contacts', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            log(`📡 حالة الاستجابة: ${response.status}`, response.ok ? 'success' : 'error');
            return response.json();
        })
        .then(data => {
            if (data.success) {
                log('✅ تم حفظ معلومة التواصل بنجاح', 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('contactModal'));
                if (modal) modal.hide();
                loadContacts();
            } else {
                log(`❌ فشل الحفظ: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
        });
    };
    
    function loadContacts() {
        log('🔄 تحميل معلومات التواصل...', 'info');
        
        fetch('/dashboard/contacts', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            log(`📡 حالة الاستجابة: ${response.status}`, response.ok ? 'success' : 'error');
            return response.json();
        })
        .then(data => {
            if (data.success) {
                log(`✅ تم تحميل ${data.data.length} معلومة تواصل`, 'success');
                
                if (data.data.length > 0) {
                    const html = data.data.map(contact => `
                        <div class="mb-2 p-2 border rounded">
                            <strong>${contact.display_label || contact.contact_type}</strong><br>
                            <small>${contact.contact_value}</small>
                        </div>
                    `).join('');
                    contactsDisplay.innerHTML = html;
                } else {
                    contactsDisplay.innerHTML = '<p class="text-muted">لا توجد معلومات تواصل</p>';
                }
            } else {
                log(`❌ فشل التحميل: ${data.message}`, 'error');
                contactsDisplay.innerHTML = '<p class="text-danger">فشل في تحميل معلومات التواصل</p>';
            }
        })
        .catch(error => {
            log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            contactsDisplay.innerHTML = '<p class="text-danger">خطأ في الشبكة</p>';
        });
    }
});
</script>
@endsection
