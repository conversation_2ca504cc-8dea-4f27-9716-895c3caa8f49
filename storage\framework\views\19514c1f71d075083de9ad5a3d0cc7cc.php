
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['ad', 'detailed' => false, 'position' => 'overlay']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['ad', 'detailed' => false, 'position' => 'overlay']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>


<?php
    // تحديد الشارات المؤهلة للعرض حسب الأولوية
    $priorityBadges = [];

    // 1. شارات الحالة الحرجة (أولوية عالية)
    if ($ad->status === 'rejected') {
        $priorityBadges['critical'] = 'rejected';
    } elseif ($ad->status === 'expired') {
        $priorityBadges['critical'] = 'expired';
    } elseif ($ad->status === 'pending') {
        $priorityBadges['critical'] = 'pending';
    }

    // 2. شارات الميزات المميزة (أولوية متوسطة-عالية)
    if ($ad->is_featured ?? false) {
        $priorityBadges['featured'] = true;
    }

    // 3. شارات الأسعار (مبسطة)
    if ($ad->is_free) {
        $priorityBadges['free'] = true;
    }

    // إزالة شارة "قابل للتفاوض" - غير ضرورية

    // إزالة شارات التوقيت والتحذيرات لتبسيط التصميم

    // 6. شارات الإنجاز
    $achievements = [];
    if ($ad->created_at->diffInDays(now()) <= 3) {
        $achievements['new'] = true;
    }
    if ($ad->views_count > 1000) {
        $achievements['popular'] = true;
    }

    // تسجيل البيانات للمراجعة (في الوضع المفصل فقط)
    if ($detailed) {
        Log::info('عرض نظام الشارات المبسّط', [
            'ad_id' => $ad->id,
            'priority_badges' => $priorityBadges,
            'achievements' => $achievements,
            'user_id' => auth()->id(),
            'timestamp' => now()
        ]);
    }
?>

<div class="badges-container <?php echo e($position === 'inline' ? 'badges-inline' : 'badges-overlay'); ?> <?php echo e($detailed ? 'badges-detailed' : 'badges-compact'); ?>">

    
    <?php if(isset($priorityBadges['critical'])): ?>
        <div class="critical-badges-group">
            <?php if($priorityBadges['critical'] === 'rejected'): ?>
                <div class="status-badge rejected-badge" title="تم رفض الإعلان">
                    <i class="fas fa-times-circle me-1"></i>
                    <span class="arabic-text"><?php echo e(__('Rejected')); ?></span>
                </div>
            <?php elseif($priorityBadges['critical'] === 'expired'): ?>
                <div class="status-badge expired-badge" title="انتهت صلاحية الإعلان">
                    <i class="fas fa-calendar-times me-1"></i>
                    <span class="arabic-text"><?php echo e(__('Expired')); ?></span>
                </div>
            <?php elseif($priorityBadges['critical'] === 'pending'): ?>
                <div class="status-badge pending-badge" title="في انتظار مراجعة الإدارة">
                    <i class="fas fa-hourglass-half me-1"></i>
                    <span class="arabic-text"><?php echo e(__('Under Review')); ?></span>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    
    <?php if(isset($priorityBadges['featured'])): ?>
        <div class="featured-badges-group">
            <div class="status-badge featured-badge" title="إعلان مميز ومدفوع">
                <i class="fas fa-crown me-1"></i>
                <span class="arabic-text"><?php echo e(__('Featured')); ?></span>
            </div>
        </div>
    <?php endif; ?>

    
    <div class="price-badges-group">
        <?php if(isset($priorityBadges['free'])): ?>
            <div class="feature-badge free-badge" title="مجاني تماماً">
                <i class="fas fa-gift me-1"></i>
                <span class="arabic-text"><?php echo e(__('Free')); ?></span>
            </div>
        <?php endif; ?>
    </div>

    

    
    

    
    <?php if(!empty($achievements) && !isset($priorityBadges['critical'])): ?>
        <div class="achievement-badges-group">
            <?php if(isset($achievements['new'])): ?>
                <div class="achievement-badge new-badge" title="إعلان جديد منشور خلال آخر 3 أيام">
                    <i class="fas fa-star me-1"></i>
                    <span class="arabic-text"><?php echo e(__('New')); ?></span>
                </div>
            <?php endif; ?>

            <?php if(isset($achievements['popular']) && $detailed): ?>
                <div class="achievement-badge popular-badge" title="إعلان شائع - <?php echo e(number_format($ad->views_count)); ?> مشاهدة">
                    <i class="fas fa-trending-up me-1"></i>
                    <span class="arabic-text"><?php echo e(__('Trending')); ?></span>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>





<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/partials/badges.blade.php ENDPATH**/ ?>