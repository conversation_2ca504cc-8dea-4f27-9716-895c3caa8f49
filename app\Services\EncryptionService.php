<?php

namespace App\Services;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

/**
 * خدمة تشفير البيانات الحساسة
 * تدير تشفير وفك تشفير البيانات الحساسة مثل أرقام الهواتف والبريد الإلكتروني
 */
class EncryptionService
{
    /**
     * تشفير رقم الهاتف
     */
    public static function encryptPhone(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        try {
            // تنظيف رقم الهاتف من المسافات والرموز
            $cleanPhone = preg_replace('/[^\d+]/', '', $phone);
            
            // تشفير الرقم
            $encrypted = Crypt::encryptString($cleanPhone);
            
            Log::info('Phone number encrypted successfully');
            return $encrypted;
            
        } catch (\Exception $e) {
            Log::error('Failed to encrypt phone number', [
                'error' => $e->getMessage()
            ]);
            return $phone; // إرجاع الرقم الأصلي في حالة فشل التشفير
        }
    }

    /**
     * فك تشفير رقم الهاتف
     */
    public static function decryptPhone(?string $encryptedPhone): ?string
    {
        if (empty($encryptedPhone)) {
            return null;
        }

        try {
            // محاولة فك التشفير
            $decrypted = Crypt::decryptString($encryptedPhone);
            return $decrypted;

        } catch (\Exception $e) {
            // تسجيل تحذير مرة واحدة فقط لتجنب امتلاء السجل
            if (!cache()->has('decrypt_phone_warning_logged')) {
                Log::warning('Failed to decrypt phone number, returning masked value', [
                    'error' => $e->getMessage(),
                    'note' => 'This warning will be logged once per hour'
                ]);
                cache()->put('decrypt_phone_warning_logged', true, 3600); // ساعة واحدة
            }

            // إرجاع رقم مقنع بدلاً من القيمة المشفرة
            return '***-***-****';
        }
    }

    /**
     * تشفير البريد الإلكتروني
     */
    public static function encryptEmail(?string $email): ?string
    {
        if (empty($email)) {
            return null;
        }

        try {
            // التحقق من صحة البريد الإلكتروني
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                Log::warning('Invalid email format provided for encryption', ['email' => $email]);
                return $email;
            }

            // تشفير البريد الإلكتروني
            $encrypted = Crypt::encryptString(strtolower($email));
            
            Log::info('Email encrypted successfully');
            return $encrypted;
            
        } catch (\Exception $e) {
            Log::error('Failed to encrypt email', [
                'error' => $e->getMessage()
            ]);
            return $email; // إرجاع البريد الأصلي في حالة فشل التشفير
        }
    }

    /**
     * فك تشفير البريد الإلكتروني
     */
    public static function decryptEmail(?string $encryptedEmail): ?string
    {
        if (empty($encryptedEmail)) {
            return null;
        }

        try {
            // محاولة فك التشفير
            $decrypted = Crypt::decryptString($encryptedEmail);
            return $decrypted;

        } catch (\Exception $e) {
            // تسجيل تحذير مرة واحدة فقط لتجنب امتلاء السجل
            if (!cache()->has('decrypt_email_warning_logged')) {
                Log::warning('Failed to decrypt email, returning masked value', [
                    'error' => $e->getMessage(),
                    'note' => 'This warning will be logged once per hour'
                ]);
                cache()->put('decrypt_email_warning_logged', true, 3600); // ساعة واحدة
            }

            // إرجاع إيميل مقنع بدلاً من القيمة المشفرة
            return '***@***.***';
        }
    }

    /**
     * تشفير نص عام
     */
    public static function encryptText(?string $text): ?string
    {
        if (empty($text)) {
            return null;
        }

        try {
            return Crypt::encryptString($text);
        } catch (\Exception $e) {
            Log::error('Failed to encrypt text', [
                'error' => $e->getMessage()
            ]);
            return $text;
        }
    }

    /**
     * فك تشفير نص عام
     */
    public static function decryptText(?string $encryptedText): ?string
    {
        if (empty($encryptedText)) {
            return null;
        }

        try {
            return Crypt::decryptString($encryptedText);
        } catch (\Exception $e) {
            // تسجيل تحذير مرة واحدة فقط لتجنب امتلاء السجل
            if (!cache()->has('decrypt_text_warning_logged')) {
                Log::warning('Failed to decrypt text, returning masked value', [
                    'error' => $e->getMessage(),
                    'note' => 'This warning will be logged once per hour'
                ]);
                cache()->put('decrypt_text_warning_logged', true, 3600); // ساعة واحدة
            }

            // إرجاع نص مقنع بدلاً من القيمة المشفرة
            return '***';
        }
    }

    /**
     * إخفاء رقم الهاتف للعرض (إظهار آخر 4 أرقام فقط)
     */
    public static function maskPhone(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // فك التشفير أولاً إذا كان مشفراً
        $decryptedPhone = self::decryptPhone($phone);
        
        if (strlen($decryptedPhone) <= 4) {
            return str_repeat('*', strlen($decryptedPhone));
        }

        $visiblePart = substr($decryptedPhone, -4);
        $hiddenPart = str_repeat('*', strlen($decryptedPhone) - 4);
        
        return $hiddenPart . $visiblePart;
    }

    /**
     * إخفاء البريد الإلكتروني للعرض
     */
    public static function maskEmail(?string $email): ?string
    {
        if (empty($email)) {
            return null;
        }

        // فك التشفير أولاً إذا كان مشفراً
        $decryptedEmail = self::decryptEmail($email);
        
        if (!str_contains($decryptedEmail, '@')) {
            return str_repeat('*', strlen($decryptedEmail));
        }

        [$username, $domain] = explode('@', $decryptedEmail, 2);
        
        if (strlen($username) <= 2) {
            $maskedUsername = str_repeat('*', strlen($username));
        } else {
            $maskedUsername = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
        }
        
        return $maskedUsername . '@' . $domain;
    }

    /**
     * التحقق من أن النص مشفر
     */
    public static function isEncrypted(?string $text): bool
    {
        if (empty($text)) {
            return false;
        }

        try {
            Crypt::decryptString($text);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * تشفير مجموعة من البيانات
     */
    public static function encryptArray(array $data, array $fieldsToEncrypt): array
    {
        foreach ($fieldsToEncrypt as $field) {
            if (isset($data[$field])) {
                $data[$field] = self::encryptText($data[$field]);
            }
        }

        return $data;
    }

    /**
     * فك تشفير مجموعة من البيانات
     */
    public static function decryptArray(array $data, array $fieldsToDecrypt): array
    {
        foreach ($fieldsToDecrypt as $field) {
            if (isset($data[$field])) {
                $data[$field] = self::decryptText($data[$field]);
            }
        }

        return $data;
    }

    /**
     * إنشاء hash آمن للبيانات الحساسة (للبحث)
     */
    public static function createSearchableHash(?string $data): ?string
    {
        if (empty($data)) {
            return null;
        }

        // استخدام hash مع salt للبحث الآمن
        return hash('sha256', config('app.key') . strtolower($data));
    }

    /**
     * التحقق من تطابق البيانات مع الـ hash
     */
    public static function verifyHash(?string $data, ?string $hash): bool
    {
        if (empty($data) || empty($hash)) {
            return false;
        }

        return hash_equals($hash, self::createSearchableHash($data));
    }
}
